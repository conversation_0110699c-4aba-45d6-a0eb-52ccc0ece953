using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using Microsoft.EntityFrameworkCore;
using System.Linq;
{{#if Dependencies}}{{#each Dependencies}}using {{this}};
{{/each}}{{/if}}
namespace {{Namespace}}
{
    public class {{EntityName}}Service: I{{EntityName}}Service
    {
        private readonly IMapper _mapper;
        private readonly IRepositoryFactory _repositoryFactory;
        private readonly IDistributedCacheServiceFactory _cacheServiceFactory;

        public {{EntityName}}Service(IRepositoryFactory repositoryFactory, IDistributedCacheServiceFactory cacheServiceFactory, IMapper mapper)
        {
            _mapper = mapper;
            _repositoryFactory = repositoryFactory;
            _cacheServiceFactory = cacheServiceFactory;
        }

        public async Task<IEnumerable<{{EntityName}}Model>> GetAllAsync()
        {
            var {{EntityNameCamelCase}}Cache = _cacheServiceFactory.CreateMappedEntity<{{EntityName}}, {{EntityName}}Model>();
            var {{EntityNameCamelCase}} = await {{EntityNameCamelCase}}Cache.AllAsync();
            
            return {{EntityNameCamelCase}};
        }

        public async Task<{{EntityName}}Model> GetByIdAsync(int id)
        {
            var {{EntityNameCamelCase}}Cache = _cacheServiceFactory.CreateMappedEntity<{{EntityName}}, {{EntityName}}Model>();
            var {{EntityNameCamelCase}} = await {{EntityNameCamelCase}}Cache.FirstOrDefaultAsync(m => m.Id == id);
            
            return {{EntityNameCamelCase}};
        }

        public async Task AddAsync({{EntityName}}Model model)
        {
            var {{EntityNameCamelCase}} = _mapper.Map<{{EntityName}}>(model);
            var {{EntityNameCamelCase}}Cache = _cacheServiceFactory.CreateTrackedEntity<{{EntityName}}>();
            {{EntityNameCamelCase}}Cache.Add({{EntityNameCamelCase}});
            await {{EntityNameCamelCase}}Cache.SaveChangesAsync();   
        }

        public async Task UpdateAsync({{EntityName}}Model model)
        {
            var {{EntityNameCamelCase}}Cache = _cacheServiceFactory.CreateTrackedEntity<{{EntityName}}>();

            var {{EntityNameCamelCase}} = await {{EntityNameCamelCase}}Cache.FirstOrDefaultAsync(m => m.Id == model.Id);
            
            {{#if Properties}}{{#each Properties}}{{EntityNameCamelCase}}.{{this.Name}} =  model.{{this.Name}};
            {{/each}}{{/if}}                        
                        
            await {{EntityNameCamelCase}}Cache.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var {{EntityNameCamelCase}}Cache = _cacheServiceFactory.CreateTrackedEntity<{{EntityName}}>();
            var {{EntityNameCamelCase}} = {{EntityNameCamelCase}}Cache.Configure(p => p).FirstOrDefault(m => m.Id == id);

            {{EntityNameCamelCase}}Cache.Remove({{EntityNameCamelCase}});
            await {{EntityNameCamelCase}}Cache.SaveChangesAsync();
        }

        public async Task<IEnumerable<{{EntityName}}HistoryModel>> GetHistory(int id)
        {
            var {{EntityNameCamelCase}}Repository = _repositoryFactory.Create<{{EntityName}}>();
            var {{EntityNameCamelCase}}History = await {{EntityNameCamelCase}}Repository
                .GetTemporalAll()
                .Where(x => x.Id == id)
                .OrderByDescending(x => x.LastUpdatedDate)
                .ToListAsync();

            return _mapper.Map<IEnumerable<{{EntityName}}HistoryModel>>({{EntityNameCamelCase}}History);
        }
    }
}
