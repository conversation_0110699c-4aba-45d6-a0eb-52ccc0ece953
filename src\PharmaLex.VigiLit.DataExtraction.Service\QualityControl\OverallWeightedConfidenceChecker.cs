﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using System.Reflection;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class OverallWeightedConfidenceChecker : IExtractionValidator
{
    private readonly List<PropertyInfo> _nonMandatoryFields = [];
    private readonly ILogger<OverallWeightedConfidenceChecker> _logger;
    private readonly List<PropertyInfo> _mandatoryFields = [];
    private readonly float _mandatoryWeight;
    private readonly float _nonMandatoryWeight;
    private readonly float _overallMinimumConfidenceLevel;
    public OverallWeightedConfidenceChecker(ILogger<OverallWeightedConfidenceChecker> logger, IConfiguration configuration)
    {
        PopulatePropertyInfoCollections();
        _mandatoryWeight = configuration.GetValue<float>("DataExtraction:MandatoryWeight");
        _nonMandatoryWeight = configuration.GetValue<float>("DataExtraction:NonMandatoryWeight");
        _overallMinimumConfidenceLevel = configuration.GetValue<float>("DataExtraction:OverallMinimumConfidenceLevel");
        _logger = logger;
    }

    public bool IsValid(ExtractedReference extractedReference)
    {
        var nonMandatoryConfidences = GetConfidenceLevels(extractedReference, _nonMandatoryFields);

        var mandatoryWeightedSum = _mandatoryFields.Sum(x => (x.GetValue(extractedReference) as IExtractedProperty)!.Confidence * _mandatoryWeight);
        var nonMandatoryWeightedSum = nonMandatoryConfidences.Sum(confidence => confidence * _nonMandatoryWeight);
        var totalWeight = _mandatoryFields.Count * _mandatoryWeight + _nonMandatoryFields.Count * _nonMandatoryWeight;
        var overallConfidence = totalWeight <= 0.001F ? 0.0F : (mandatoryWeightedSum + nonMandatoryWeightedSum) / totalWeight;
        _logger.LogInformation("Calculated {overallConfidence} and configured {minimumConfidenceLevel}", overallConfidence, _overallMinimumConfidenceLevel);
        return overallConfidence > _overallMinimumConfidenceLevel;
    }

    private static List<float> GetConfidenceLevels(ExtractedReference reference, List<PropertyInfo> fields)
    {
        var confidenceLevels = new List<float>();
        foreach (var field in fields)
        {
            if (field.GetValue(reference) is ICollection<IExtractedProperty> items)
            {
                confidenceLevels.AddRange(items.Select(x => x.Confidence));
            }
            else
            {
                var extractedProperty = field.GetValue(reference) as IExtractedProperty;
                confidenceLevels.Add(extractedProperty!.Confidence);
            }
        }
        return confidenceLevels;
    }

    private void PopulatePropertyInfoCollections()
    {
        var properties = typeof(ExtractedReference).GetProperties();
        foreach (var info in properties)
        {
            if (Attribute.IsDefined(info, typeof(MandatoryAttribute)))
            {
                _mandatoryFields.Add(info);
            }

            else
            {
                _nonMandatoryFields.Add(info);
            }
        }
    }
}