﻿using Microsoft.CodeAnalysis.Scripting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using PharmaLex.VigiLit.ReferenceManagement;
using FuzzySharp;

namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;

internal class ReferenceMatcher : IReferenceMatcher
{
    private readonly ICSharpScriptFactory _cSharpScriptFactory;
    private readonly IExpressionBuilder _expressionBuilder;
    private readonly IAbstractTextSearcher _abstractTextSearcher;
    private readonly ILogger<ReferenceMatcher> _logger;
    private readonly int _fuzzySharpJournalMatchThreshold;

    public ReferenceMatcher(IAbstractTextSearcher abstractTextSearcher,
                                ICSharpScriptFactory cSharpScriptFactory,
                                IExpressionBuilder expressionBuilder,
                                ILogger<ReferenceMatcher> logger,
                                IConfiguration configuration)
    {
        _abstractTextSearcher = abstractTextSearcher;
        _cSharpScriptFactory = cSharpScriptFactory;
        _expressionBuilder = expressionBuilder;
        _logger = logger;
        _fuzzySharpJournalMatchThreshold = configuration.GetValue<int>("DataExtraction:FuzzySharpJournalMatchThreshold");
    }

    public async Task<bool> Matches(IReference referenceOperand, string matchCondition, IReadOnlyCollection<string> journalTitles)
    {
        var abstractText = referenceOperand.Abstract;
        _logger.LogInformation("Raw search term {matchCondition} to check in {abstractText} ", matchCondition, abstractText);

        var formattedCSharpCondition = _expressionBuilder.Build(matchCondition);
        _logger.LogInformation("Formatted string: {formattedString} to check in abstractText ", formattedCSharpCondition);

        try
        {
            var result = await DoesAbstractMatch(formattedCSharpCondition, abstractText);
            result &= DoesJournalTitleMatchWhenChecking(referenceOperand.JournalTitle, journalTitles);

            // set true for testing
            return result;
        }

        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in reference matching: {Error}", ex.ToString());
            return await Task.FromResult(false);
        }
    }

    private bool DoesJournalTitleMatchWhenChecking(string journalTitle, IReadOnlyCollection<string> journalTitles)
    {
        if (journalTitles.Count == 0)
        {
            return true;
        }

        var journalTitleUpper = journalTitle.ToUpper();
        var journalTitlesUpper = journalTitles.Select(x => x.ToUpper());

        var fuzzyMatchSuccess = journalTitlesUpper.Any(x => Fuzz.Ratio(journalTitleUpper, x) > _fuzzySharpJournalMatchThreshold);

        if (!fuzzyMatchSuccess)
        {
            _logger.LogWarning("Journal title: {JournalTitle} could not be matched.", journalTitle);
        }

        return fuzzyMatchSuccess;
    }

    private async Task<bool> DoesAbstractMatch(string formattedCSharpCondition, string abstractText)
    {
        var scriptOptions = ScriptOptions.Default.AddReferences(typeof(string).Assembly);

        var script = _cSharpScriptFactory.Create(
            formattedCSharpCondition,
            scriptOptions,
            globalsType: typeof(IAbstractTextSearcher)
        );

        _abstractTextSearcher.AbstractText = abstractText;
        var scriptResult = await script.RunAsync(_abstractTextSearcher);
        return scriptResult.ReturnValue;
    }

}