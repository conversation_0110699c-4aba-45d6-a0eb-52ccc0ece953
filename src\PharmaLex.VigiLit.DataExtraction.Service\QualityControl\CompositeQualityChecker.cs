﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.ImportManagement.Client;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class CompositeQualityChecker : ICompositeQualityChecker
{
    private readonly IEnumerable<IExtractionValidator> _extractionValidators;
    private readonly IEnumerable<IExtractionMatchValidator> _matchValidators;
    private readonly ILogger<CompositeQualityChecker> _logger;

    public CompositeQualityChecker(
        ILogger<CompositeQualityChecker> logger,
        IEnumerable<IExtractionValidator> extractionValidators,
        IEnumerable<IExtractionMatchValidator> matchValidators)
    {
        _logger = logger;
        _extractionValidators = extractionValidators;
        _matchValidators = matchValidators;
    }

    public bool IsValid(ExtractedReference extractedReference)
    {
        // Check all basic validators
        var basicValidationPassed = _extractionValidators.All(validator =>
        {
            var result = validator.IsValid(extractedReference);
            if (!result)
            {
                _logger.LogWarning("Basic validation failed in: {QualityCheck}", validator.GetType().Name);
            }
            return result;
        });

        // Check all match validators
        var matchValidationPassed = _matchValidators.All(validator =>
        {
            var matchResult = validator.GetMatchResult(extractedReference);
            var result = matchResult.Status == MatchingStatus.Passed;
            if (!result)
            {
                _logger.LogWarning("Match validation failed in: {QualityCheck}", validator.GetType().Name);
            }
            return result;
        });

        return basicValidationPassed && matchValidationPassed;
    }
}
