﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Repositories;

internal interface IContractRepository : ITrackingRepository<Contract>
{
    /// <summary>
    /// Gets the specified contract with the versions.
    /// </summary>
    /// <param name="id">The identifier.</param>
    /// <returns></returns>
    Task<Contract?> Get(int id);
}