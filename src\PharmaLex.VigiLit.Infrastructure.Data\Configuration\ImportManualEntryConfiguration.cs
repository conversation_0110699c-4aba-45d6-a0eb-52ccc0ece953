﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration
{
    public class ImportManualEntryConfiguration : EntityBaseMap<ImportManualEntry>
    {
        public override void Configure(EntityTypeBuilder<ImportManualEntry> builder)
        {
            base.Configure(builder);

            builder.ToTable("ImportManualEntry");

            builder.Property(e => e.Doi)
                .HasMaxLength(250);
            builder.Property(e => e.SourceId)
                .HasMaxLength(450);
        }
    }
}