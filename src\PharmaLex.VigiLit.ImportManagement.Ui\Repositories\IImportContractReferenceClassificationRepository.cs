﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public interface IImportContractReferenceClassificationRepository : ITrackingRepository<ImportContractReferenceClassification> // Used by Classification Service
{
    Task<IEnumerable<ImportContractReferenceClassification>> GetForSigning(int importId, int batchSize);
    Task<DashboardDetailsResult> GetImportDashboardDetailsRows(int importId, int page, int pageSize);
    Task<IEnumerable<DashboardDetailsRowModel>> GetImportDashboardDetailsExport(int importId);
    void ClearChangeTracker();
}