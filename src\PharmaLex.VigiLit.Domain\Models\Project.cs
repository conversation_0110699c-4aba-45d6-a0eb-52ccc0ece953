using PharmaLex.VigiLit.DataAccessLayer.Base;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace PharmaLex.VigiLit.Domain.Models;

public class Project : VigiLitEntityBase
{
    public string Name { get; set; }
    public int CompanyId { get; set; }
    public Company Company { get; set; }
    protected internal IList<Contract> ContractsInternal { get; set; } = new List<Contract>();
    public IReadOnlyCollection<Contract> Contracts => new ReadOnlyCollection<Contract>(ContractsInternal);

    public Project()
    {
    }

    public Project(string name, int companyId)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentNullException(nameof(Project));

        if (companyId == default)
            throw new ArgumentException($"Company Id: {nameof(companyId)}");

        Name = name;
        CompanyId = companyId;
    }

    public new void Update(string name)
    {
        if (string.IsNullOrWhiteSpace(name))
            throw new ArgumentNullException(nameof(Project));
        Name = name;
    }

    public void AddContract(Contract contract) => ContractsInternal.Add(contract);

    public void RemoveContract(Contract contract) => ContractsInternal.Remove(contract);

    // MIGRATIION ONLY
    public Project(string name)
    {
        Name = name;
    }
}
