﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.7" />
    <PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Company\PharmaLex.VigiLit.Company.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.DataAccessLayer\PharmaLex.VigiLit.DataAccessLayer.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ReferenceManagement.Contracts\PharmaLex.VigiLit.ReferenceManagement.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ReferenceManagement.Service\PharmaLex.VigiLit.ReferenceManagement.Service.csproj" />
  </ItemGroup>

</Project>
