﻿using System.Text;

namespace PharmaLex.VigiLit.Domain.Models;

public class ReferenceUpdate : ReferenceBase
{
    public int ImportContractId { get; set; }
    public int ReferenceId { get; set; }
    public int SubstanceId { get; set; }

    public string Citation
    {
        get
        {
            var citation = new StringBuilder();
            if (!string.IsNullOrWhiteSpace(Authors))
            {
                citation.Append($"{Authors}. ");
            }

            citation.Append($"{VolumeAbbreviation} ");
            citation.Append($"{PublicationYear}; ");
            citation.Append($"{Volume}");

            if (!string.IsNullOrWhiteSpace(FullPagination))
            {
                citation.Append($": {FullPagination}");
            }

            return citation.ToString();
        }
    }

    public bool Is(ReferenceUpdate updateForSubstance)
    {
        return updateForSubstance != null && Id == updateForSubstance.Id;
    }
}
