﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface IReferenceClassificationRepository : ITrackingRepository<ReferenceClassification>
{
    Task<ReferenceClassification?> GetByIdAsync(int id);
    Task AddRangeAsync(IEnumerable<ReferenceClassification> references);
    Task<IEnumerable<ReferenceClassification>> GetReferenceClassificationsToPreClassify_Locked(List<int> classificationIds);
    Task<ReferenceClassification?> GetReferenceClassificationToPreClassify(int importId, List<int> allLockedClassificationIds);
    Task<ReferenceClassification?> GetReferenceClassificationToPreClassify_BySubstancePreference(int userId, int importId, List<int> allLockedClassificationIds);
    Task<IEnumerable<ReferenceClassification>> GetReferenceClassificationsForDuplicates(int referenceClassificationId, string doi, int substanceId, List<int> allLockedClassificationIds);
    Task<IEnumerable<ReferenceClassification>> GetPreclassifiedPotentialCasesForApproval(int importId);
    Task<IEnumerable<ReferenceClassification>> GetPreclassifiedQualityCheckSample(int assessorId, int itemsToTake, int importId);
    Task<IEnumerable<QualityCheckStatusReport>> GetAssessorQualityCheckStatusReports(int importId);
    Task<int> GetTodoCount(int importId);
    Task<int> GetPreclassifiedCount(int userId, int importId);
    Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetPreclassifiedByUserId(int userId, int importId);
    Task<int> GetClassifiedCount(int importId);
    Task<ReferenceClassificationWithSubstanceModel> GetTemporalAsOfAsync(DateTime timeStampOn, int referenceClassificationId);
    Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetAllClassified(int importId);
    void ClearChangeTracker();
    Task<IEnumerable<ReferenceClassificationWithSubstanceModel>> GetForReferenceDetails(int referenceId, User user);
    Task<ReferenceClassification?> GetReferenceClassificationForEdit(int referenceClassificationId);
    Task<ReferenceClassificationSendGridTemplateModel?> GetForEmail(int referenceClassificationId);
    Task<AiSuggestedClassificationModel> GetAiSuggestedClassification(DateTime dateRevised, string substanceName, string sourceId);
}