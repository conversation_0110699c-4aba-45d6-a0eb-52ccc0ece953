﻿using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.AccessControl;

namespace PharmaLex.VigiLit.Case;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddCasesAccessControl(this IServiceCollection services)
    {
        services.AddScoped<IPermissionEvaluator, CaseAccessEvaluator>();
        services.AddScoped<IPermissionEvaluator<CaseAccessContext>, CaseAccessEvaluator>();

        return services;
    }
}
