﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CaseCompaniesConfiguration : EntityBaseMap<CaseCompanies>
{
    public override void Configure(EntityTypeBuilder<CaseCompanies> builder)
    {
        base.Configure(builder);

        builder.ToTable("CaseCompanies");

        builder.<PERSON><PERSON>ey(e => e.Id);
    }
}