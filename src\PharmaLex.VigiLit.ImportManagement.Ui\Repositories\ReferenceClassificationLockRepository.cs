﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public class ReferenceClassificationLockRepository : TrackingGenericRepository<ReferenceClassificationLock>, IReferenceClassificationLockRepository
{
    // Warning: Id is not the Primary Key, instead it's {ReferenceClassificationId, UserId}

    public ReferenceClassificationLockRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<List<int>> GetAllLockedClassificationIdsForPick()
    {
        return await context.Set<ReferenceClassificationLock>()
            .Select(r => r.ReferenceClassificationId)
            .ToListAsync();
    }

    public async Task<IEnumerable<ReferenceClassificationLock>> GetLocksForUser(int userId)
    {
        return await context.Set<ReferenceClassificationLock>()
            .Where(e => e.UserId == userId)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<LockingResult> Lock(List<int> referenceClassificationIds, int userId)
    {
        int expected = referenceClassificationIds.Count;
        int claimed = 0;

        var existingLocks = await context.Set<ReferenceClassificationLock>()
                .Where(e => referenceClassificationIds.Contains(e.ReferenceClassificationId))
                .ToListAsync();

        foreach (var referenceClassificationId in referenceClassificationIds)
        {
            var existingLock = existingLocks.Find(e => e.ReferenceClassificationId == referenceClassificationId);

            if (existingLock == null)
            {
                var newLock = new ReferenceClassificationLock { ReferenceClassificationId = referenceClassificationId, UserId = userId };
                await context.AddAsync(newLock);
                claimed++;
            }
            else if (existingLock.UserId != userId)
            {
                throw new InvalidOperationException(
                    string.Format("Cannot lock referenceClassificationId={0} for userId={1} because it is already locked by userId={2}. Reference Ids: {3} Locks: {4}",
                        referenceClassificationId,
                        userId,
                        existingLock.UserId,
                        string.Join(",", referenceClassificationIds),
                        string.Join(",", existingLocks.Select(x => x.ReferenceClassificationId))));
            }
            else if (existingLock.UserId == userId)
            {
                claimed++;
            }
        }

        await SaveChangesAsync();

        return new LockingResult() { IsSuccessful = claimed == expected, ClassificationIds = referenceClassificationIds };
    }

    public async Task<LockingResult> Unlock(int userId)
    {
        var locks = await context.Set<ReferenceClassificationLock>()
            .Where(e => e.UserId == userId)
            .ToListAsync();

        RemoveRange(locks);
        await SaveChangesAsync();

        return new LockingResult() { IsSuccessful = true, ClassificationIds = locks.Select(x => x.ReferenceClassificationId).ToList() };
    }
}
