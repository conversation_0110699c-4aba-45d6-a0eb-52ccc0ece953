﻿<script type="text/x-template" id="journals-table-component-template">
    <div id="journals-table" v-cloak>
        <div class="sub-header">
            <h2>Journals</h2>
            <div class="controls">
                <button v-on:click="openJournalModalForAdd()">Add Journal</button>
            </div>
        </div>

            <filtered-table :items="journals" :columns="columns" :filters="filters" :link="link" :key="tableKey"  @@row-clicked="selectJournal"></filtered-table>
       
    </div>
    <journal-modal :journal="selectedJournal" @@saved="save"></journal-modal>
</script>

<script type="text/javascript">
    vueApp.component('journals-table', {
        template: '#journals-table-component-template',
        data: function () {
            return {
                selectedJournal: {
                    url: '',
                    name: '',
                    countryId: 0
                },
                journals: [],
                tableKey: 0,
                columns: {
                    idKey: 'id',
                    config: [
                        {
                            dataKey: 'url',
                            sortKey: 'url',
                            header: 'Data Source',
                            type: 'text',
                            style: 'width: 30%;'
                        },
                        {
                            dataKey: 'name',
                            sortKey: 'name',
                            header: 'Journal Title',
                            type: 'text',
                            style: 'width: 30%;'
                        },
                        {
                            dataKey: 'countryName',
                            sortKey: 'countryName',
                            header: 'Country',
                            type: 'text',
                            style: 'width: 30%;'
                        },
                        {
                            dataKey: 'issn',
                            sortKey: 'issn',
                            header: 'ISSN',
                            type: 'text',
                            style: 'width: 20%;'
                        },
                        {
                            dataKey: 'cronExpression',
                            sortKey: 'cronExpression',
                            header: 'Frequency',
                            type: 'text',
                            style: 'width: 20%;'
                        },
                        {
                            dataKey: 'enabled',
                            sortKey: 'enabled',
                            header: 'Enabled',
                            type: 'bool',
                            style: 'width: 10%;'
                        },
                        {
                            dataKey: 'countryId',
                            sortKey: 'countryId',
                            header: 'CountryId',
                            type: 'text',
                            style: 'display:none;'
                        },
                    ]
                },
                filters: [
                    {
                        key: 'name',
                        options: [],
                        type: 'search',
                        header: 'Journal Title',
                        fn: v => p => p.name.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        },
        methods: {
            openJournalModalForAdd() {
                this.selectedJournal = { countryId: 0 };
                $('#addJournalModal').modal('show');
            },
            selectJournal(journal) {
                this.getJournal(journal.id);
            },
            getJournal(id) {
                fetch(`/Journals/${id}`, {
                    method: "GET",
                    credentials: 'same-origin',
                    headers: {
                        "Content-Type": "application/json",
                        "RequestVerificationToken": token
                    }
                }).then(res => {
                    if (!res.ok) {
                        throw res;
                    }
                    return res.json();
                }).then((journal) => {
                    this.selectedJournal = { ...journal }; // Make a fresh object to trigger reactivity
                    this.$nextTick(() => {
                        $('#addJournalModal').modal('show');
                    });
                }).catch(error => {
                    console.log(error);
                    plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                });
            },
            save({ journal, isEdit }) {
                if (isEdit) {
                    this.updateJournal(journal);
                } else {
                    this.addJournal(journal);
                }

            },
            addJournal(journal) {
                fetch(`/Journals/Save`, {
                    method: "POST",
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        name: journal.name || '',
                        countryId: journal.countryId || 0,
                        url: journal.url || '',
                        enabled:journal.enabled || false,
                        issn: journal.issn || null,
                        cronExpression: journal.cronExpression || ''
                    }),
                    headers: {
                        "Content-Type": "application/json",
                        "RequestVerificationToken": token
                    }
                })
                    .then(async res => {
                        const text = await res.text();
                        let data = {};

                        try {
                            data = text ? JSON.parse(text) : {};
                        } catch (err) {
                            console.warn('Failed to parse JSON:', err);
                        }

                        return { status: res.status, ok: res.ok, body: data };
                    })
                    .then(({ status, ok, body }) => {
                        if (status === 400) {
                            if (body.errors) {
                                let errorMessages = [];
                                for (const field in body.errors) {
                                    const messages = Array.isArray(body.errors[field])
                                        ? body.errors[field]
                                        : [body.errors[field]];
                                    messages.forEach(msg => errorMessages.push(`* ${msg}`));
                                }
                                plx.toast.show(errorMessages.join('\n'), 2, 'failed', null, 2500);
                            } else {
                                plx.toast.show('Validation failed, but no details provided.', 2, 'failed', null, 2500);
                            }
                            throw new Error('Validation failed');
                        }

                        if (!ok) {
                            throw new Error(`Server error: ${status}`);
                        }

                        $('#addJournalModal').modal('hide');
                        plx.toast.show('Journal added successfully', 2, 'confirm', null, 2500);

                        if (typeof this.getJournals === 'function') {
                            this.getJournals();
                        }
                    })
                    .catch(error => {
                        console.error('Fetch error:', error);
                        if (error.message !== 'Validation failed') {
                            plx.toast.show('Something went wrong, please try again.', 2, 'failed', null, 2500);
                        }
                    });
            },
            updateJournal(journal) {
                fetch(`/Journals/Update`, {
                    method: "POST",
                    credentials: 'same-origin',
                    body: JSON.stringify({
                        id: journal.id,
                        name: journal.name || '',
                        countryId: journal.countryId || 0,
                        url: journal.url || '',
                        enabled: journal.enabled,
                        issn: journal.issn || null,
                        cronExpression: journal.cronExpression || ''
                    }),
                    headers: {
                        "Content-Type": "application/json",
                        "RequestVerificationToken": token
                    }
                })
                    .then(async res => {
                        const text = await res.text();
                        let data = {};

                        try {
                            data = text ? JSON.parse(text) : {};
                        } catch (err) {
                            console.warn('Failed to parse JSON:', err);
                        }

                        return { status: res.status, ok: res.ok, body: data };
                    })
                    .then(({ status, ok, body }) => {
                        if (status === 400) {
                            if (body.errors) {
                                let errorMessages = [];
                                for (const field in body.errors) {
                                    const messages = Array.isArray(body.errors[field])
                                        ? body.errors[field]
                                        : [body.errors[field]];
                                    messages.forEach(msg => errorMessages.push(`* ${msg}`));
                                }
                                plx.toast.show(errorMessages.join('\n'), 2, 'failed', null, 2500);
                            } else {
                                plx.toast.show('Validation failed, but no details provided.', 2, 'failed', null, 2500);
                            }
                            throw new Error('Validation failed');
                        }

                        if (!ok) {
                            throw new Error(`Server error: ${status}`);
                        }

                        $('#addJournalModal').modal('hide');
                        plx.toast.show('Journal updated successfully', 2, 'confirm', null, 2500);

                        if (typeof this.getJournals === 'function') {
                            this.getJournals();
                        }
                    })
                    .catch(error => {
                        console.error('Fetch error:', error);
                        if (error.message !== 'Validation failed') {
                            plx.toast.show('Something went wrong, please try again.', 2, 'failed', null, 2500);
                        }
                    });

            },
            getJournals() {
                fetch(`/Journals`, {
                    method: "GET",
                    credentials: 'same-origin',
                    headers: {
                        "Content-Type": "application/json",
                        "RequestVerificationToken": token
                    }
                }).then(res => {
                    if (!res.ok) {
                        throw res;
                    }
                    return res.json();
                }).then((data) => {
                    this.journals = data;
                    this.tableKey++; // triggers re-render of filtered-table
                }).catch(error => {
                    console.log(error);
                    plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                });
            }
        },
        mounted() {
            this.getJournals();
        }
    });
</script>

