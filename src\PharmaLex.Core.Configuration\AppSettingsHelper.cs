﻿using Microsoft.Extensions.Configuration;

namespace PharmaLex.Core.Configuration;

/// <summary>
/// Helper for getting settings from configuration.
/// </summary>
/// <remarks>
/// Properties are nullable when they probably shouldn't be but to keep it backwards compatible is nullable for now.
/// </remarks>
public class AppSettingsHelper 
{
    public string? BuildInfo { get; private set; }
    public string? SystemAdminEmail { get; private set; }
    public string? Version { get; private set; }
    public AppSettingsHelper(IConfiguration configuration) 
    {
        BuildInfo = configuration.GetValue<string>("appSettings:BuildInfo");
        SystemAdminEmail = configuration.GetValue<string>("appSettings:SystemAdminEmail");
        Version = configuration.GetValue<string>("appSettings:Version");
    }
}