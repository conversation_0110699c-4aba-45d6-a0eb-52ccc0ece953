using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ImportContractConfiguration : EntityBaseMap<ImportContract>
{
    public override void Configure(EntityTypeBuilder<ImportContract> builder)
    {
        base.Configure(builder);

        builder.ToTable("ImportContracts");

        builder.HasIndex(c => new { c.ContractId, c.PubMedModificationDate });

        builder.HasIndex(c => new { c.ImportId })
            .IncludeProperties(p => new
            {
                p.ContractId,
                p.PubMedModificationDate,
                p.StartDate,
                p.EndDate,
                p.ImportContractStatusType,
                p.ReferencesCount,
                p.NewReferencesCount,
                p.UpdatesCount,
                p.SilentUpdatesCount
            });

        builder.HasIndex(c => new { c.Id, c.ImportId });

        builder.HasIndex(c => new { c.ContractVersionId, c.ContractId, c.ImportId })
            .IncludeProperties(p => new
            {
                p.ReferencesCount,
                p.ImportDate
            });

        builder.HasOne(ic => ic.Import).WithMany(i => i.ImportContracts).HasForeignKey(ic => ic.ImportId);
        builder.HasOne(ic => ic.Contract).WithMany(c => c.ImportContracts).HasForeignKey(ic => ic.ContractId);
    }
}