using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ReferenceUpdateConfiguration : EntityBaseMap<ReferenceUpdate>
{
    public override void Configure(EntityTypeBuilder<ReferenceUpdate> builder)
    {
        base.Configure(builder);

        builder.ToTable("ReferenceUpdates");

        builder.HasIndex(p => new { p.ReferenceId, p.SubstanceId }).IsUnique();

        builder.Property(i => i.ImportContractId)
            .IsRequired();

        builder.Property(i => i.SourceId)
            .IsRequired();

        builder.Property(i => i.ReferenceId)
            .IsRequired();

        builder.Property(i => i.SubstanceId)
            .IsRequired();

        builder.Property(e => e.Doi)
            .HasMaxLength(250);
    }
}