﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.ImportManagement.Client;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ReferenceManagement;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.ImportManagement.Entities;

#pragma warning disable CS8766
public class FailedImportFile : VigiLitEntityBase, IReference
{
    public string? Abstract { get; set; }
    public string? AffiliationTextFirstAuthor { get; set; }
    public string? Authors { get; set; }
    public string? CountryOfOccurrence { get; set; }
    public string? CountryMatched { get; set; }
    public DateTime DateRevised { get; set; }
    public string? Doi { get; set; }
    public string? FullPagination { get; set; }
    public string? Issn { get; set; }
    public string? Issue { get; set; }
    public string? Language { get; set; }
    public int SourceSystem { get; set; }
    public string? SourceId { get; set; }
    public string? PublicationType { get; set; }
    public ushort? PublicationYear { get; set; }
    public string? Title { get; set; }
    public string? Volume { get; set; }
    public string? VolumeAbbreviation { get; set; }
    public string? Keywords { get; set; }
    public string? MeshHeadings { get; set; }
    public string? JournalTitle { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte AbstractConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte AffiliationTextFirstAuthorConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte AuthorsConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte CountryOfOccurrenceConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte DateRevisedConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte DoiConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte FullPaginationConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte IssnConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte IssueConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte LanguageConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte SourceIdConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte PublicationTypeConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte PublicationYearConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte TitleConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte VolumeConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte VolumeAbbreviationConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte KeywordsConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte MeshHeadingsConfidence { get; set; }

    [Range(0, 100, ErrorMessage = "Confidence level must be between 0 and 100")]
    public byte JournalTitleConfidence { get; set; }

    public bool AbstractConfidenceCheckPassed { get; set; }
    public bool CountryOfOccurrenceConfidenceCheckPassed { get; set; }
    public bool TitleConfidenceCheckPassed { get; set; }
    public bool JournalTitleConfidenceCheckPassed { get; set; }
    public bool JournalTitleMatchingCheckPassed { get; set; }
    public MatchingStatus CountryOfOccurrenceMatchingStatus { get; set; }

    [Required]
    public string? DocumentLocation { get; set; }

    [Required]
    public string? Filename { get; set; }

    [Required]
    public Guid CorrelationId { get; set; }

    [Required]
    public Guid BatchId { get; set; }

    public FailedImportStatusType Status { get; set; }
}

