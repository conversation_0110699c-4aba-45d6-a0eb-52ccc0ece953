﻿using PharmaLex.VigiLit.AccessControl;

namespace PharmaLex.VigiLit.ContractManagement.Security;

public class ViewContractPermissionContext : IAccessControlContext
{
    /// <summary>
    /// Context for checking permissions for a user to view a contract.
    /// </summary>
    /// <seealso cref="PharmaLex.VigiLit.AccessControl.IAccessControlContext" />
    public ViewContractPermissionContext(int userId, int contractId)
    {
        UserId = userId;
        ContractId = contractId;
    }

    public int UserId { get; }

    public int ContractId { get; }
}
