using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface IContractService
{
    Task<IEnumerable<SimpleContractModel>> GetAllAsync();
    Task<ContractDetailsModel> GetContractDetails(int id);
    Task<ContractDetailsModel> GetContractDetailsForAdminUsers(int id);
    Task<IEnumerable<ExportContractModel>> GetAllToExportAsync();
    Task<IEnumerable<SimpleContractModel>> GetForAdHocImportCreate(int companyId, int projectId = 0);
    Task AddAsync(ContractModel model);
    Task UpdateAsync(ContractModel model);
    Task<ContractModel> GetByIdAsync(int id);
    Task<ContractDetailsModel> GetContractWithLatestVersion(int contractId);
}
