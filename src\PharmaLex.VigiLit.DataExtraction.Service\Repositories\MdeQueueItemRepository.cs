﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.DataExtraction.Entities;
using PharmaLex.VigiLit.DataExtraction.Entities.Enums;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;

namespace PharmaLex.VigiLit.DataExtraction.Service.Repositories;

internal class MdeQueueItemRepository(PlxDbContext context, IUserContext userContext)
    : TrackingGenericRepository<MdeQueueItem>(context, userContext.User), IMdeQueueItemRepository
{
    public async Task<MdeQueueItem?> GetNextInQueue()
    {
        return await context.Set<MdeQueueItem>()
            .Where(x => x.Status == MdeQueueItemStatus.Queued)
            .OrderBy(x => x.Id)
            .FirstOrDefaultAsync();
    }

    public async Task<MdeQueueItem?> GetByCorrelationId(Guid correlationId)
    {
        return await context.Set<MdeQueueItem>()
            .Where(i => i.CorrelationId == correlationId)
            .FirstOrDefaultAsync();
    }
}