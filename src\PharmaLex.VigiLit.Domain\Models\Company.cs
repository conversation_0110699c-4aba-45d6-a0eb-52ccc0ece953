using PharmaLex.VigiLit.DataAccessLayer.Base;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class Company : VigiLitEntityBase
{
    public string Name { get; private set; }
    public string ContactPersonName { get; private set; }
    public string ContactPersonEmail { get; private set; }
    public bool IsActive { get; set; }

    public IEnumerable<Project> Projects { get; set; } = new List<Project>();
    public IEnumerable<CompanyUser> CompanyUsers { get; set; } = new List<CompanyUser>();
    public ICollection<CompanyReport> CompanyReports { get; set; } = new List<CompanyReport>();

    public Company()
    {

    }

    public Company(string name, string contactPersonName, string contactPersonEmail, bool isActive)
    {
        Update(name, contactPersonName, contactPersonEmail, isActive);
    }

    public void Update(string name, string contactPersonName, string contactPersonEmail, bool isActive)
    {
        if (string.IsNullOrEmpty(name))
        {
            throw new ArgumentException($"{nameof(Company)} {nameof(name)} is required");
        }

        Name = name;
        ContactPersonName = contactPersonName;
        ContactPersonEmail = contactPersonEmail;
        IsActive = isActive;
    }
}