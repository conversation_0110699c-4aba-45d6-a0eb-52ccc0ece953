﻿using Microsoft.AspNetCore.Http;
using Microsoft.Azure.Functions.Worker;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
using System;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.ImportApp.Functions.ImportDataExtract;

public class ImportDataExtractProcessor(IPhlexVisionService phlexVisionService)
{
    [Transaction]
    [Function("ImportDataExtractProcessor_ProcessEnqueuedImports")]
    public async Task Run([HttpTrigger(AuthorizationLevel.Function, "get")] HttpRequest req)
    {
        var extractRequest = new ExtractRequest()
        {
            BatchId = Guid.NewGuid().ToString(),
            FileName = "filename.pdf"
        };

        await phlexVisionService.RequestDataExtraction(extractRequest);

        // Previous code which we may replace with Service Bus which is basically the ProcessQueueItem method on dataExtractionService
    }
}