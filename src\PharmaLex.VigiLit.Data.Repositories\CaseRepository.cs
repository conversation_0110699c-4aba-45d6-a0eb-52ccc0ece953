﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

namespace PharmaLex.VigiLit.Data.Repositories;

public class CaseRepository : TrackingGenericRepository<Cases>, ICaseRepository
{

    public CaseRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<Cases>> GetAllAsync(CaseStatus? caseStatus = null)
    {
        IQueryable<Cases> query = context.Set<Cases>();

        if (caseStatus.HasValue)
            query = query.Where(c => c.Status == caseStatus.Value);

        return await query
            .OrderByDescending(c => c.Id)
            .ToListAsync();
    }

#nullable enable
    public async Task<IEnumerable<Cases>> GetAllWithDetailsAsync(CaseSearchRequest? searchRequest = null)
    {
        IQueryable<Cases> query = context.Set<Cases>()
            .Include(c => c.CaseFiles)
            .Include(c => c.CaseCompanies).ThenInclude(c => c.Company)
            .Include(r => r.ReferenceClassification).ThenInclude(s => s.Substance);

        if (searchRequest != null)
        {
            if (searchRequest.CaseStatus.HasValue)
                query = query.Where(c => c.Status == searchRequest.CaseStatus.Value);

            if (searchRequest.CompanyId.HasValue)
                query = query.Where(c => c.CaseCompanies.Any(caseCompany => caseCompany.CompanyId == searchRequest.CompanyId.Value));

            if (searchRequest.PlxId is > 0)
                query = query.Where(c => c.ReferenceClassificationId == searchRequest.PlxId);

            if (searchRequest.CreatedFrom.HasValue)
                query = query.Where(rc => rc.CreatedDate >= searchRequest.CreatedFrom.Value);

            if (searchRequest.CreatedTo.HasValue)
                query = query.Where(rc => rc.CreatedDate <= searchRequest.CreatedTo.Value);

            if (searchRequest.SubstanceId is > 0)
                query = query.Where(rc => rc.ReferenceClassification.SubstanceId == searchRequest.SubstanceId);
        }

        return await query
            .OrderByDescending(c => c.Id)
            .AsNoTracking()
            .ToListAsync();
    }
#nullable disable

    public async Task<Cases> GetByIdAsync(int id)
    {
        return await context.Set<Cases>()
            .Where(u => u.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<Cases> GetByIdWithDetailsAsync(int id)
    {
        return await context.Set<Cases>()
            .Include(c => c.CaseFiles)
            .Include(c => c.CaseCompanies).ThenInclude(c => c.Company)
            .Include(r => r.ReferenceClassification).ThenInclude(s => s.Substance)
            .Where(u => u.Id == id)
            .FirstOrDefaultAsync();
    }
}