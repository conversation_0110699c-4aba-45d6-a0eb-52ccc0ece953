﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Data.Repositories;

public class CaseFileDocumentTypeRepository : TrackingGenericRepository<CaseFileDocumentTypes>, ICaseFileDocumentTypeRepository
{

    public CaseFileDocumentTypeRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<CaseFileDocumentTypes>> GetAllAsync()
    {
        return await context.Set<CaseFileDocumentTypes>()
            .OrderBy(c => c.Name)
            .AsNoTracking()
            .ToListAsync();
    }
}