﻿using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.Aggregators.PubMed.Repositories;
using PharmaLex.VigiLit.Aggregators.PubMed.Scheduled;

namespace PharmaLex.VigiLit.Aggregators.PubMed;

public static class ConfigureServices
{
    public static void RegisterPubMedAggregator(this IServiceCollection services)
    {
        services.AddScoped<IImportQueueService, ImportQueueService>();

        services.AddScoped<IAdHocImportRepository, AdHocImportRepository>();
        services.AddScoped<IContractRepository, ContractRepository>();
        services.AddScoped<IImportRepository, ImportRepository>();
        services.AddScoped<IImportContractRepository, ImportContractRepository>();
    }
}
