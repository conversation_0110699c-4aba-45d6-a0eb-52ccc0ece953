@using PharmaLex.Core.Web.Helpers
@model PharmaLex.VigiLit.ImportManagement.Ui.Models.ImportLogDetailsPageModel
@{
    ViewData["Title"] = "Import Log Details";
}

<div id="import-log-details" v-cloak>
    <div class="sub-header">
        <h2>Import Log Details</h2>
        <div class="controls">
            <div v-if="pageModel.canRetry">
                <form asp-action="ImportLogDetails_Retry" asp-route-importId="@Model.ImportId" method="post">
                    <button>Retry Failed Contracts</button>
                </form>
            </div>
            <a class="button btn-default" href="/ImportDashboardDetails/@Model.ImportId?page=1">Import Details</a>
            <a class="button btn-default" href="../ImportLog">Back to Import Log</a>
        </div>
    </div>
    <section>
        <filtered-table :items="pageModel.importContractModels" :columns="columns" :filters="filters"></filtered-table>
    </section>
</div>

@section Scripts {

<script type="text/javascript">

    let tableData = @Html.Raw(AntiXss.ToJson(Model));
    tableData.importContractModels.forEach(x => x.pubMedModificationDate = x.pubMedModificationDate === "" ? "N/A" : x.pubMedModificationDate);

    var pageConfig = {
        appElement: "#import-log-details",
        data: function () {
            return {
                pageModel: tableData,
                columns: {
                    idKey: 'id',
                    config: [
                            {
                                dataKey: 'companyName',
                                sortKey: 'companyName',
                                header: 'Company',
                                type: 'text',
                                style: 'width: 10%;'
                            }, 
                            {
                                dataKey: 'projectName',
                                sortKey: 'projectName',
                                header: 'Project',
                                type: 'text',
                                style: 'width: 10%;'
                            },
                            {
                                dataKey: 'substanceName',
                                sortKey: 'substanceName',
                                header: 'Substance',
                                type: 'text',
                                style: 'width: 10%;'
                            },
                            {
                                dataKey: 'importDate',
                                sortKey: 'importDate',
                                header: 'Import Date',
                                type: 'text',
                                style: 'width: 7%;'
                            },
                            {
                                dataKey: 'pubMedModificationDate',
                                sortKey: 'pubMedModificationDate',
                                header: 'Mod Date (ET)',
                                type: 'text',
                                style: 'width: 7%;'
                            },
                            {
                                dataKey: 'startDate',
                                sortKey: 'startDate',
                                header: 'Start Date',
                                type: 'text',
                                style: 'width: 10%;'
                            },
                            {
                                dataKey: 'endDate',
                                sortKey: 'endDate',
                                header: 'End Date',
                                type: 'text',
                                style: 'width: 10%;'
                            },
                            {
                                dataKey: 'duration',
                                sortKey: 'duration',
                                header: 'Duration',
                                type: 'text',
                                style: 'width: 6%;'
                            },
                            {
                                dataKey: 'importContractStatusType',
                                sortKey: 'importContractStatusType',
                                header: 'Status',
                                type: 'text',
                                style: 'width: 7%;'
                            },
                            {
                                dataKey: 'referencesCount',
                                sortKey: 'referencesCount',
                                header: 'References',
                                type: 'number',
                                style: 'width: 6%;'
                            },
                            {
                                dataKey: 'newReferencesCount',
                                sortKey: 'newReferencesCount',
                                header: 'New Refs',
                                type: 'number',
                                style: 'width: 6%;'
                            },
                            {
                                dataKey: 'updatesCount',
                                sortKey: 'updatesCount',
                                header: 'Updates',
                                type: 'number',
                                style: 'width: 6%;'
                            },
                            {
                                dataKey: 'silentUpdatesCount',
                                sortKey: 'silentUpdatesCount',
                                header: 'Silent Upd.',
                                type: 'number',
                                style: 'width: 6%;'
                            }
                    ]
                },
                filters: [{
                        key: 'companyName',
                        options: [],
                        type: 'search',
                        header: 'Search Company',
                        fn: v => p => p.companyName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'projectName',
                        options: [],
                        type: 'search',
                        header: 'Search Project',
                        fn: v => p => p.projectName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'substanceName',
                        options: [],
                        type: 'search',
                        header: 'Search Substance',
                        fn: v => p => p.substanceName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'importContractStatusType',
                        options: [],
                        type: 'search',
                        header: 'Search Status',
                        fn: v => p => p.importContractStatusType.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                ]
            };
        }
    };
</script>
}

@section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
}