using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

public class ImportSelectionMappingProfile : Profile
{
    public ImportSelectionMappingProfile()
    {
        CreateMap<ImportSelection, ImportSelectionModel>()
            .ForMember(d => d.ImportName, s => s.MapFrom(x => string.Format("{0}: {1}", x.Import.ImportType.GetDescription(), x.Import.ImportDate.HasValue ? x.Import.ImportDate.Value.ToString("d MMM yyyy") : "")))
            .ForMember(d => d.ImportDashboardStatusType, s => s.MapFrom(x => x.Import.ImportDashboardStatusType.GetDescription()));
    }
}
