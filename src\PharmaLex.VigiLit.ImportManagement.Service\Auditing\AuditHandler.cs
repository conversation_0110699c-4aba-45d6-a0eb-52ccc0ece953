﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;

namespace PharmaLex.VigiLit.ImportManagement.Service.Auditing;

internal class AuditHandler : IAuditHandler
{
    private readonly ILogger<AuditHandler> _logger;

    private readonly IImportingImportRepository _importRepository;

    public AuditHandler(ILogger<AuditHandler> logger, IImportingImportRepository importRepository)
    {
        _logger = logger;
        _importRepository = importRepository;
    }

    public async Task Consume(StatusChangedEvent command)
    {
        if (command.CorrelationId == Guid.Empty)
        {
            _logger.LogWarning("CorrelationId is empty: {CorrelationId}", command.CorrelationId);
            throw new ArgumentException("CorrelationId is empty");
        }

        var importRecord = await _importRepository.GetByCorrelationId(command.CorrelationId);
        if (importRecord != null)
        {
            _logger.LogInformation("Import Status: {ImportStatus} for CorrelationId : {CorrelationId}", command.Message,
                command.CorrelationId);
            importRecord.EndDate = DateTime.UtcNow;
            importRecord.Message = command.Message;
            importRecord.LastUpdatedBy = command.User;
            await _importRepository.SaveChangesAsync();
        }
        else
        {
            _logger.LogWarning("Record with Correlation Id: {CorrelationId} not found", command.CorrelationId);
        }
    }
}