﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CasesConfiguration : EntityBaseMap<Cases>
{
    public override void Configure(EntityTypeBuilder<Cases> builder)
    {
        base.Configure(builder);

        builder.ToTable("Cases");

        builder.<PERSON><PERSON>ey(e => e.Id);
    }
}