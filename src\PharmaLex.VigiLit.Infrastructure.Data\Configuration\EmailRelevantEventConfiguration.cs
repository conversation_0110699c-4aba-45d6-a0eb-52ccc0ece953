using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class EmailRelevantEventConfiguration : EntityBaseMap<EmailRelevantEvent>
{
    public override void Configure(EntityTypeBuilder<EmailRelevantEvent> builder)
    {
        base.Configure(builder);

        builder.ToTable("EmailRelevantEvents");

        builder.HasIndex(c => new { c.EmailRelevantEventEmailStatusType, c.CompanyInterestId });

        builder.HasOne(ic => ic.CompanyInterest).WithMany(i => i.EmailRelevantEvents).HasForeignKey(ic => ic.CompanyInterestId);
    }
}