﻿using Azure.Storage.Blobs;
using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;

namespace PharmaLex.VigiLit.ContractManagement.Service;
public class ContractDocumentService : IContractDocumentService
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageContractDocumentOptions _azureStorageOptions;

    public ContractDocumentService(IDocumentService documentService,
        IOptions<AzureStorageContractDocumentOptions> azureStorageContractDocumentOptions)
    {
        _documentService = documentService;
        _azureStorageOptions = azureStorageContractDocumentOptions.Value;
    }

    public async Task<BlobClient> Create(ContractDocumentDescriptor contractUploadDocumentDescriptor, Stream stream, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(contractUploadDocumentDescriptor);
        return await _documentService.Create(documentDescriptorUpload, stream, cancellationToken);
    }

    public async Task<Stream> OpenRead(ContractDocumentDescriptor contractUploadDocumentDescriptor,
        CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(contractUploadDocumentDescriptor);
        var stream = await _documentService.OpenRead(documentDescriptor, cancellationToken);
        return stream;
    }

    private DocumentDescriptor GetDocumentDescriptor(ContractDocumentDescriptor contractUploadDocumentDescriptor)
    {
        var blobName = contractUploadDocumentDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_azureStorageOptions.AccountName, _azureStorageOptions.ContainerName, blobName);
        return documentDescriptor;
    }
}
