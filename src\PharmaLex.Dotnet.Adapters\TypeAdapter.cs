using System.Reflection;
using PharmaLex.Dotnet.Adapters.Reflection;

namespace PharmaLex.Dotnet.Adapters;

[System.Diagnostics.DebuggerDisplay("{AssemblyQualifiedName}")]
public class TypeAdapter : IType
{
    private const BindingFlags DefaultBinding = BindingFlags.Public | BindingFlags.Static | BindingFlags.Instance;

    private readonly Type _type;

    public TypeAdapter(Type type)
    {
        _type = type;
    }

    public IAssembly Assembly => new AssemblyAdapter(_type.Assembly);
    public string AssemblyQualifiedName => _type.AssemblyQualifiedName;
    public string FullName => _type.FullName;
    public bool IsAbstract => _type.IsAbstract;
    public bool IsClass => _type.IsClass;
    public bool IsInterface => _type.IsInterface;
    public string Name => _type.Name;

    public override int GetHashCode()
    {
        return _type.GetHashCode();
    }

    public override bool Equals(object obj)
    {
        var typeAdapter = obj as TypeAdapter;

        if (typeAdapter != null)
        {
            return typeAdapter._type.Equals(_type);
        }

        var t = obj as Type;

        if (t != null)
        {
            return t.Equals(_type);
        }

        return false;
    }

    public IEventInfo[] GetEvents()
    {
        return GetEvents(DefaultBinding);
    }

    public IEventInfo[] GetEvents(BindingFlags binding)
    {
        var unadaptedEvents = _type.GetEvents(binding);
        var totalEvents = unadaptedEvents.Length;
        IEventInfo[] events = new IEventInfo[totalEvents];

        for (int i = 0; i < totalEvents; i++)
        {
            events[i] = new EventInfoAdapter(unadaptedEvents[i]);
        }

        return events;
    }

    public IType GetInterface(string name)
    {
        var interfaceType = _type.GetInterface(name);

        if (interfaceType != null)
        {
            return new TypeAdapter(_type.GetInterface(name));
        }

        return null;
    }

    public IType[] GetInterfaces()
    {
        var unadaptedTypes = _type.GetInterfaces();
        var totalInterfaces = unadaptedTypes.Length;
        IType[] interfaceTypes = new IType[totalInterfaces];

        for (int i = 0; i < totalInterfaces; i++)
        {
            interfaceTypes[i] = new TypeAdapter(unadaptedTypes[i]);
        }

        return interfaceTypes;
    }

    public IMethodInfo GetMethod(string name)
    {
        return GetMethod(name, DefaultBinding);
    }

    public IMethodInfo GetMethod(string name, BindingFlags binding)
    {
        var mi = _type.GetMethod(name, binding);

        if (mi != null)
        {
            return new MethodInfoAdapter(mi);
        }

        return null;
    }

    public IMethodInfo[] GetMethods()
    {
        return GetMethods(DefaultBinding);
    }

    public IMethodInfo[] GetMethods(BindingFlags binding)
    {
        var unadaptedMethods = _type.GetMethods(binding);
        var totalMethods = unadaptedMethods.Length;
        IMethodInfo[] methods = new IMethodInfo[totalMethods];

        for (int i = 0; i < totalMethods; i++)
        {
            methods[i] = new MethodInfoAdapter(unadaptedMethods[i]);
        }

        return methods;
    }

    public IPropertyInfo[] GetProperties()
    {
        return GetProperties(DefaultBinding);
    }

    public IPropertyInfo[] GetProperties(BindingFlags binding)
    {
        var unadaptedProperties = _type.GetProperties(binding);
        var totalProperties = unadaptedProperties.Length;
        IPropertyInfo[] properties = new IPropertyInfo[totalProperties];

        for (int i = 0; i < totalProperties; i++)
        {
            properties[i] = new PropertyInfoAdapter(unadaptedProperties[i]);
        }

        return properties;
    }

    public IPropertyInfo GetProperty(string name, BindingFlags binding)
    {
        var propertyInfo = _type.GetProperty(name, binding);

        if (propertyInfo != null)
        {
            return new PropertyInfoAdapter(propertyInfo);
        }

        return null;
    }

    public bool IsSubclassOf(Type otherType)
    {
        return _type.IsSubclassOf(otherType);
    }

    public bool IsValueType => _type.IsValueType;

    public object[] GetCustomAttributes(bool inherit)
    {
        return _type.GetCustomAttributes(inherit);
    }

    public object[] GetCustomAttributes(Type attributeType, bool inherit)
    {
        return _type.GetCustomAttributes(attributeType, inherit);
    }

    public bool IsDefined(Type attributeType, bool inherit)
    {
        return _type.IsDefined(attributeType, inherit);
    }

    public bool IsAssignableFrom(IType type)
    {
        var t = Type.GetType(type.AssemblyQualifiedName);
        var b = _type.IsAssignableFrom(t);
        return b;
    }

    public new Type GetType()
    {
        return _type.GetType();
    }
}