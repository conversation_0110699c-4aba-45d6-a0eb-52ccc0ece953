using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Ui.AdHoc;

public class AdHocImportRepository : TrackingGenericRepository<AdHocImport>, IAdHocImportRepository
{
    protected readonly IMapper _mapper;

    public AdHocImportRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<AdHocImport> GetById(int id)
    {
        AdHocImport? adHocImport = null;
        adHocImport = await context.Set<AdHocImport>()
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
        return adHocImport!;
    }

    public async Task<IEnumerable<AdHocImportModel>> GetForList()
    {
        var query = context.Set<AdHocImport>()
            .Include(i => i.AdHocImportContracts)
            .Where(i => i.AdHocImportStatusType == AdHocImportStatusType.New)
            .OrderByDescending(i => i.Id)
            .AsNoTracking();

        return await _mapper.ProjectTo<AdHocImportModel>(query).ToListAsync();
    }

    public async Task<AdHocImport> GetForEnqueue(int id)
    {
        AdHocImport? adHocImport = null;
        adHocImport = await context.Set<AdHocImport>()
            .Include(i => i.AdHocImportContracts)
                .ThenInclude(i => i.Contract)
                    .ThenInclude(c => c.ContractVersionsInternal)
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
        return adHocImport!;
    }
}
