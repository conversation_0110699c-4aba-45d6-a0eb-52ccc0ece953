﻿using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;
internal interface ISubstanceRepository
{
    /// <summary>
    /// Gets the specified substance.
    /// </summary>
    /// <param name="id">The substance identifier.</param>
    /// <summary>Throws exception is not found</summary>
    /// <returns></returns>
    Task<Substance> Get(int id);
}
