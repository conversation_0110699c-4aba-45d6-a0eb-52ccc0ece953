﻿using PharmaLex.VigiLit.ImportManagement.Client;
using PharmaLex.VigiLit.ImportManagement.Service.Services;

namespace PharmaLex.VigiLit.ImportManagement.Service.Clients;

public class ImportManagementClient : IImportManagementClient // This will become the same as "AiReferencePublisher" when moved to use Service Bus
{
    private readonly IEnqueueReferenceCommandHandler _enqueueHandler;
    private readonly IManualCorrectionCommandHandler _correctionHandler;

    public ImportManagementClient(IEnqueueReferenceCommandHandler enqueueHandler, IManualCorrectionCommandHandler correctionHandler)
    {
        _enqueueHandler = enqueueHandler;
        _correctionHandler = correctionHandler;
    }

    public async Task Send(EnqueueReferenceCommand command)
    {
        await _enqueueHandler.Consume(command);
    }

    public async Task Send(ManualCorrectionCommand command)
    {
        await _correctionHandler.Consume(command);
    }
}
