﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage;
using PharmaLex.BlobStorage.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Infrastructure.Document.Case;
using PharmaLex.VigiLit.Infrastructure.Emails;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Utilities;
using SendGrid.Extensions.DependencyInjection;

namespace PharmaLex.VigiLit.Infrastructure;

public static class ConfigureServices
{
    public static void AddEmailServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<EmailOptions>(configuration.GetSection(EmailOptions.EmailSettings));

        services.AddSendGrid(options => options.ApiKey = configuration.GetValue<string>("SendGridApiKey"));

        services.AddScoped<IEmailSender, SendGridSender>();
        services.AddScoped<IEmailLogService, EmailLogService>();
        services.AddScoped<IEmailService, EmailService>();
        services.AddScoped<ICaseFilesEmailService, CaseFilesEmailService>();
        services.AddScoped<IEmailMaintenanceService, EmailMaintenanceService>();
        services.AddScoped<IPubMedUrlRenderHelper, PubMedUrlRenderHelper>();
        services.AddScoped<IUrlRenderHelper, UrlRenderHelper>();
    }

    public static void AddDocumentServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterDocumentBlobServices(configuration);

        services.AddCaseDocumentServices(configuration);

        services.AddSingleton<IEnumerable<AzureStorageDocumentOptions>>(sp =>
        {
            return new List<AzureStorageDocumentOptions>
            {
                sp.GetRequiredService<IOptions<AzureStorageCaseDocumentUploadOptions>>().Value,
                sp.GetRequiredService<IOptions<AzureStorageCaseDocumentOptions>>().Value,
            };
        });
    }

    private static void AddCaseDocumentServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<AzureStorageCaseDocumentUploadOptions>(configuration.GetSection(AzureStorageCaseDocumentUploadOptions.ConfigurationKey));
        services.Configure<AzureStorageCaseDocumentOptions>(configuration.GetSection(AzureStorageCaseDocumentOptions.ConfigurationKey));

        services.AddScoped<ICaseDocumentUploadService, CaseDocumentUploadService>();
        services.AddScoped<ICaseDocumentService, CaseDocumentService>();
    }
}