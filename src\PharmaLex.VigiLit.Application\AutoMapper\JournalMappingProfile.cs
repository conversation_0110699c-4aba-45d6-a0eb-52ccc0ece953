﻿using AutoMapper;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class JournalMappingProfile : Profile
{
    public JournalMappingProfile()
    {
        CreateMap<Journal, JournalViewModel>()
            .ForMember(dest => dest.CountryName, opt => opt.MapFrom(src => src.Country.Name)).ReverseMap();
        CreateMap<Journal, JournalModel>().ReverseMap();
    }
}