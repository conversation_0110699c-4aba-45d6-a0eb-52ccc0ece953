﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Search\**" />
    <EmbeddedResource Remove="Search\**" />
    <None Remove="Search\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.25.0" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.7" />
    <PackageReference Include="NewRelic.Agent.Api" Version="10.43.0" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="SendGrid.Extensions.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="PharmaLex.BlobStorage" Version="*********" />
  </ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
	</ItemGroup>

  <ItemGroup>
	  <InternalsVisibleTo Include="PharmaLex.VigiLit.Infrastructure.Unit.Tests" />
	  <InternalsVisibleTo Include="PharmaLex.VigiLit.Infrastructure.IntegrationTests" />
	  <InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
  </ItemGroup>

</Project>
