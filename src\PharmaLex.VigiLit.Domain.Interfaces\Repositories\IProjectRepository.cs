using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface IProjectRepository : ITrackingRepository<Project>
{
    Task<IEnumerable<Project>> GetAllAsync();
    Task<IEnumerable<Project>> GetByCompanyIdAsync(int companyId);
    Task<Project?> GetByIdAsync(int id);
    Task<Project?> GetByNameAndCompanyAsync(string name, int companyId);
}
