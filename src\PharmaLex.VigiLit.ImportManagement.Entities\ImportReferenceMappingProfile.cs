﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Client.Dto;

namespace PharmaLex.VigiLit.ImportManagement.Entities;
public class ImportReferenceMappingProfile : Profile
{
    public ImportReferenceMappingProfile()
    {
        CreateMap<ImportReference, Reference>()
            .ForMember(x => x.Id, opt => opt.Ignore())
            .ReverseMap();

        CreateMap<ReferenceDto, ImportReference>();
    }
}
