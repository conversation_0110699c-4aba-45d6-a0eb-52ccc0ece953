﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddIndexApogephaReport : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateIndex(
                name: "IX_ReferenceClassifications_CreatedDate",
                table: "ReferenceClassifications",
                column: "CreatedDate")
                .Annotation("SqlServer:Include", new[] { "ReferenceId", "SubstanceId", "ClassificationCategoryId" });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_ReferenceClassifications_CreatedDate",
                table: "ReferenceClassifications");
        }
    }
}
