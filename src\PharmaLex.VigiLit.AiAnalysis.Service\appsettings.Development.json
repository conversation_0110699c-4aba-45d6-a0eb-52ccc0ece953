{"Logging": {"LogLevel": {"Default": "Information", "Grpc": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "MessageBus": {"RetryPolicy": {"Count": 10, "Interval": "00:00:05", "Type": "Interval", "MaxInterval": "01:00:00"}, "TransportType": "AzureServiceBus", "RabbitMq": {"Host": "rabbitMq", "Port": 5672, "VirtualHost": "/", "Username": "fromSecrets", "Password": "fromSecrets"}, "AzureServiceBus": {"Namespace": "phcgv-nonprod-servicebus-eun", "ConnectionString": "Endpoint=sb://phcgv-nonprod-servicebus-eun.servicebus.windows.net"}}, "KeyVaultName": "vgt-dev-kv-eun", "ConnectionStrings": {"default": "fromSecrets"}, "AiEndpointSettings": {"Uri": "http://pharmalex-vigilit-api.northeurope.azurecontainer.io/process"}, "VisualStudioTenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77"}