﻿namespace PharmaLex.VigiLit.ImportManagement;

public class ImportProcessingParams
{
    public int ImportId { get; set; }
    public int ImportContractId { get; private set; }

    public ImportProcessingParams(int importId, int importContractId)
    {
        ImportId = importId;
        ImportContractId = importContractId;
    }

    public bool HasWorkToDo()
    {
        return ImportId > 0 || ImportContractId > 0;
    }
}