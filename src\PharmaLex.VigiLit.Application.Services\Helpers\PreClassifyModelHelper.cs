﻿using Microsoft.FeatureManagement;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.Services.Helpers;

internal class PreClassifyModelHelper : IPreClassifyModelHelper
{
    private readonly IClassificationCategoryService _classificationCategoryService;
    private readonly ICountryService _countryService;
    private readonly IClassificationService _classificationService;
    private readonly IFeatureManager _featureManager;
    private const string DisplayAiSuggestions = "DisplayAiSuggestions";

    public PreClassifyModelHelper(
        IClassificationCategoryService classificationCategoryService,
        ICountryService countryService,
        IClassificationService classificationService,
        IFeatureManager featureManager
    )
    {
        _classificationCategoryService = classificationCategoryService;
        _countryService = countryService;
        _classificationService = classificationService;
        _featureManager = featureManager;
    }

    public async Task<PreclassifyModel> GetPreClassifyModel(ImportSelectionModel selectedImport,
        IEnumerable<PreclassifyReferenceModel> preClassifyReferenceModels)
    {
        var todoCount = await _classificationService.GetToDoCount(selectedImport.ImportId);
        var preClassifiedCount = await _classificationService.GetPreclassifiedCount(selectedImport.ImportId);
        var classificationCategories = await _classificationCategoryService.GetAllAsync();
        var countries = await _countryService.GetAllAsync();
        var displayAiSuggestion = await _featureManager.IsEnabledAsync(DisplayAiSuggestions);

        var preClassifyModel = new PreclassifyModel
        {
            SelectedImport = selectedImport,
            ClassificationCategories = classificationCategories,
            Countries = countries,
            TodoCount = todoCount,
            CompletedCount = preClassifiedCount,
            PreclassifyReferenceModels = preClassifyReferenceModels,
            DisplayAiSuggestions = displayAiSuggestion
        };

        return preClassifyModel;
    }
}