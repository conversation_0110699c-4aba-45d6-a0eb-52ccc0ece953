﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Infrastructure.Utilities;

public class PubMedUrlRenderHelper : IPubMedUrlRenderHelper
{
    private readonly ILogger<PubMedUrlRenderHelper> _logger;

    public PubMedUrlRenderHelper(ILoggerFactory loggerFactory)
    {
        _logger = loggerFactory.CreateLogger<PubMedUrlRenderHelper>();
    }

    public string GetAbstractAndUrlConcatenated(ReferenceModel referenceModel)
    {
        var input = referenceModel.Abstract;
        var pubmedId = referenceModel.SourceId;
        int length = 200;

        _logger.LogInformation("PubMedUrlRenderHelper returning abstract: {Abstract} with length: {Length} having sourceId:{SourceId}", input, input.Length, pubmedId);
        if (input.Length < length)
        {
            return $"{input} <a href=\"https://pubmed.ncbi.nlm.nih.gov/{pubmedId}\"  target= \"_blank\" rel=\"noopener\">[Source]</a> ";
        }
        else
        {
            int iLastSpace = input.LastIndexOf(' ', length);

            return $"{input.Substring(0, (iLastSpace > 0) ? iLastSpace : length).Trim()}...<a href=\"https://pubmed.ncbi.nlm.nih.gov/{pubmedId}\"  target= \"_blank\" rel=\"noopener\">[Source]</a> ";
        }
    }
}