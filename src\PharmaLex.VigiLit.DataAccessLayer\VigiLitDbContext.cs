﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;


namespace PharmaLex.VigiLit.DataAccessLayer;

public class VigiLitDbContext : PlxDbContext
{
    public VigiLitDbContext(DbContextOptions options, IUserContext userContext, IDbConnectionService sqlService)
        : base(options, userContext, sqlService)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        var currentDomain = AppDomain.CurrentDomain;
        ApplyInfrastructureConfiguration(modelBuilder, currentDomain);
        ApplyReportingConfigurations(modelBuilder, currentDomain);

        modelBuilder.ApplyUtcDateTimeConverter();
    }

    private static void ApplyInfrastructureConfiguration(ModelBuilder modelBuilder, AppDomain currentDomain)
    {
        currentDomain.Load("PharmaLex.VigiLit.Infrastructure.Data");
        var assembly = currentDomain.GetAssemblies()
            .First(x =>
            {
                var name = x.FullName?.Substring(0, x.FullName.IndexOf(','));
                return name == "PharmaLex.VigiLit.Infrastructure.Data";
            });


        modelBuilder.ApplyConfigurationsFromAssembly(assembly);
    }

    private static void ApplyReportingConfigurations(ModelBuilder modelBuilder, AppDomain currentDomain)
    {
        var assemblies = currentDomain.GetAssemblies()
            .Where(x =>
            {
                var name = x.FullName?.Substring(0, x.FullName.IndexOf(','));
                return name != null && name.StartsWith("PharmaLex.VigiLit.Reports.");
            });

        foreach (var reportAssembly in assemblies)
        {
            modelBuilder.ApplyConfigurationsFromAssembly(reportAssembly);
        }
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);

        //// EFCore defaults to AsSingleQuery. A warning is produced when multiple includes are used in a single query.
        //// This config converts that warning into an exception, forcing the developer to specify either AsSplitQuery or AsSingleQuery.
        //// Usually in this scenario AsSplitQuery is the correct choice to avoid cartesian explosion.
        //// Query splitting can be configured as the default behaviour but each query causes a network round-trip,
        //// which is a big drawback due to high latency in the cloud. A future EFCore version will combine the queries into one round-trip.
        //// How to do that: optionsBuilder.UseSqlServer(o => o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery));
        //// Background info: https://learn.microsoft.com/en-us/ef/core/querying/single-split-queries
        //// optionsBuilder.ConfigureWarnings(w => w.Throw(RelationalEventId.MultipleCollectionIncludeWarning));

        // ^ That quickly became a nightmare because this app is riddled with multiple includes.
        // So I'm configuring query splitting globally instead to see how well it works. -MW
        optionsBuilder.UseSqlServer(o => o.UseQuerySplittingBehavior(QuerySplittingBehavior.SplitQuery));

        // Configuring `MigrationsAssembly` method specifies the assembly where EF Core should look for and store migrations.
        // In this case, migrations are stored in the "PharmaLex.VigiLit.Infrastructure.Data" assembly.
        // This allows the separation of migrations into a different project/assembly, which can be useful for
        // modularizing the application or separating concerns, such as keeping the migrations in an infrastructure layer.
        optionsBuilder.UseSqlServer(o => o.MigrationsAssembly("PharmaLex.VigiLit.Infrastructure.Data"));


#if DEBUG
        // Extra detail in error messages, useful when debugging.
        optionsBuilder.EnableSensitiveDataLogging(true);
#endif
    }
}