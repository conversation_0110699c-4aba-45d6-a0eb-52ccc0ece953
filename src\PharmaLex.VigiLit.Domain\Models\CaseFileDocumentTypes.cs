﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.Domain.Models;

public class CaseFileDocumentTypes : VigiLitEntityBase
{
    [Required, <PERSON><PERSON>ength(256)]
    public string Name { get; set; }

    [Required, <PERSON><PERSON>ength(10)]
    public string Extension { get; set; }
        
    [Required]
    public int MaxFileSizeMb { get; set; }
}