﻿using FuzzySharp;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class JournalTitleChecker : IExtractionValidator
{
    private List<string> _journalTitles = new();
    private readonly IJournalRepository _journalRepository;
    private readonly int _fuzzySharpJournalMatchThreshold;
    private readonly ILogger<JournalTitleChecker> _logger;
    private Task? _loadingTask;

    public JournalTitleChecker(ILogger<JournalTitleChecker> logger, IJournalRepository journalRepository, IConfiguration configuration)
    {
        _fuzzySharpJournalMatchThreshold = configuration.GetValue<int>("DataExtraction:FuzzySharpJournalMatchThreshold");
        _journalRepository = journalRepository;
        _logger = logger;
    }

    public bool IsValid(ExtractedReference extractedReference)
    {
        JournalTitleInitialised();
        var journalTitle = extractedReference.JournalTitle.Value;
        _logger.LogInformation("Extracted Journal Title:{JournalTitle}", journalTitle);
        if (_journalTitles.Count == 0)
        {
            _logger.LogWarning("JournalTitle count is zero");
            return true;
        }
        var journalTitleUpper = journalTitle.ToUpper();
        var journalTitlesUpper = _journalTitles!.Select(x => x.ToUpper());

        var fuzzyMatchSuccess =
            journalTitlesUpper.Any(x => Fuzz.Ratio(journalTitleUpper, x) > _fuzzySharpJournalMatchThreshold);

        if (!fuzzyMatchSuccess)
        {
            _logger.LogWarning("Journal title: {JournalTitle} could not be matched.", journalTitle);
        }

        return fuzzyMatchSuccess;
    }

    private void JournalTitleInitialised()
    {
        if (_journalTitles.Count > 0)
            return;
        _loadingTask ??= PopulateJournalTitles();
        _loadingTask.GetAwaiter().GetResult();

    }

    private async Task PopulateJournalTitles()
    {
        _journalTitles = (await _journalRepository.GetNames()).ToList();
    }
}