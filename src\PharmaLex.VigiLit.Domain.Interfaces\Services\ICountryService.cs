﻿using PharmaLex.VigiLit.Ui.ViewModels.Countries;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface ICountryService
{
    /// <summary>
    /// Returns list of all countries in the database as a list of strings
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<string>> GetAllAsync();
    
    /// <summary>
    /// Returns list of all countries in the database with full details
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<CountryModel>> GetAllWithDetailsAsync();
}