﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <PropertyGroup>
	  <NoWarn>S1192</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="SQLScripts\Client Reports\**" />
    <EmbeddedResource Remove="SQLScripts\Client Reports\**" />
    <None Remove="SQLScripts\Client Reports\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="SQLScripts\Add-ISOCountries.sql" />
    <None Remove="SQLScripts\Initial-CreateEmailPreferences.sql" />
    <None Remove="SQLScripts\Initial-CreateFeatureFlags.sql" />
    <None Remove="SQLScripts\Initial-MonitoringSP.sql" />
    <None Remove="SQLScripts\spGetAiSuggestedClassification.sql" />
    <None Remove="SQLScripts\spGetAiSuggestedClassification_1.sql" />
    <None Remove="SQLScripts\spGetAiSuggestedClassification_2.sql" />
    <None Remove="SQLScripts\Update-ClassificationCategory.sql" />
    <None Remove="SQLScripts\Update-ContractStartDate.sql" />
    <None Remove="SQLScripts\Update-CountryNameCorrections.sql" />
    <None Remove="SQLScripts\Update-ReportClaims.sql" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.7" />
    <PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.267" />
    <PackageReference Include="PharmaLex.FeatureManagement.Entities" Version="*********" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.UserSessionManagement.Entities\PharmaLex.Core.UserSessionManagement.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.DataAccessLayer\PharmaLex.VigiLit.DataAccessLayer.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.AiAnalysis.Entities\PharmaLex.VigiLit.AiAnalysis.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Entities\PharmaLex.VigiLit.DataExtraction.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Entities\PharmaLex.VigiLit.ImportManagement.Entities.csproj" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="SQLScripts\Initial-CreateFeatureFlags.sql">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="SQLScripts\spGetAiSuggestedClassification_1.sql" />
    <EmbeddedResource Include="SQLScripts\spGetAiSuggestedClassification_3.sql" />
    <EmbeddedResource Include="SQLScripts\spGetAiSuggestedClassification_2.sql" />
    <EmbeddedResource Include="SQLScripts\Update-ClassificationCategory.sql">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </EmbeddedResource>
    <EmbeddedResource Include="SQLScripts\Update-ContractStartDate.sql" />
    <EmbeddedResource Include="SQLScripts\Update-CountryNameCorrections.sql" />
    <EmbeddedResource Include="SQLScripts\spCaseFileReport.sql" />
    <EmbeddedResource Include="SQLScripts\spApogephaClassificationReport.sql" />
    <EmbeddedResource Include="SQLScripts\spMerzClassificationReport.sql" />
    <EmbeddedResource Include="SQLScripts\Add-ISOCountries.sql" />
    <EmbeddedResource Include="SQLScripts\spTrackingSheetReport.sql" />
    <EmbeddedResource Include="SQLScripts\Initial-CreateCountries.sql" />
    <EmbeddedResource Include="SQLScripts\Initial-CreateClassificationCategories.sql" />
    <EmbeddedResource Include="SQLScripts\Initial-CreateEmailPreferences.sql" />
    <EmbeddedResource Include="SQLScripts\Initial-CreateUsers.sql" />
    <EmbeddedResource Include="SQLScripts\Initial-CreateClaims.sql" />
    <EmbeddedResource Include="SQLScripts\Initial-MonitoringSP.sql" />
    <EmbeddedResource Include="SQLScripts\Update-ReportClaims.sql" />
  </ItemGroup>
</Project>