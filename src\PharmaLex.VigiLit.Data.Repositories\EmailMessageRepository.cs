using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Data.Repositories;


public class EmailMessageRepository : TrackingGenericRepository<EmailMessage>, IEmailMessageRepository
{
    private readonly IMapper _mapper;

    public EmailMessageRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<EmailMessageModel>> GetForEmailMessageLog(int emailId)
    {
        var query = context.Set<EmailMessage>()
            .Include(e => e.User)
            .Include(e => e.Company)
            .Where(e => e.EmailId == emailId)
            .OrderBy(e => e.Id)
            .AsNoTracking();

        return await _mapper.ProjectTo<EmailMessageModel>(query).ToListAsync();
    }

    public async Task<EmailMessage?> GetEmailMessage(int emailMessageId)
    {
        var query = context.Set<EmailMessage>()
            .Include(e => e.Email)
            .Where(e => e.Id == emailMessageId)
            .AsNoTracking();
        return await query.FirstOrDefaultAsync();
    }
}
