﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;
public class FailedImportFileConfiguration : EntityBaseMap<FailedImportFile>
{
    public override void Configure(EntityTypeBuilder<FailedImportFile> builder)
    {
        base.Configure(builder);
        builder.ToTable("FailedImportFiles");
        builder.Property(e => e.Doi)
               .HasMaxLength(250);
        builder.Property(e => e.DocumentLocation)
              .HasMaxLength(2000);
        builder.Property(c => c.CountryMatched)
           .HasMaxLength(128);
        builder.Property(e => e.Status)
       .IsRequired()
       .HasDefaultValue((FailedImportStatusType)10);
    }
}
