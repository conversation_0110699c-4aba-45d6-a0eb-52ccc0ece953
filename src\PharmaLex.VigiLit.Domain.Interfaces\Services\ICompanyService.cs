using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface ICompanyService
{
    Task<IEnumerable<CompanyModel>> GetAllAsync();
    Task AddAsync(CompanyModel model);
    Task UpdateAsync(CompanyModel model);
    Task<CompanyModel> GetByIdAsync(int id);
    Task<bool> ValidateNameAsync(string name, int id);
    Task<CompanyWithContractsModel> GetCompanyWithContracts(int companyId);
    Task<CompanyWithProjectsModel> GetCompanyWithProjects(int companyId);
    Task<CompanyUserListModel> GetCompanyUsers(int companyId);
    Task<IEnumerable<CompanyEmailModel>> GetActiveCompaniesWithUsers();
    Task<IEnumerable<CompanyModel>> GetCompaniesForSubstanceAsync(int substanceId);
    Task<IEnumerable<CompanyItemModel>> GetForSearch(User user);
    Task<IEnumerable<CompanyModel>> GetActiveCompaniesWithActiveContractsForSubstance(int substanceId);
}