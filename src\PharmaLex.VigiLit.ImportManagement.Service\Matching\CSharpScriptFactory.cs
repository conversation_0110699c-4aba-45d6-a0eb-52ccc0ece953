﻿using Microsoft.CodeAnalysis.CSharp.Scripting;
using Microsoft.CodeAnalysis.Scripting;
using Microsoft.CodeAnalysis.Scripting.Hosting;

namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;
internal class CSharpScriptFactory : ICSharpScriptFactory
{
    public Script<bool> Create(string code, ScriptOptions? options = null, Type? globalsType = null,
        InteractiveAssemblyLoader? assemblyLoader = null)
    {
        var script = CSharpScript.Create<bool>(
            code,
            options,
            globalsType: typeof(IAbstractTextSearcher)
        );
        return script;
    }
}
