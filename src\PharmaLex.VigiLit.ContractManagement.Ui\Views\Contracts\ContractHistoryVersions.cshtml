@using Microsoft.AspNetCore.Authorization
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Domain.UserManagement

@inject IAuthorizationService AuthorizationService


@model PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.ContractDetailsModel

@{
    ViewData["Title"] = "Contracts History";
}


<div id="contractDetails" v-cloak>
    <div class="sub-header">
        <h2>Contract History</h2>
        <div class="controls">
            <a class="button btn-default" :href="'/contracts/PrintPreview/'+contractDetails.id" target="_blank">Print Preview</a>
            <a class="button" onclick="history.back()">Back</a>
        </div>
    </div>
    <div v-cloak  class="flex gapped">
        @Html.AntiForgeryToken()
        <div class="flex-item flex-20-percent p-2">
            <div class="tile">
                <h3>Contract Overview</h3>
                <div class="form-group">
                    <div class="contractHistoryLabel">ID</div>
                    <p>{{contractDetails.id}}</p>
                </div>
                <div class="form-group">
                    <div class="contractHistoryLabel">Project Name</div>
                    <p>{{contractDetails.projectName}}</p>
                </div>
                <div class="form-group">
                    <div class="contractHistoryLabel">Substance</div>
                    <p>{{contractDetails.substanceName}}</p>
                </div>
                <div class="form-group">
                    <div class="contractHistoryLabel">Contract Start Date</div>
                    @if (Model.ContractStartDate < new DateTime(1901, 1, 1))
                    {
                        <p>Not Started</p>
                    }
                    else
                    {
                        <p>{{getDateFormat(contractDetails.contractStartDate)}}</p>
                    }
                    
                </div>
            </div>
        </div>
        <div class="flex-item flex-80-percent">
            <contract-history-display :contract-versions="contractVersions">
            </contract-history-display>
        </div>
    </div>
</div>


<script type="text/javascript">
    let model = @Html.Raw(AntiXss.ToJson(Model));

    var pageConfig = {
        appElement: "#contractDetails",
        data: function () {
            return {
                contractDetails: model,
                contractVersions: model.contractsVersions
            }
        },
        methods: {
            getDateFormat: function (date) {
                return moment.utc(date).format('DD MMM YYYY');
            }
        }
    }
</script>
@section VueComponentScripts{
    <partial name="Components/ContractHistoryDisplay" />
}