﻿using PharmaLex.VigiLit.Application.Services.Helpers;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.Services.Comparers
{
    /// <summary>
    /// Comparer used for sorting PreclassifyReferenceModels.
    /// </summary>
    /// <seealso cref="System.Collections.Generic.IComparer&lt;VigiLit.Ui.ViewModels.ReferenceModule.PreclassifyReferenceModel&gt;" />
    public class PreclassifyReferenceModelComparer : IComparer<PreclassifyReferenceModel>
    {
        /// <summary>
        /// Compares the specified x.
        /// </summary>
        /// <remarks>
        /// The rules are the comparer are follows:
        /// x is considered less than y if:
        /// The Reference State of the ReferenceClassification is "New" for x and any other value for y.
        /// If both are "New" then the DateRevised of the Reference is compared with a more recent date being considered less
        /// Non-"New" items with an Update are considered to be less than non-"New" items without an update
        /// Updates with a more recent DateRevised are considered to be less
        /// </remarks>
        /// <param name="x">PreclassifyReferenceModel</param>
        /// <param name="y">PreclassifyReferenceModel</param>
        /// <returns>
        /// A signed integer that indicates the relative values of x and y:
        /// - If less than 0, x is less than y.
        /// - If 0, x equals y.
        /// - If greater than 0, x is greater than y.
        /// </returns>
        public int Compare(PreclassifyReferenceModel? x, PreclassifyReferenceModel? y)
        {
            if (TryCompareByValueNullsLast(x, y, out var compare))
            {
                return compare;
            }

            Guard.AgainstNull(x, "x");
            Guard.AgainstNull(y, "y");

            if (TryCompareByReferenceStateNewFirst(x, y, out var referenceStateCompare))
            {
                return referenceStateCompare;
            }

            if (TryCompareNewReferenceStateByDateRevisedDescendingOrder(x, y, out var dateRevisedCompare))
            {
                return dateRevisedCompare;
            }

            if (TryCompareByReferenceUpdateDateRevised(x, y, out var dateCompare))
            {
                return dateCompare;
            }

            return CompareByDateRevised(x, y);
        }

        private static bool TryCompareByValueNullsLast(PreclassifyReferenceModel? x, PreclassifyReferenceModel? y, out int compare)
        {
            if (x == null && y == null)
            {
                compare = 0;
                return true;
            }
            
            if (x == null)
            {
                compare = -1;
                return true;
            }
            
            if (y == null)
            {
                compare = 1;
                return true;
            }

            compare = 0; 
            return false;
        }

        private static bool TryCompareByReferenceUpdateDateRevised(PreclassifyReferenceModel x, PreclassifyReferenceModel y, out int compare)
        {
            if (x.ReferenceUpdate != null && y.ReferenceUpdate == null)
            {
                compare = -1;
                return true;
            }

            if (x.ReferenceUpdate == null && y.ReferenceUpdate != null)
            {
                compare = 1;
                return true;
            }

            if (x.ReferenceUpdate != null && y.ReferenceUpdate != null)
            {
                int dateComparison = y.ReferenceUpdate.DateRevised.CompareTo(x.ReferenceUpdate.DateRevised);
                if (dateComparison != 0)
                {
                    compare = dateComparison;
                    return true;
                }
            }

            compare = 0;
            return false;
        }

        private static bool TryCompareByReferenceStateNewFirst(PreclassifyReferenceModel x, PreclassifyReferenceModel y, out int compare)
        {
            if (x.ReferenceClassification.ReferenceState == (int)ReferenceState.New &&
                y.ReferenceClassification.ReferenceState != (int)ReferenceState.New)
            {
                compare = -1;
                return true;
            }

            if (x.ReferenceClassification.ReferenceState != (int)ReferenceState.New &&
                     y.ReferenceClassification.ReferenceState == (int)ReferenceState.New)
            {
                compare = 1;
                return true;
            }

            compare = 0;
            return false;
        }

        private static bool TryCompareNewReferenceStateByDateRevisedDescendingOrder(PreclassifyReferenceModel x, PreclassifyReferenceModel y, out int dateRevisedCompare)
        {
            if (x.ReferenceClassification.ReferenceState == (int)ReferenceState.New)
            {
                int dateComparison = y.ReferenceClassification.Reference.DateRevised.CompareTo(x.ReferenceClassification.Reference.DateRevised);
                if (dateComparison != 0)
                {
                    dateRevisedCompare = dateComparison;
                    return true;
                }
            }

            dateRevisedCompare = 0;
            return false;
        }

        private static int CompareByDateRevised(PreclassifyReferenceModel x, PreclassifyReferenceModel y)
        {
            return y.ReferenceClassification.Reference.DateRevised.CompareTo(x.ReferenceClassification.Reference.DateRevised);
        }
    }
}