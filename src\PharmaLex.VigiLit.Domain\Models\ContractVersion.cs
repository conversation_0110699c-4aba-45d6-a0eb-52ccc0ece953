using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.UserManagement;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class ContractVersion : VigiLitEntityBase
{
    protected ContractVersion()
    {

    }

    public ContractVersion(Contract contract, string searchString)
    {
        if (contract == null)
        {
            throw new ArgumentException($"Contract : {nameof(contract)}");
        }
        if (string.IsNullOrWhiteSpace(searchString))
        {
            throw new ArgumentException($"Search string: {nameof(searchString)}");
        }
        Contract = contract;
        SearchString = searchString;
    }

    public int ContractId { get; private set; }
    public ContractVersionStatus ContractVersionStatus { get; private set; } = ContractVersionStatus.New;
    public bool IsActive { get; set; }
    public ContractType ContractType { get; set; }
    public ContractWeekday ContractWeekday { get; set; }
    public int SearchPeriodDays { get; set; }

    public string SearchString { get; set; }

    public int Version { get; set; }

    public string ReasonForChange { get; set; }

    public int UserId { get; set; }
    public User User { get; set; }

    public DateTime TimeStamp { get; set; }

    public Contract Contract { get; private set; }

    public ICollection<ContractVersionJournal> ContractVersionJournals { get; set; } = new List<ContractVersionJournal>();

    public void SetContractVersion(ContractVersionStatus status )
    {
        ContractVersionStatus = status;
    }
    
    public void SetContractDetails(ContractVersionStatus status,Contract contract, int contractId)
    {
        ContractVersionStatus = status;
        Contract = contract;
        ContractId = contractId;
    }
}
