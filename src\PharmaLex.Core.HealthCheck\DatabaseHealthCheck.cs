﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.HealthCheck;

public class DatabaseHealthCheck<T> : IHealthCheck
    where T : PlxDbContext
{
    private const int _timeoutMilliseconds = 1000 * 60;

    private readonly T _dbContext;
    private readonly ILogger<DatabaseHealthCheck<T>> _logger;

    public DatabaseHealthCheck(T dbContext, ILoggerFactory loggerFactory)
    {
        _dbContext = dbContext;
        _logger = loggerFactory.CreateLogger<DatabaseHealthCheck<T>>();
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        bool isHealthy;

        try
        {
            isHealthy = await IsReachableAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while validating database health.");
            isHealthy = false;
        }

        return await Task.FromResult(isHealthy ? HealthCheckResult.Healthy() : HealthCheckResult.Unhealthy());
    }

    private async Task<bool> IsReachableAsync()
    {
        var task = _dbContext.Database.CanConnectAsync();
        if (await Task.WhenAny(task, Task.Delay(_timeoutMilliseconds)) == task)
        {
            return await task;
        }

        throw new TimeoutException("Database health check request timed out.");
    }
}