﻿@using PharmaLex.VigiLit.Domain.Enums
<script type="text/x-template" id="reference-component-template">
    <div class="reference-group">
         <label>Title</label>
         <p v-if="title" v-html="title"></p>
         <p v-else>No information available.</p>
     </div>
     <div class="reference-group">
         <label v-html="sourceTextLabel"></label>
        <p v-html="abstract"></p>
     </div>
     <div class="reference-group">
         <label>Authors</label>
         <p v-if="authors" v-html="authors"></p>
         <p v-else>No information available.</p>
     </div>
     <div class="reference-group">
         <label>Mesh Terms</label>
         <p v-if="meshTerms" v-html="meshTerms"></p>
         <p v-else>No information available.</p>
     </div>
     <div class="reference-group">
         <label>Keywords</label>
         <p v-if="keywords" v-html="keywords"></p>
         <p v-else>No information available.</p>
     </div>
     <div class="reference-group">
         <label>Affiliation</label>
         <p v-if="affiliation" v-html="affiliation"></p>
         <p v-else>No information available.</p>
     </div>
</script>

<script type="text/javascript">
    vueApp.component('reference', {
        template: '#reference-component-template',
        props: {
            reference: {
                type: Object
            },
            referenceUpdate: {
                type: Object
            },
            referenceSnapshot: {
                type: Object 
            },
            substance: {
                type: Object
            }
        },
        computed: {
            sourceTextLabel() {
                return this.reference.sourceSystem == '@((Int16)SourceSystem.PubMed)' ? "Abstract" : "Match";
            },
            title() {
                return this.referenceUpdate ? this.compare(this.referenceUpdate?.title, this.referenceSnapshot?.title) : this.highlight(this.reference?.title);
            },
            abstract() {
                return this.referenceUpdate ? this.compare(this.referenceUpdate?.abstract, this.referenceSnapshot?.abstract) : this.highlight(this.reference?.abstract);
            },
            authors() {
                return this.referenceUpdate ? this.compare(this.referenceUpdate?.authors, this.referenceSnapshot?.authors) : this.reference?.authors;
            },
            meshTerms() {
                return this.referenceUpdate ? this.compare(this.referenceUpdate?.meshHeadings, this.referenceSnapshot?.meshHeadings) : this.highlight(this.reference?.meshHeadings);
            },
            keywords() {
                return this.referenceUpdate ? this.compare(this.referenceUpdate?.keywords, this.referenceSnapshot?.keywords) : this.highlight(this.reference?.keywords);
            },
            affiliation() {
                return this.referenceUpdate ? this.compare(this.referenceUpdate?.affiliationTextFirstAuthor, this.referenceSnapshot?.affiliationTextFirstAuthor) : this.reference?.affiliationTextFirstAuthor;
            }
        },
        methods: {
            compare: function (newValue, oldValue) {

                if (newValue === null) newValue = '';
                if (oldValue === null) oldValue = '';

                let synonyms = this.getSynonyms();
                const diff = Diff.diffWords(oldValue, newValue, { ignoreWhitespace: false });
                const fragment = document.createDocumentFragment();

                diff.forEach((part) => {
                    let span = document.createElement('span');
                    if (part.added || part.removed) {
                        // green for additions, red for deletions
                        // grey for common parts
                        const color = part.added ? 'green' :
                            part.removed ? '#ce0000' : '';

                        const background = part.added ? '#ddf8dd' :
                            part.removed ? '#ffdbdb' : '';

                        span.style.color = color;
                        span.style.background = background;
                        span.style.textDecoration = part.removed ? 'line-through' : '';
                    } else if (synonyms.length > 0) {
                        synonyms.forEach((synonym) => {
                            var matchingSynonym = new RegExp("\\b" + this.escapeRegexSpecialCharacter(synonym) + "\\b", "ig");
                            part.value = part.value.toString().replace(matchingSynonym, function (highlightText) {
                                return ('<span style="background:yellow;">' + highlightText + '</span>');
                            });
                        });
                    }
                    span.innerHTML = part.value;
                    fragment.appendChild(span);
                });

                // hack to return the html string we just built, so it can go into v-html.
                let div=document.createElement("div");
                div.appendChild(fragment);
                return div.innerHTML == "<span></span>" ? null : div.innerHTML;
            },
            highlight: function (part) {
                let synonyms = this.getSynonyms();
                if(synonyms.length > 0 && part != null){
                    synonyms.forEach((synonym) => {
                        var matchingSynonym = new RegExp("\\b" + this.escapeRegexSpecialCharacter(synonym) + "\\b", "ig");
                        part = part.toString().replace(matchingSynonym, function (highlightText) {
                            return ('<span style="background:yellow;">' + highlightText + '</span>');
                        });
                    });
                }
                return part;
            },
            getSynonyms: function(){
                let synonyms = [];
                var substanceName = this.substance?.name;
                var substanceSynonymsObject = this.substance?.substanceSynonyms;

                if (substanceSynonymsObject != null) {
                    synonyms = substanceSynonymsObject.map(s => s.name);
                }
                synonyms.push(substanceName);
                return synonyms;
            },
            // Escape a character that has special meaning inside a regular expression to allow adding it to synonyms.
            escapeRegexSpecialCharacter(text) {
                if (text) {
                    return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&');
                }
            }
        }
    });
</script>
