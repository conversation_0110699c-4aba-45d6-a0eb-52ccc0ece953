using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class EmailMessageMappingProfile : Profile
{
    public EmailMessageMappingProfile()
    {
        CreateMap<EmailMessage, EmailMessageModel>()
            .ForMember(d => d.CompanyName, s => s.MapFrom(x => x.Company.Name))
            .ForMember(d => d.RecipientName, s => s.MapFrom(x => $"{x.User.GivenName} {x.User.FamilyName}"))
            .ForMember(d => d.RecipientEmailAddress, s => s.MapFrom(x => x.EmailAddress))
            .ForMember(d => d.Status, s => s.MapFrom(x => x.EmailMessageStatusType.GetDescription()))
            .ForMember(d => d.Timestamp, s => s.MapFrom(x => x.Timestamp.ToString("dd MMM yyyy HH:mm:ss")));
    }
}
