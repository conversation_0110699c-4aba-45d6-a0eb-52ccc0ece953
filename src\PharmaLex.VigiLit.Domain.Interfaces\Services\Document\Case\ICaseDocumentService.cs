﻿using PharmaLex.VigiLit.Domain.Models.Document.Case;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;

public interface ICaseDocumentService
{
    Task<bool> Exists(CaseDocumentDescriptor caseDocumentDescriptor, CancellationToken cancellationToken = default);
    Task<Stream> OpenRead(CaseDocumentDescriptor caseDocumentDescriptor, CancellationToken cancellationToken = default);
    Task Delete(CaseDocumentDescriptor caseDocumentDescriptor, CancellationToken cancellationToken = default);
}