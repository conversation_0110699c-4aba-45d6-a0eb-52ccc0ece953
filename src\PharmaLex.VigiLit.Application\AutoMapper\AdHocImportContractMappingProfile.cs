using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class AdHocImportContractMappingProfile : Profile
{
    public AdHocImportContractMappingProfile()
    {
        CreateMap<AdHocImportContract, AdHocImportContractModel>()
            .ForMember(i => i.CompanyName, s => s.MapFrom(c => c.Contract.Project.Company.Name))
            .ForMember(i => i.ProjectName, s => s.MapFrom(c => c.Contract.Project.Name))
            .ForMember(i => i.SubstanceName, s => s.MapFrom(c => c.Contract.Substance.Name))
            .ForMember(i => i.ContractType, s => s.MapFrom(c => c.Contract.GetCurrentContractVersion().ContractType.GetDescription()))
            .ForMember(i => i.SearchPeriod, s => s.MapFrom(c => c.Contract.GetCurrentContractVersion().SearchPeriodDays == 0 ? "Unlimited" : string.Format("{0} days",
                       c.Contract.GetCurrentContractVersion().SearchPeriodDays)));
    }
}