using PharmaLex.Dotnet.Adapters.Reflection;
using System.Reflection;

namespace PharmaLex.Dotnet.Adapters;

public interface IType : ICustomAttributeProvider
{
    /// <summary>
    /// Gets the assembly.
    /// </summary>
    /// <value>The assembly.</value>
    IAssembly Assembly { get; }

    /// <summary>
    /// Gets the assembly qualified name of type.
    /// </summary>
    /// <value>The assembly qualified name of type.</value>
    string AssemblyQualifiedName { get; }

    /// <summary>
    /// Gets the full name.
    /// </summary>
    /// <value>The full name.</value>
    string FullName { get; }

    /// <summary>
    /// Gets a value indicating whether this instance is abstract.
    /// </summary>
    /// <value>
    /// 	<c>true</c> if this instance is abstract; otherwise, <c>false</c>.
    /// </value>
    bool IsAbstract { get; }

    /// <summary>
    /// Gets a value indicating whether this instance is class.
    /// </summary>
    /// <value><c>true</c> if this instance is class; otherwise, <c>false</c>.</value>
    bool IsClass { get; }

    /// <summary>
    /// Gets a value indicating whether this instance is interface.
    /// </summary>
    /// <value>
    /// 	<c>true</c> if this instance is interface; otherwise, <c>false</c>.
    /// </value>
    bool IsInterface { get; }

    /// <Summary>
    ///     Gets a value indicating whether the System.Type is a value type.
    /// </Summary>
    /// <Returns>
    ///     true if the System.Type is a value type; otherwise, false.
    ///</Returns>
    bool IsValueType { get; }

    /// <summary>
    /// Gets the name.
    /// </summary>
    /// <value>The name.</value>
    string Name { get; }

    /// <summary>
    /// Gets the events.
    /// </summary>
    /// <returns></returns>
    IEventInfo[] GetEvents();

    /// <summary>
    /// Gets the events.
    /// </summary>
    /// <param name="binding">The binding flags.</param>
    /// <returns></returns>
    IEventInfo[] GetEvents(BindingFlags binding);

    /// <summary>
    /// Gets the interface.
    /// </summary>
    /// <param name="name">The name.</param>
    /// <returns></returns>
    IType GetInterface(string name);

    /// <summary>
    /// Gets the interfaces.
    /// </summary>
    /// <returns></returns>
    IType[] GetInterfaces();

    /// <summary>
    /// Gets the method.
    /// </summary>
    /// <param name="name">The name.</param>
    /// <returns></returns>
    IMethodInfo GetMethod(string name);

    /// <summary>
    /// Gets the method.
    /// </summary>
    /// <param name="name">The name.</param>
    /// <param name="binding">The binding flags.</param>
    /// <returns></returns>
    IMethodInfo GetMethod(string name, BindingFlags binding);

    /// <summary>
    /// Gets the methods.
    /// </summary>
    /// <returns></returns>
    IMethodInfo[] GetMethods();

    /// <summary>
    /// Gets the methods.
    /// </summary>
    /// <param name="binding">The binding flags.</param>
    /// <returns></returns>
    IMethodInfo[] GetMethods(BindingFlags binding);

    /// <summary>
    /// Gets the properties.
    /// </summary>
    /// <returns></returns>
    IPropertyInfo[] GetProperties();

    /// <summary>
    /// Gets the properties.
    /// </summary>
    /// <param name="binding">The binding flags.</param>
    /// <returns></returns>
    IPropertyInfo[] GetProperties(BindingFlags binding);

    /// <summary>
    /// Gets the property.
    /// </summary>
    /// <param name="name">The name.</param>
    /// <param name="binding">The binding flags.</param>
    /// <returns></returns>
    IPropertyInfo GetProperty(string name, BindingFlags binding);

    /// <summary>
    /// Determines whether this instance is subclass of the specified other type.
    /// </summary>
    /// <param name="otherType">Type of the other.</param>
    /// <returns>
    /// 	<c>true</c> if this instance is subclass of the specified other type; otherwise, <c>false</c>.
    /// </returns>
    bool IsSubclassOf(Type otherType);

    /// <summary>
    /// Determines whether the specified <see cref="System.Object"/> is equal to this instance.
    /// </summary>
    /// <param name="arg">The <see cref="System.Object"/> to compare with this instance.</param>
    /// <returns>
    /// 	<c>true</c> if the specified <see cref="System.Object"/> is equal to this instance; otherwise, <c>false</c>.
    /// </returns>
    bool Equals(object arg);

    bool IsAssignableFrom(IType type);

    Type GetType();
}