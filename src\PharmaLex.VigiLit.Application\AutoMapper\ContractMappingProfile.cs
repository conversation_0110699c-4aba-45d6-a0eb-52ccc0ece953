using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using System.Linq;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class ContractMappingProfile : Profile
{
    private const int DEFAULT_EXPORT_SEARCH_VERSION = 0;
    private readonly string DEFAULT_EXPORT_SEARCH_STRING = string.Empty;

    public ContractMappingProfile()
    {
        CreateMap<Contract, ContractModel>()
            .ForMember(d => d.ContractVersionCurrent, s => s.MapFrom(x => x.ContractVersionsInternal.FirstOrDefault(cs => cs.ContractVersionStatus == ContractVersionStatus.Approved)))
            .ForMember(d => d.ContractVersionPending, s => s.MapFrom(x => x.ContractVersionsInternal.FirstOrDefault(cs => cs.ContractVersionStatus != ContractVersionStatus.Approved && cs.ContractVersionStatus != ContractVersionStatus.History)));

        CreateMap<ContractModel, Contract>()
            .ForSourceMember(s => s.ContractVersionCurrent, opt => opt.DoNotValidate())
            .ForSourceMember(s => s.ContractVersionPending, opt => opt.DoNotValidate())
            .ForSourceMember(s => s.SubstanceId, opt => opt.DoNotValidate());


        CreateMap<Contract, SimpleContractModel>()
            .ForMember(c => c.CompanyName, s => s.MapFrom(c => c.Project.Company.Name))
            .ForMember(c => c.ProjectName, s => s.MapFrom(c => c.Project.Name))
            .ForMember(c => c.SubstanceName, s => s.MapFrom(c => c.Substance.Name))
            .ForMember(c => c.ContractStartDate, s => s.MapFrom(c => c.ContractStartDate))
            .ForMember(c => c.IsActive, s => s.MapFrom(c => c.GetCurrentContractVersion().IsActive))
            .ForMember(c => c.ContractType, s 
                => s.MapFrom(c => c.GetContractTypeDescription()))
            .ForMember(c => c.ContractWeekday, s => 
                s.MapFrom(c => c.GetWeekDayDescription()))
            .ForMember(c => c.SearchPeriod, s => 
                s.MapFrom(c => c.GetSearchPeriodDescription()));

        CreateMap<Contract, ExportContractModel>()
            .ForMember(c => c.CompanyName, s => s.MapFrom(c => c.Project.Company.Name))
            .ForMember(c => c.ProjectName, s => s.MapFrom(c => c.Project.Name))
            .ForMember(c => c.SubstanceName, s => s.MapFrom(c => c.Substance.Name))
            .ForMember(c => c.ContractType, s => s.MapFrom(c => c.GetCurrentContractVersion().ContractType.GetDescription()))
            .ForMember(c => c.ContractWeekday, s => s.MapFrom((c => c.GetCurrentContractVersion().ContractWeekday.GetDescription())))
            .ForMember(c => c.SearchPeriod, s => s.MapFrom((c => c.GetCurrentContractVersion().SearchPeriodDays == 0 ? "Unlimited" : string.Format("{0} days", c.GetCurrentContractVersion().SearchPeriodDays))))
            .ForMember(c => c.IsActive, s => s.MapFrom(c => c.GetCurrentContractVersion().IsActive))
            .ForMember(c => c.SearchVersion, s => s.MapFrom(c => c.ContractVersionsInternal.Count > 0 ?
                c.ContractVersionsInternal.OrderByDescending(x => x.Version).First().Version : DEFAULT_EXPORT_SEARCH_VERSION))
            .ForMember(c => c.SearchString, s => s.MapFrom(c => c.ContractVersionsInternal.Count > 0 ?
                c.ContractVersionsInternal.OrderByDescending(x => x.Version).First().SearchString : DEFAULT_EXPORT_SEARCH_STRING));

        CreateMap<Contract, ContractDetailsModel>()
            .ForMember(c => c.ProjectName, s => s.MapFrom(c => c.Project.Name))
            .ForMember(c => c.SubstanceName, s => s.MapFrom(c => c.Substance.Name))
            .ForMember(c => c.ContractStartDate, s => s.MapFrom(c => c.ContractStartDate))
            .ForMember(c => c.ContractType, s => s.MapFrom(c => c.GetCurrentContractVersion().ContractType.GetDescription()))
            .ForMember(c => c.ContractsVersions, s => s.MapFrom(c => c.ContractVersionsInternal));
    }
}
