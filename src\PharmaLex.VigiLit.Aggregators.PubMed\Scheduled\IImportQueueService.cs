﻿using PharmaLex.VigiLit.ImportManagement.Enums;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Scheduled;

/// <summary>
/// Interface for a service that can Enqueue scheduled or Ad-Hoc imports against an Indexed Reference Source.
/// </summary>
/// <remarks>
/// At present this is specifically for PubMed, but we may wish to move this in "PharmaLex.VigiLit.Aggregators.Core" when the next is implemented.
/// </remarks>
public interface IImportQueueService
{
    /// <summary>
    /// Enqueues the scheduled import.
    /// </summary>
    /// <param name="importTriggerType">Type of the import trigger.</param>
    /// <returns></returns>
    Task EnqueueScheduledImport(ImportTriggerType importTriggerType);

    /// <summary>
    /// Determines whether this instance can retry the specified import identifier.
    /// </summary>
    /// <param name="importId">The import identifier.</param>
    /// <returns></returns>
    Task<bool> CanRetry(int importId);

    /// <summary>
    /// Retries the specified import identifier.
    /// </summary>
    /// <param name="importId">The import identifier.</param>
    /// <returns></returns>
    Task Retry(int importId);

    /// <summary>
    /// Enqueues the ad hoc import.
    /// </summary>
    /// <param name="adHocImportId">The ad hoc import identifier.</param>
    /// <returns></returns>
    Task EnqueueAdHocImport(int adHocImportId);
}
