﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddMandatoryFieldConfidencePassCheckToFailedImportFileTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "AbstractConfidenceCheckPassed",
                table: "FailedImportFiles",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "CountryOfOccurrenceConfidenceCheckPassed",
                table: "FailedImportFiles",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "JournalTitleConfidenceCheckPassed",
                table: "FailedImportFiles",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "TitleConfidenceCheckPassed",
                table: "FailedImportFiles",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AbstractConfidenceCheckPassed",
                table: "FailedImportFiles");

            migrationBuilder.DropColumn(
                name: "CountryOfOccurrenceConfidenceCheckPassed",
                table: "FailedImportFiles");

            migrationBuilder.DropColumn(
                name: "JournalTitleConfidenceCheckPassed",
                table: "FailedImportFiles");

            migrationBuilder.DropColumn(
                name: "TitleConfidenceCheckPassed",
                table: "FailedImportFiles");
        }
    }
}
