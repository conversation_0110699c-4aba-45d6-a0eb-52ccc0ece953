﻿using PharmaLex.Core.UserManagement;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

/// <summary>
/// Interface for managing users.
/// </summary>
public interface IUserRepository : ITrackingRepository<User>, IUserRepository<User>
{
    /// <summary>
    /// Asynchronously retrieves all users.
    /// </summary>
    /// <param name="shouldHaveClaims">Optional parameter indicating whether users should include their claims.</param>
    /// <returns>A collection of user models.</returns>
    Task<IEnumerable<UserFullModel>> GetAllAsync(bool shouldHaveClaims = true);

    /// <summary>
    /// Asynchronously retrieves all assessors.
    /// </summary>
    /// <returns>A collection of assessor users.</returns>
    Task<IEnumerable<User>> GetAssessorsAsync();

    /// <summary>
    /// Asynchronously retrieves an assessor by their ID.
    /// </summary>
    /// <param name="id">The ID of the assessor to retrieve.</param>
    /// <returns>The assessor with the specified ID, if found; otherwise, null.</returns>
    Task<User?> GetAssessorAsync(int id);

    /// <summary>
    /// Asynchronously updates an assessor's details.
    /// </summary>
    /// <param name="user">The assessor to update.</param>
    /// <param name="substanceIdsToAdd">The IDs of substances to add to the assessor.</param>
    /// <param name="userSubstancesToRemove">The user-substance associations to remove from the assessor.</param>
    /// <param name="qualityCheckPercentage">The new quality check percentage for the assessor.</param>
    Task UpdateAssessorAsync(User user, IEnumerable<int> substanceIdsToAdd, IEnumerable<UserSubstance> userSubstancesToRemove, int qualityCheckPercentage);

    /// <summary>
    /// Asynchronously retrieves locks associated with users.
    /// </summary>
    /// <returns>A collection of user locks.</returns>
    Task<IEnumerable<UserLocksModel>> GetUserLocks();

    /// <summary>
    /// Asynchronously retrieves a user by their email address and company ID.
    /// </summary>
    /// <param name="email">The email address of the user to retrieve.</param>
    /// <param name="companyId">The ID of the company to which the user belongs.</param>
    /// <returns>The user with the specified email address and company ID, if found; otherwise, null.</returns>
    Task<User?> GetCompanyUserByEmailAsync(string email, int companyId);

    /// <summary>
    /// Asynchronously retrieves an inactive user by their email address.
    /// </summary>
    /// <param name="email">The email address of the inactive user to retrieve.</param>
    /// <returns>The inactive user with the specified email address, if found; otherwise, null.</returns>
    Task<User?> GetInactiveCompanyUserByEmailAsync(string email);
}
