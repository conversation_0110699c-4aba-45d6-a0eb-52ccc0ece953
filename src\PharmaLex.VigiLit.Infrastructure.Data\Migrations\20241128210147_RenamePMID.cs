﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class RenamePMID : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {



            migrationBuilder.RenameColumn(
                name: "PMID",
                table: "ReferenceUpdates",
                newName: "SourceId");

            migrationBuilder.Sql("ALTER TABLE [dbo].[References] SET (SYSTEM_VERSIONING = OFF);");
            migrationBuilder.Sql("EXEC sp_rename 'References.PMID', 'SourceId', 'COLUMN';");
            migrationBuilder.Sql("EXEC sp_rename 'ReferencesHistory.PMID', 'SourceId', 'COLUMN';");
            migrationBuilder.Sql("ALTER TABLE [dbo].[References] SET (SYSTEM_VERSIONING = ON (HISTORY_TABLE = dbo.ReferencesHistory, DATA_CONSISTENCY_CHECK = ON));");

            migrationBuilder.RenameIndex(
                name: "IX_References_PMID",
                table: "References",
                newName: "IX_References_SourceId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "SourceId",
                table: "ReferenceUpdates",
                newName: "PMID");

            migrationBuilder.Sql("ALTER TABLE References SET (SYSTEM_VERSIONING = OFF);");
            migrationBuilder.Sql("EXEC sp_rename 'References.SourceId', 'PMID', 'COLUMN';");
            migrationBuilder.Sql("EXEC sp_rename 'ReferencesHistory.SourceId', 'PMID', 'COLUMN';");
            migrationBuilder.Sql("ALTER TABLE References SET (SYSTEM_VERSIONING = ON (HISTORY_TABLE = dbo.ReferencesHistory, DATA_CONSISTENCY_CHECK = ON));");

            migrationBuilder.RenameIndex(
                name: "IX_References_SourceId",
                table: "References",
                newName: "IX_References_PMID");
        }
    }
}
