﻿using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.Core.Aggregator.Models;
using PharmaLex.VigiLit.Core.Aggregator.Providers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;
internal class ImportProcessingHelperService : IReferenceResolver
{
    private readonly IReferenceProvider<ESearchCriteria> _referenceProvider;
    private readonly ISubstanceRepository _substanceRepository;

    public ImportProcessingHelperService(IReferenceProvider<ESearchCriteria> referenceProvider, ISubstanceRepository substanceRepository)
    {
        _referenceProvider = referenceProvider;
        _substanceRepository = substanceRepository;
    }

    public async Task<ReferenceResults> GetReferences(ESearchCriteria criteria, DateTime timeout)
    {
        return await _referenceProvider.GetReferences(criteria, timeout);
    }

    public async Task<Substance> GetSubstance(int substanceId)
    {
        return await _substanceRepository.Get(substanceId);
    }
}