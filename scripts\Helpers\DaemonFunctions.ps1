function SwitchDaemon {
    param(
        [string]$os
    )

    Write-Host "Checking if daemon is running $os" -ForegroundColor Green
	
	$dockerDesktopInfo = docker info 2>>$null
	
	switch ($os)
	{
		"linux" 
		{
			if ($dockerDesktopInfo -match "OSType:\s+linux") 
			{
				Write-Host "Docker Desktop is configured to run linux containers."
			} 
			else 
			{
				Write-Host "Docker Desktop is switching to run linux containers."
				& 'C:\Program Files\Docker\Docker\DockerCli.exe' -SwitchLinuxEngine 
			}
		}
		"windows"
		{
			if ($dockerDesktopInfo -match "OSType:\s+windows") 
			{
				Write-Host "Docker Desktop is configured to run windows containers."
			} 
			else 
			{
				Write-Host "Docker Desktop is switching to run windows containers."
				& 'C:\Program Files\Docker\Docker\DockerCli.exe' -SwitchWindowsEngine 
			}
		}
	}

    Write-Host "Completed." -ForegroundColor Green
}
