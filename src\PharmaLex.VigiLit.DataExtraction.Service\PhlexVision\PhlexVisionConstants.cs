﻿namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
internal static class PhlexVisionConstants
{
    public static string ConsumerKeyHeaderValue => "PhlexVision:ConsumerKeyHeaderValue";
    public static string PhlexVisionUrl => "PhlexVision:Url";
    public static string SecretValue => "PhlexVision:Secret";
    public static string PhlexVisionCallbackBaseUrl => "PhlexVision:CallbackBaseUrl";
    public static string OpenAiConfigId => "PhlexVision:OpenAiConfigId";

    public static string AuthorizationHeader => "Authorization";
    public const string CorrelationIdHeader = "X-Correlation-ID";
    public static string ConsumerKeyHeader => "consumerkey";
}
