using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class AdHocImportConfiguration : EntityBaseMap<AdHocImport>
{
    public override void Configure(EntityTypeBuilder<AdHocImport> builder)
    {
        base.Configure(builder);

        builder.ToTable("AdHocImports");

        builder.HasIndex(c => new { c.AdHocImportStatusType });
    }
}