﻿using AutoMapper;
using PharmaLex.VigiLit.DataExtraction.Entities;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.FileImport;

namespace PharmaLex.VigiLit.Application.AutoMapper;
public class ImportFileMappingProfile : Profile
{

    public ImportFileMappingProfile()
    {
        CreateMap<ImportFile, ImportFileModel>().ReverseMap();
        CreateMap<ImportFileModel, MdeQueueItem>().ForMember(x => x.Id, opt => opt.Ignore()).ReverseMap();
    }
}
