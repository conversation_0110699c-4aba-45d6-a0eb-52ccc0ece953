﻿using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.AiAnalysis.Entities.Interfaces;
using PharmaLex.VigiLit.AiAnalysis.Entities.Repositories;

namespace PharmaLex.VigiLit.AiAnalysis.Entities
{
    public static class ConfigureServices
    {
        public static void RegisterAiAnalysisEntities(this IServiceCollection services)
        {
            services.AddScoped<IAiSuggestionRepository, AiSuggestionRepository>();
        }
    }
}
