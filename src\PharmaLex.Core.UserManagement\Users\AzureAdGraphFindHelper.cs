﻿using AutoMapper;
using Azure.Identity;
using Microsoft.Graph.Models;
using PharmaLex.Authentication.B2C;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserManagement.Users;

public class AzureAdGraphFindHelper<TUserEntity> : IAzureAdGraphFindHelper
    where TUserEntity : EntityBase, IUserEntity
{
    private readonly IUserRepository<TUserEntity> _userRepository;
    private readonly IAzureAdGraphService _graphService;
    private readonly IMapper _mapper;

    public AzureAdGraphFindHelper(IUserRepository<TUserEntity> userRepository, IAzureAdGraphService graphService,
        IMapper mapper)
    {
        _userRepository = userRepository;
        _graphService = graphService;
        _mapper = mapper;
    }

    private static bool HasName(UserFindResult user)
    {
        return !string.IsNullOrEmpty(user.GivenName) && !string.IsNullOrEmpty(user.FamilyName);
    }

    public async Task<List<UserFindResult>> Find(string term)
    {
        var allUsers = await _userRepository.GetAllUsers(false);

        var dbUsers = allUsers.Where(x =>
            x.Email.StartsWith(term, StringComparison.InvariantCultureIgnoreCase)
            || x.GivenName.StartsWith(term, StringComparison.InvariantCultureIgnoreCase)
            || x.FamilyName.StartsWith(term, StringComparison.InvariantCultureIgnoreCase)
        ).ToList();

        List<User> graphUsers;
        try
        {
            graphUsers = await _graphService.FindUsers(term);
        }
        catch (AuthenticationFailedException e)
        {
            Console.WriteLine(
                "Developer: If you are here then you probably need to check the ClientSecret in AzureAdGraph in appsettings.json:" +
                e.Message);
            throw;
        }

        var graphUsersNotInDb = new List<UserFindResult>();

        foreach (var activeDirectoryUser in graphUsers)
        {
            var emailAddress = activeDirectoryUser.GetEmail();
            if (!dbUsers.Exists(y => y.Value.Equals(emailAddress, StringComparison.OrdinalIgnoreCase)))
            {
                var missingUser = _mapper.Map<UserFindResult>(activeDirectoryUser);

                if (HasName(missingUser))
                {
                    missingUser.Email = emailAddress;
                    missingUser.Name = $"{missingUser.GivenName} {missingUser.FamilyName} ({missingUser.Email})";
                    graphUsersNotInDb.Add(missingUser);
                }
            }
        }

        var graphUsersAlreadyInDb = dbUsers.Where(dbUser => graphUsers.Exists(user =>
            user.GetEmail().Equals(dbUser.Value, StringComparison.OrdinalIgnoreCase)));

        var users = new List<UserFindResult>(graphUsersNotInDb);
        users.AddRange(graphUsersAlreadyInDb);

        return users.OrderBy(x => x.Name).ToList();
    }
}