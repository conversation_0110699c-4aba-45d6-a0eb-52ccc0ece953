using AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Client.Dto;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class ReferenceMappingProfile : Profile
{
    public ReferenceMappingProfile()
    {
        CreateMap<Reference, ReferenceModel>();

        CreateMap<Reference, ReferenceSupportModel>();

        CreateMap<Reference, ReferenceDetailedModel>()
            .ReverseMap();

        CreateMap<ReferenceDetailedTemporalModel, ReferenceDetailedModel>();

        CreateMap<Reference, ReferenceSendGridTemplateModel>()
            .ForMember(dest => dest.SourceSystem, x => x.MapFrom(src => (SourceSystem)src.SourceSystem));

        CreateMap<FailedImportReferenceDto, FailedImportFile>()
          .ReverseMap();
    }
}