using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class AiSuggestedClassificationStoredProcResultConfiguration : EntityBaseMap<AiSuggestedClassificationStoredProcResult>
{
    public override void Configure(EntityTypeBuilder<AiSuggestedClassificationStoredProcResult> builder)
    {
        builder.HasNoKey().ToView(null);
    }
}