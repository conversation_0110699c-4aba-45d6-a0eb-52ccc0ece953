﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class EmailPreferenceConfiguration : EntityBaseMap<EmailPreference>
{
    public override void Configure(EntityTypeBuilder<EmailPreference> builder)
    {
        base.Configure(builder);

        builder.ToTable("EmailPreferences");

        builder.Property(e => e.Name)
            .IsRequired(false)
            .HasMaxLength(50);

        builder.Property(e => e.DisplayName)
            .IsRequired(false)
            .HasMaxLength(50);
    }
}