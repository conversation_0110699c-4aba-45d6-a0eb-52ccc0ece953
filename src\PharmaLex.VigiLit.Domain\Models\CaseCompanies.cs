﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.VigiLit.Domain.Models;

public class CaseCompanies : VigiLitEntityBase
{
    [ForeignKey("Cases")]
    public int CaseId { get; set; }
    public Cases Case { get; set; }

    [ForeignKey("Company")]
    public int CompanyId { get; set; }
    public Company Company { get; set; }

    public CaseCompanies() { }

    public CaseCompanies(Cases caseRecord, int companyId)
    {
        CaseId = caseRecord.Id;
        Case = caseRecord;
        CompanyId = companyId;
    }
}