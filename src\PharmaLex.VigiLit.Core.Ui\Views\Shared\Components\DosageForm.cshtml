﻿<script type="text/x-template" id="dosage-form-template">
    <div class="form-group">
        <input type="text" :value="modelValue" required="" class="dosageForm" />
        <span v-for="option in dosageOptions" class="dosage-form-badge" :value="option" v-on:click="$emit('update:modelValue', option)">{{option}}</span>
    </div>
    
</script>

<script type="text/javascript">
    const dosages = [
        "N.A",
        "N.I",
        "IN VITRO",
        "I.V",
        "IN VIVO",
        "P.O"
    ];

    vueApp.component('dosage-form', {
            template: '#dosage-form-template',
            props: ['modelValue'],
            emits: ['update:modelValue'],
            data: () => {
                return {
                    //selectedDosageForm: "",
                    dosageOptions: dosages
                };
            }
    });
</script>
