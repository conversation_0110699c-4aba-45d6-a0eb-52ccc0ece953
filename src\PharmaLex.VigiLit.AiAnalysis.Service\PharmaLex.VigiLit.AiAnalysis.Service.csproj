﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <OutputType>Exe</OutputType>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <DockerfileContext>..\..</DockerfileContext>
    <RuntimeIdentifiers>linux-x64</RuntimeIdentifiers>
    <EnableSdkContainerDebugging>True</EnableSdkContainerDebugging>
    <IsPublishable>True</IsPublishable>
    <EnableSdkContainerSupport>True</EnableSdkContainerSupport>
	  <GenerateAssemblyInfo>true</GenerateAssemblyInfo>

	  <UserSecretsId>PharmaLex.VigiLit.AiAnalysis.Service</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
	<PackageReference Include="AutoMapper" Version="14.0.0" />
	<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
	<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
	<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1" />
	<PackageReference Include="NewRelic.Agent.Api" Version="10.43.0" />
	<PackageReference Include="NLog" Version="6.0.2" />
	<PackageReference Include="NLog.Web.AspNetCore" Version="6.0.2" />
	<PackageReference Include="Phlex.Core.MessageBus" Version="**********" />
	<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
	<PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.Configuration\PharmaLex.Core.Configuration.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.DataAccessLayer\PharmaLex.VigiLit.DataAccessLayer.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.AiAnalysis.Entities\PharmaLex.VigiLit.AiAnalysis.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.MessageBroker.Contracts\PharmaLex.VigiLit.MessageBroker.Contracts.csproj" />
  </ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.AiAnalysis.Unit.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.AiAnalysis.Integration.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="DynamicProxyGenAssembly2, PublicKey=0024000004800000940000000602000000240000525341310004000001000100c547cac37abd99c8db225ef2f6c8a3602f3b3606cc9891605d02baa56104f4cfc0734aa39b93bf7852f7d9266654753cc297e7d2edfe0bac1cdcf9f717241550e0a7b191195b7667bb4f64bcb8e2121380fd1d9d46ad2d92d2d15605093924cceaf74c4861eff62abf69b9291ed0a340e113be11e6a7d3113e92484cf7045cc7"></InternalsVisibleTo>
	</ItemGroup>

	<ItemGroup>
	  <None Update="appsettings.Development.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="appsettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
