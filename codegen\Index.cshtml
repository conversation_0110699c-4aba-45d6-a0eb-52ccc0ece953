{{#if Dependencies}}{{#each Dependencies}}@using {{this}};
{{/each}}{{/if}}
@model IEnumerable<{{EntityName}}Model>

 @{
    ViewData["Title"] = "{{EntityNamePlural}}";
 }

<div id="{{EntityNameCamelCase}}" v-cloak>
    <section>
        <div class="flex justify-space-between">
            <h2 class="brand-color">{{EntityNamePlural}}</h2>
            <div class="controls">
                <a asp-action="Create" class="button primary">Create {{EntityName}}</a>
            </div>
        </div>

        <div>
            <filtered-table :items="{{EntityNameCamelCase}}" :columns="columns" :filters="filters" :link="link"></filtered-table>
        </div>
    </section>

    <modal-dialog v-if="confirmResolve"
                  width="200px"
                  height="150px"
                  title="Delete Company"
                  v-on:close="confirmReject"
                  v-on:confirm="confirmResolve">
        <p>Are you sure you want to delete this {{EntityNameCamelCase}}?</p>
    </modal-dialog>
</div>

    @section Scripts {
<script type="text/javascript">

    var pageConfig = {
        appElement: "#{{EntityNameCamelCase}}",
        data: function () {
            return {
                confirmResolve: null,
                confirmReject: null,
                confirmText: null,
                link: '/{{EntityNamePlural}}/edit/',
                historyUrl: '/{{EntityNamePlural}}/history/',
                resources: {
                    remove: "Remove {{EntityName}}"
                },
                {{EntityNameCamelCase}}: @Html.Raw(Model.ToJson()),
                columns: {
                    idKey: 'id',
                    actions:[{
                        title: 'Delete',
                        icon: 'icon-trash',
                        claim: (item) => {
                            return new Promise((claimResolve, claimReject) => {
                                var confirm = new Promise((resolve, reject) => {
                                    this.confirmText = `Are you sure you want to delete this {{EntityName}}?`;
                                    this.confirmResolve = resolve;
                                    this.confirmReject = reject;
                                });

                                confirm.then(() => {
                                    fetch(`/{{EntityNamePlural}}/delete/${item.id}`, {
                                        method: 'DELETE',
                                        credentials: 'same-origin',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        }
                                    }).then(result => {
                                        if (result.ok) {
                                            plx.toast.show('{{EntityName}} has been removed', 2, 'confirm', null, 2500);
                                            this.{{EntityNameCamelCase}}.splice(this.{{EntityNameCamelCase}}.findIndex(x => x[this.columns.idKey] === item[this.columns.idKey]), 1);
                                            claimResolve();
                                        }
                                        else {
                                            plx.toast.show('removing {{EntityName}} failed', 2, 'failed', null, 5000);
                                            claimReject();
                                        }
                                    });
                                }, claimReject)
                                .finally(() => {
                                    this.confirmResolve = this.confirmReject = null;
                                    this.confirmTitle = null;
                                });
                            });
                        }
                    },{
                        title: 'History',
                        icon: 'icon-history',
                        claim: (item) => {
                            if (this.historyUrl) {
                                window.location.href = this.historyUrl + item[this.columns.idKey];
                            }
                        }
                    }],                    
                    config: [{{#if PropertiesCamelCase}}{{#each PropertiesCamelCase}}
                        {
                            dataKey: '{{this.Name}}',
                            sortKey: '{{this.Name}}',
                            header: '{{this.Name}}',
                            {{#if this.IsNumber}}type: 'number'{{/if}}
                            {{#if this.IsString}}type: 'text'{{/if}}
                            {{#if this.IsDateTime}}type: 'date'{{/if}}
                            {{#if this.IsUnknownType}}type: 'text'{{/if}}

                        },{{/each}}{{/if}}
                    ]
                },
                filters: [
                    {{#if PropertiesCamelCase}}{{#each PropertiesCamelCase}}
                        {
                            key: '{{this.Name}}',
                            options: [],
                            type: 'search',
                            header: 'Search {{this.Name}}',
                            {{#if this.IsString}}fn: v => p => p.{{this.Name}}.toLowerCase().includes(v.toLowerCase()), {{/if}}
                            {{#if this.IsNumber}} fn: v => p => p.{{this.Name}}.toString().includes(v),{{/if}}
                            {{#if this.IsDateTime}} fn: v => p => convertDate(p.{{this.Name}}).toLowerCase().includes(v.toLowerCase()),{{/if}}
                            {{#if this.IsUnknownType}}fn: v => p => p.{{this.Name}}.toLowerCase().includes(v.toLowerCase()), {{/if}}
                            convert: v => v
                    },{{/each}}{{/if}}
                ]
            };
        }
    };
</script>
    }

@section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
<partial name="Components/ModalDialog" />
}

