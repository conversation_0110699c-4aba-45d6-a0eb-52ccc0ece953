﻿<script type="text/x-template" id="audit-trail-menu">
    <div class="audit-trail-container" @@click.stop>
        
        <div class="audit-trail-menu-heading">
            <div class="audit-trail-menu-heading-text">
                Audit Trail
            </div>
            <div class="audit-trail-download-icon-placement">
                <span class="audit-trail-download-icon">arrow_drop_down</span>
            </div>
        </div>

        <div class="audit-trail-dropdown-content">
            <ul>
                <li @@click.stop.prevent="translation"  title="Download the translation"><span class="audit-trail-download-icon">download</span>Translation</li>
                <li @@click.stop.prevent="originalFile" title="Download the original file"><span class="audit-trail-download-icon">download</span>{{getFileNameDisplay}}</li>
            </ul>
        </div>
    </div>

</script>

<script type="text/javascript">
    vueApp.component('audit-trail-selection', {
        template: '#audit-trail-menu',
        emits: ['translationSelected', 'originalFileSelected'],
        props: { 
            fileName: {
                type: String
            }
        },
        computed: {
            getFileNameDisplay() {
                return `Original File | ${this.fileName}`;
            }
        },
        methods: {
            translation: function () {
                this.$emit('translationSelected');
            },
            originalFile: function () {
                this.$emit('originalFileSelected');
            }
        }
    });
</script>

