﻿using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

public class ParameterInfoAdapter : IParameterInfo
{
    private readonly ParameterInfo _parameterInfo;
    private IType _parameterType;

    public ParameterInfoAdapter(ParameterInfo parameterInfo)
    {
        _parameterInfo = parameterInfo;
    }

    public IType ParameterType
    {
        get
        {
            if (_parameterType == null)
            {
                _parameterType = new TypeAdapter(_parameterInfo.ParameterType);
            }

            return _parameterType;
        }
    }

    public virtual string Name => _parameterInfo.Name;

    public object[] GetCustomAttributes(bool inherit)
    {
        return _parameterInfo.GetCustomAttributes(inherit);
    }

    public object[] GetCustomAttributes(Type attributeType, bool inherit)
    {
        return _parameterInfo.GetCustomAttributes(attributeType, inherit);
    }

    public bool IsDefined(Type attributeType, bool inherit)
    {
        return _parameterInfo.IsDefined(attributeType, inherit);
    }
}