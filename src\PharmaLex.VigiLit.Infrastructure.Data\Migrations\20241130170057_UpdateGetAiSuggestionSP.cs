﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.VigiLit.Infrastructure.Data.Extensions;

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class UpdateGetAiSuggestionSP : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.SqlFileExec("spGetAiSuggestedClassification_2.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // VigiLit does not go back in versions
        }
    }
}
