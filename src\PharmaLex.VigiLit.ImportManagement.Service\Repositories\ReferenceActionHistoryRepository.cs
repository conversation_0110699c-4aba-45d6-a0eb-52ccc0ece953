﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;

public class ReferenceHistoryActionRepository : TrackingGenericRepository<ReferenceHistoryAction>, IReferenceHistoryActionRepository
{
    public ReferenceHistoryActionRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
    {
    }
}