using PharmaLex.Dotnet.Adapters.Reflection;

namespace PharmaLex.Dotnet.Adapters;

public class AppDomainAdapter : IAppDomain
{
    readonly AppDomain _appDomain;

    public AppDomainAdapter(AppDomain appDomain)
    {
        _appDomain = appDomain;
    }

    public string BaseDirectory => _appDomain.BaseDirectory;

    public IAssembly[] GetAssemblies()
    {
        var assemblies = _appDomain.GetAssemblies();
        IAssembly[] list = new IAssembly[assemblies.Length];

        for (var i = 0; i < assemblies.Length; i++)
        {
            list[i] = new AssemblyAdapter(assemblies[i]);
        }

        return list;
    }
}