﻿using System.IO.Compression;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;

namespace PharmaLex.VigiLit.Infrastructure.Utilities;

public class StreamHelper : IStreamHelper
{
    public byte[] GetZipOfBytesFromFileStreams(Dictionary<string, Stream> files)
    {
        var outStream = new MemoryStream();

        if (files != null && files.Count > 0)
        {
            using (var archive = new ZipArchive(outStream, ZipArchiveMode.Create, true))
            {
                foreach (var stream in files)
                {
                    byte[] fileBytes = StreamToByteArray(stream.Value);

                    var fileInArchive = archive.CreateEntry(stream.Key);

                    using (var entryStream = fileInArchive.Open())
                    using (var fileToCompressStream = new MemoryStream(fileBytes))
                    {
                        fileToCompressStream.CopyTo(entryStream);
                    }
                }
            }
            outStream.Seek(0, SeekOrigin.Begin);
        }

        return outStream.ToArray();
    }

    public bool AreDictionariesTheSame(Dictionary<string, Stream> compareTo, Dictionary<string, MemoryStream> compare)
    {
        var matching = compareTo.Count == compare.Count;

        if (!matching)
        {
            return false;
        }

        foreach (var entry in compareTo)
        {
            if (compare.TryGetValue(entry.Key, out var value))
            {
                matching = matching && AreStreamsIdentical(entry.Value, value);
            }
            else
            {
                matching = false;
            }
        }
        return matching;
    }

    public Dictionary<string, MemoryStream> UnzipBytesToDictionaryOfStreams(byte[] inputBytes)
    {
        var result = new Dictionary<string, MemoryStream>();

        using (var zipStream = new MemoryStream())
        {
            zipStream.Write(inputBytes, 0, inputBytes.Length);

            using (var zip = new ZipArchive(zipStream, ZipArchiveMode.Read))
            {
                foreach (var zipEntry in zip.Entries)
                {
                    using (var unzippedStream = zipEntry.Open())
                    using (var memoryStream = new MemoryStream())
                    {
                        unzippedStream.CopyTo(memoryStream);
                        memoryStream.Position = 0;
                        result.Add(zipEntry.FullName, memoryStream);
                    }
                }
            }
        }
        return result;
    }

    public bool AreStreamsIdentical(Stream stream, MemoryStream memoryStream)
    {
        return ((MemoryStream)stream).ToArray().SequenceEqual(memoryStream.ToArray());
    }

    private byte[] StreamToByteArray(Stream input)
    {
        using (MemoryStream memoryStream = new MemoryStream())
        {
            input.Position = 0;
            input.CopyTo(memoryStream);
            return memoryStream.ToArray();
        }
    }
}
