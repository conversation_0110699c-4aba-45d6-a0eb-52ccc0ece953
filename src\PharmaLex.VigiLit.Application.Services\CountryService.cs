using PharmaLex.Caching.Data;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.Countries;

namespace PharmaLex.VigiLit.Application.Services;

public class CountryService : ICountryService
{
    private readonly IDistributedCacheServiceFactory _cacheServiceFactory;

    public CountryService(IDistributedCacheServiceFactory cacheServiceFactory)
    {
        _cacheServiceFactory = cacheServiceFactory;
    }

    public async Task<IEnumerable<string>> GetAllAsync()
    {
        var countryCache = _cacheServiceFactory.CreateMappedEntity<Country, string>();
        var countries = await countryCache.AllAsync();

        return countries.OrderBy(x => x);
    }

    public async Task<IEnumerable<CountryModel>> GetAllWithDetailsAsync()
    {
        var countryCache = _cacheServiceFactory.CreateMappedEntity<Country, CountryModel>();
        var countries = await countryCache.AllAsync();

        return countries.OrderBy(x => x.Name);
    }
}