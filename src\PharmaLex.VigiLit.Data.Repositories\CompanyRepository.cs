using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Data.Repositories;

public class CompanyRepository : TrackingGenericRepository<Company>, ICompanyRepository, IReportingCompanyRepository
{
    private readonly IMapper _mapper;

    public CompanyRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<Company>> GetAllAsync()
    {
        return await context.Set<Company>()
            .OrderBy(c => c.Name)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<Company?> GetByIdAsync(int id)
    {
        return await context.Set<Company>()
            .Where(u => u.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<Company?> GetByNameAsync(string name)
    {
        return await context.Set<Company>()
            .Where(u => u.Name == name)
            .AsNoTracking()
            .FirstOrDefaultAsync();
    }

    public async Task<Company?> GetWithContractsByIdAsync(int id)
    {
        return await context.Set<Company>()
            .Include(c => c.Projects).ThenInclude(p => p.ContractsInternal).ThenInclude(c => c.ContractVersionsInternal).ThenInclude(cvi => cvi.ContractVersionJournals)
            .Include(c => c.Projects).ThenInclude(p => p.ContractsInternal).ThenInclude(c => c.ContractVersionsInternal)
            .Include(c => c.Projects).ThenInclude(p => p.ContractsInternal).ThenInclude(c => c.Substance)
            .Where(c => c.Id == id)
            .AsNoTracking()
            .FirstOrDefaultAsync();
    }

    public async Task<Company?> GetWithProjectsByIdAsync(int id)
    {
        return await context.Set<Company>()
            .Include(c => c.Projects)
            .Where(c => c.Id == id)
            .AsNoTracking()
            .FirstOrDefaultAsync();
    }

    public async Task<Company?> GetWithUsersByIdAsync(int id)
    {
        return await context.Set<Company>()
            .Include(c => c.CompanyUsers).ThenInclude(c => c.User)
            .Where(c => c.Id == id)
            .AsNoTracking()
            .FirstOrDefaultAsync();
    }
    public async Task<Company?> GetWithUsersAndSuppressionsByIdAsync(int id)
    {
        return await context.Set<Company>()
            .Include(c => c.CompanyUsers)
                .ThenInclude(u => u.User)
                .ThenInclude(u => u.EmailSuppression)
            .Where(c => c.Id == id)
            .AsNoTracking()
            .FirstOrDefaultAsync();
    }

    public async Task<List<Company>> GetAllCompaniesWithEmailSuppressions()
    {
        return await context.Set<Company>()
            .Include(c => c.CompanyUsers)
                .ThenInclude(u => u.User)
                .ThenInclude(u => u.EmailSuppression)
            .Where(c => c.CompanyUsers.Any(x => x.User.EmailSuppression != null))
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<IEnumerable<CompanyEmailModel>> GetActiveCompaniesWithUsers()
    {
        var query = context.Set<Company>()
            .Include(c => c.CompanyUsers)
                .ThenInclude(cu => cu.User)
            .Where(c => c.IsActive)
            .OrderBy(c => c.Name)
            .AsNoTracking();

        return await _mapper.ProjectTo<CompanyEmailModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<Company>> GetCompaniesForSubstanceAsync(int substanceId)
    {
        return await context.Set<Contract>()
            .Where(contract => contract.SubstanceId == substanceId
                                && contract.ContractVersionsInternal.SingleOrDefault(cs => cs.Id == contract.CurrentContractVersionId)!.IsActive
                                && contract.Project.Company.IsActive)
            .Select(contract => contract.Project.Company)
            .Distinct()
            .OrderBy(company => company.Name)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<IEnumerable<CompanyItemModel>> GetCompanyItems(User user)
    {
        if (user.IsCompanyUser())
        {
            return Enumerable.Empty<CompanyItemModel>();
        }

        var query = context.Set<Company>()
            .OrderBy(s => s.Name)
            .AsNoTracking();

        return await _mapper.ProjectTo<CompanyItemModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<CompanyModel>> GetActiveCompaniesWithActiveContractsForSubstance(int substanceId)
    {
        var query = context.Set<Contract>()
           .Include(c => c.ContractVersionsInternal)
           .Include(c => c.Project)
               .ThenInclude(p => p.Company)
           .Where(c => c.SubstanceId == substanceId
               && c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.IsActive
               && c.Project.Company.IsActive)
           .Select(x => x.Project.Company)
           .OrderBy(x => x.Name)
           .AsNoTracking();

        return await _mapper.ProjectTo<CompanyModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<Company>> GetActiveCompanies()
    {
        return await context.Set<Company>()
            .Where(c => c.IsActive)
            .OrderBy(c => c.Name)
            .AsNoTracking()
            .ToListAsync();
    }
}
