﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.ImportApp.Functions.Email;

public class FetchSuppressions
{
    private readonly IEmailMaintenanceService _emailMaintenanceService;

    public FetchSuppressions(IEmailMaintenanceService emailMaintenanceService)
    {
        _emailMaintenanceService = emailMaintenanceService;
    }

    [Transaction]
    [Function("Email_FetchSuppressions")]
    public async Task Run([TimerTrigger("0 0 * * * *")] TimerInfo myTimer, ILogger log)
    {
        await _emailMaintenanceService.UpdateSuppressions();
    }
}