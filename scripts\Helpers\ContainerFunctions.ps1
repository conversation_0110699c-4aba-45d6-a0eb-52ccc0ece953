function ManageContainer {
    param(
        [string]$containerName,
        [string]$dockerRunCommand
    )

    Write-Host "Checking for any $containerName instances" -ForegroundColor Green

    if (docker ps --all --quiet --filter name=$containerName) {
        if ((docker inspect --format '{{.State.Running}}' $containerName) -eq "True") {
            Write-Host -NoNewline "Stopping:"
            docker stop $containerName
        }

        Write-Host -NoNewline "Removing:"
        docker rm $containerName
    }

    Write-Host -NoNewline -ForegroundColor White "$containerName ID:"
    Invoke-Expression $dockerRunCommand
    Write-Host "Completed." -ForegroundColor Green
}