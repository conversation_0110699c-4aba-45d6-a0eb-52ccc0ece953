using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Data.Repositories;

public class SubstanceRepository : CachedTrackingGenericRepository<Substance>, ISubstanceRepository
{
    private readonly IMapper _mapper;

    public SubstanceRepository(PlxDbContext context, IDistributedCache? distributedCache, IMapper mapper, IUserContext userContext)
        : base(context, distributedCache, userContext)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<SubstanceModel>> GetAllAsync()
    {
        var query = context.Set<Substance>()
            .Include(s => s.SubstanceSynonyms)
            .OrderBy(s => s.Name)
            .AsNoTracking();

        return await _mapper.ProjectTo<SubstanceModel>(query).ToListAsync();
    }

    public Task<Substance?> GetByIdAsync(int id)
    {
        return context.Set<Substance>()
            .Include(s => s.SubstanceSynonyms)
            .Where(u => u.Id == id)
            .FirstOrDefaultAsync();
    }

    public Task<Substance> Get(int id)
    {
        return context.Set<Substance>()
            .Include(s => s.SubstanceSynonyms)
            .Where(u => u.Id == id)
            .SingleAsync();
    }

    public Task<Substance?> GetByNameAsync(string name)
    {
        return context.Set<Substance>()
            .Where(s => s.Name == name)
            .AsNoTracking()
            .FirstOrDefaultAsync();
    }

    public Task<Substance?> GetByNameAndTypeAsync(string name, string type)
    {
        return context.Set<Substance>()
            .Where(s => s.Name == name && s.Type == type)
            .AsNoTracking()
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<Substance>> GetRangeByIdsAsync(IEnumerable<int> substanceIds)
    {
        return await context.Set<Substance>()
            .Where(s => substanceIds.Contains(s.Id))
            .OrderBy(s => s.Name)
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task AddNewSynonym(Substance substance, IEnumerable<SubstanceSynonym> substanceSynonyms)
    {
        foreach (var synonym in substanceSynonyms)
        {
            var exisitingSynonym = substance.SubstanceSynonyms
                 .SingleOrDefault(s => s.Id == synonym.Id
                  && s.Id != 0 && s.SubstanceId != 0);

            if (exisitingSynonym == null)
            {
                // Insert synonym
                var newSubstanceSynonym = new SubstanceSynonym
                {
                    Name = synonym.Name,
                    SubstanceId = substance.Id,

                };

                substance.SubstanceSynonyms.Add(newSubstanceSynonym);
            }
        }

        await SaveChangesAsync();
    }

    public async Task<IEnumerable<SubstanceItemModel>> GetForSearch(User user)
    {
        var query = context.Set<Substance>()
            .Include(s => s.SubstanceSynonyms)
            .AsQueryable();

        if (user.IsCompanyUser())
        {
            if (!user.HasActiveCompany())
            {
                return Enumerable.Empty<SubstanceItemModel>();
            }

            query = query.Where(s => context.Set<Contract>().Any(c => c.Project.CompanyId == user.GetActiveCompanyId() && s.Id == c.SubstanceId));
        }
        else
        {
            query = query.Where(s => context.Set<Contract>().Any(c => s.Id == c.SubstanceId));
        }

        query = query
            .OrderBy(s => s.Name)
            .AsNoTracking();

        return await _mapper.ProjectTo<SubstanceItemModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<SubstanceItemModel>> GetActiveSubstancesWithNoClassificationsForReference(int referenceId)
    {
        var query = context.Set<Substance>()
                    .Where(s => context.Set<Contract>().Any(
                            c => s.Id == c.SubstanceId &&
                                 c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.IsActive &&
                                 c.Project.Company.IsActive)
                    )
                    .Distinct();

        query = query.Where(rc => !context.Set<ReferenceClassification>().Any(x => x.ReferenceId == referenceId && x.SubstanceId == rc.Id));

        return await _mapper.ProjectTo<SubstanceItemModel>(query).ToListAsync();
    }
}
