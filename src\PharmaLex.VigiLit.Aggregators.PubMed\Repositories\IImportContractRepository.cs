﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Repositories;

internal interface IImportContractRepository : ITrackingRepository<ImportContract>
{
    /// <summary>
    /// Gets the last scheduled log entry for contract.
    /// </summary>
    /// <param name="contractId">The contract identifier.</param>
    /// <param name="days">The days for which to look back.</param>
    /// <returns></returns>
    Task<ImportContract?> GetLastScheduledLogEntryForContract(int contractId, int days);

    /// <summary>
    /// Gets the contracts for scheduled import.
    /// </summary>
    /// <param name="importDate">The import date.</param>
    /// <returns>
    /// Returns the contract versions, the company, project and substance.
    /// </returns>
    Task<IEnumerable<Contract>> GetContractsForScheduledImport(DateTime importDate);
}