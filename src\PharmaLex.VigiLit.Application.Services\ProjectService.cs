using AutoMapper;
using System.Collections.Generic;
using System.Threading.Tasks;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Application.Services;

public class ProjectService : IProjectService
{
    private readonly IProjectRepository _projectRepository;
    private readonly IMapper _mapper;

    public ProjectService(IProjectRepository projectRepository, IMapper mapper)
    {
        _projectRepository = projectRepository;
        _mapper = mapper;
    }

    public async Task<IEnumerable<ProjectModel>> GetAllAsync()
    {
        var project = await _projectRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<ProjectModel>>(project);
    }

    public async Task<ProjectModel> GetByIdAsync(int id)
    {
        var project = await _projectRepository.GetByIdAsync(id);
        return _mapper.Map<ProjectModel>(project);
    }

    public async Task AddAsync(ProjectModel model)
    {
        var project = new Project(model.Name, model.CompanyId);
        _projectRepository.Add(project);
        await _projectRepository.SaveChangesAsync();
    }

    public async Task UpdateAsync(ProjectModel model)
    {
        var project = await _projectRepository.GetByIdAsync(model.Id);
        project.Update(model.Name);

        await _projectRepository.SaveChangesAsync();
    }

    public async Task<bool> ValidateNameAsync(string name, int id, int companyId)
    {
        var project = await _projectRepository.GetByNameAndCompanyAsync(name, companyId);
        if (project == null)
            return true;

        return project.Id == id;
    }

    public async Task<IEnumerable<ProjectModel>> GetByCompanyIdAsync(int companyId)
    {
        var companyProjects = await _projectRepository.GetByCompanyIdAsync(companyId);
        return _mapper.Map<IEnumerable<ProjectModel>>(companyProjects);
    }
}