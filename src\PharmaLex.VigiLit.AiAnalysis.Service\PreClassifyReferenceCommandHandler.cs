﻿using MassTransit;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.AiAnalysis.Entities;
using PharmaLex.VigiLit.AiAnalysis.Entities.Enums;
using PharmaLex.VigiLit.AiAnalysis.Entities.Interfaces;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using System.Text;
using System.Text.Json;

namespace PharmaLex.VigiLit.AiAnalysis.Service;

internal class PreClassifyReferenceCommandHandler : IConsumer<PreClassifyReferenceCommand>
{
    private readonly AiEndpointOptions _aiEndpointOptions;
    private readonly ILogger<PreClassifyReferenceCommandHandler> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IAiSuggestionRepository _aiSuggestionRepository;

    public PreClassifyReferenceCommandHandler(ILogger<PreClassifyReferenceCommandHandler> logger, IHttpClientFactory httpClientFactory,
        IAiSuggestionRepository aiSuggestionRepository, IOptions<AiEndpointOptions> aiEndpointOptions)
    {
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _aiSuggestionRepository = aiSuggestionRepository;
        _aiEndpointOptions = aiEndpointOptions.Value;
    }

    // This is where the Consumer consumes message published in the Message broker
    [Transaction]
    public async Task Consume(ConsumeContext<PreClassifyReferenceCommand> context)
    {
        var message = context.Message;

        _logger.LogInformation("Message published to message broker: {Message}", context.Message);

        var aiSuggestionObj = await AddAiSuggestion(context, message);
        await CallAiApi(message, aiSuggestionObj);
    }

    [Trace]
    private async Task<AiSuggestion> AddAiSuggestion(ConsumeContext<PreClassifyReferenceCommand> context, PreClassifyReferenceCommand message)
    {
        var aiSuggestionObj = new AiSuggestion()
        {
            SourceId = message.RecordIdentifier,
            CorrelationId = context.CorrelationId.ToString(),
            Substance = message.Substance,
            Status = AiAnalysisStatus.AwaitingResponse,
        };
        _logger.LogInformation("Adding AI suggestion for {SourceId}:", aiSuggestionObj.SourceId);
        _aiSuggestionRepository.Add(aiSuggestionObj);
        await _aiSuggestionRepository.SaveChangesAsync();
        return aiSuggestionObj;
    }

    [Trace]
    private async Task CallAiApi(PreClassifyReferenceCommand message, AiSuggestion aiSuggestion)
    {
        var msgObj = new { title = message.Title, @abstract = message.Abstract, substance = message.Substance };
        var jsonPayload = JsonSerializer.Serialize(msgObj);

        using var client = _httpClientFactory.CreateClient();
        try
        {

            var content = new StringContent(jsonPayload, Encoding.UTF8, "application/json");
            var response = await client.PostAsync(_aiEndpointOptions.Uri, content);
            if (response.IsSuccessStatusCode)
            {
                string responseBody = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Response body for substance {substance}, {Body}", message.Substance, responseBody);

                UpdateAiSuggestion(aiSuggestion, responseBody);
            }
            else
            {
                aiSuggestion.Update(AiAnalysisStatus.AwaitingResponse);
                var errorMessage = await response.Content.ReadAsStringAsync();
                _logger.LogError("Error invoking AI service : {StatusCode}, ErrorMessage: {ErrorMessage}", response.StatusCode, errorMessage);
            }
        }
        catch (HttpRequestException ex)
        {
            aiSuggestion.Update(AiAnalysisStatus.Failed);
            _logger.LogCritical(ex, "Exception invoking AI service: {Exception}, AiAnalysisStatus: {Status}", ex, AiAnalysisStatus.Failed.ToString());
        }
        await _aiSuggestionRepository.SaveChangesAsync();
    }

    [Trace]
    private void UpdateAiSuggestion(AiSuggestion aiSuggestion, string responseBody)
    {
        var aiResponseObj = JsonSerializer.Deserialize<AiApiResponse>(responseBody);
        if (aiResponseObj != null)
        {
            aiSuggestion.Category = aiResponseObj.Answer;
            aiSuggestion.CategoryReason = aiResponseObj.Justification;
            aiSuggestion.DosageForm = aiResponseObj.Dosageform;
            aiSuggestion.DosageFormReason = aiResponseObj.DfReasoning;

            aiSuggestion.Update(aiSuggestion.Category, aiSuggestion.CategoryReason, aiSuggestion.DosageForm, aiSuggestion.DosageFormReason, AiAnalysisStatus.Success);
        }
        else
        {
            _logger.LogError("Error updating AI suggestions for {Id}: {StatusCode}", aiSuggestion.Id, "Response returned null");
        }
    }
}

