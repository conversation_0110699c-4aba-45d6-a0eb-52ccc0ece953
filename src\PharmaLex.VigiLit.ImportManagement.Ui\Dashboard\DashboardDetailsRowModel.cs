﻿namespace PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

public class DashboardDetailsRowModel
{
    public required string CompanyName { get; set; }
    public required string ProjectName { get; set; }
    public required string SubstanceName { get; set; }
    public required string SourceModificationDate { get; set; }
    public required string ICRCType { get; set; }
    public int ReferenceClassificationId { get; set; }
    public required string SourceId { get; set; }
    public required string ClassificationStatus { get; set; }
    public int CompanyId { get; set; }
    public int ContractId { get; set; }
}