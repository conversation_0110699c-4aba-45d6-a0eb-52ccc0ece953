﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Data.Repositories;

public class CompanyUserRepository : TrackingGenericRepository<CompanyUser>, ICompanyUserRepository
{
    public CompanyUserRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
    {
    }

    public Task<CompanyUser?> GetCompanyUserByIdAsync(int companyId, int userId)
    {
        return context.Set<CompanyUser>()
            .Include(cu => cu.User)
                .ThenInclude(u => u.UserEmailPreferences)
            .Where(cu => cu.CompanyId == companyId && cu.UserId == userId)
            .AsNoTracking()
            .FirstOrDefaultAsync();
    }

    public async Task<List<CompanyUser>> GetAllCompanyUsersWithSuppressions()
    {
        return await context.Set<CompanyUser>()
            .Include(cu => cu.User)
            .Include(cu => cu.User.EmailSuppression)
            .AsNoTracking()
            .ToListAsync();
    }

    public Task<CompanyUser?> GetCompanyUserByEmail(string email)
    {
        return context.Set<CompanyUser>()
            .Include(cu => cu.User).ThenInclude(u => u.ClaimsInternal)
            .Include(c => c.Company)
            .FirstOrDefaultAsync(u => u.User.Email == email);
    }
}
