﻿using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface IPotentialCaseAdditionalFieldService
{
    /// <summary>
    /// Returns list of all potential case additional categories
    /// </summary>
    /// <returns>List of PotentialCaseAdditionalFieldModel</returns>
    Task<IEnumerable<PotentialCaseAdditionalFieldModel>> GetAllAsync();
}