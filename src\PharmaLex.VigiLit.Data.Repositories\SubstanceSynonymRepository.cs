﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Data.Repositories;

public class SubstanceSynonymRepository : TrackingGenericRepository<SubstanceSynonym>, ISubstanceSynonymRepository
{
    public SubstanceSynonymRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<SubstanceSynonym>> GetAllAsync()
    {
        return await context.Set<SubstanceSynonym>()
            .AsNoTracking()
            .ToListAsync();
    }

    public Task<SubstanceSynonym?> GetByIdAsync(int id)
    {
        return context.Set<SubstanceSynonym>()
            .FirstOrDefaultAsync(u => u.Id == id);
    }

    public Task<SubstanceSynonym?> GetByNameAsync(string name, int substanceId)
    {
        return context.Set<SubstanceSynonym>()
            .FirstOrDefaultAsync(s => s.SubstanceId == substanceId && s.Name == name);
    }
}