using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Controllers;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Reporting.Contracts.Interfaces.Services;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.ContractManagement.Ui;

[Route("[controller]")]
[Authorize(Policy = Policies.Admin)]
public class ContractsController : BaseController
{
    private readonly IContractService _contractService;
    private readonly IExportService _exportService;

    public ContractsController(IContractService contractService,
                                IExportService exportService,
                                IUserSessionService userSessionService,
                                IConfiguration configuration) : base(userSessionService, configuration)
    {
        _contractService = contractService;
        _exportService = exportService;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        var items = await _contractService.GetAllAsync();

        return View(items);
    }

    [HttpGet("[action]")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Export()
    {
        IEnumerable<ExportContractModel> data = await _contractService.GetAllToExportAsync();

        var export = _exportService.Export<ExportContractModel, ExportContractsClassMap>("Contracts", data);

        return File(export.Data, export.ContentType, export.FileName);
    }

    [HttpGet("History/{contractId}")]
    public async Task<IActionResult> GetContractVersions(int contractId)
    {
        var contract = await _contractService.GetContractDetailsForAdminUsers(contractId);

        return View("ContractHistoryVersions", contract);
    }

    [HttpGet("[action]/{contractId}")]
    public async Task<IActionResult> PrintPreview(int contractId)
    {
        var contract = await _contractService.GetContractWithLatestVersion(contractId);

        return View(contract);
    }
}