﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ImportContractReferenceClassificationConfiguration : EntityBaseMap<ImportContractReferenceClassification>
{
    public override void Configure(EntityTypeBuilder<ImportContractReferenceClassification> builder)
    {
        base.Configure(builder);

        builder.ToTable("ImportContractReferenceClassifications");
        builder.ToTable(tb => tb.UseSqlOutputClause(false));

        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.HasIndex(s => new { s.ImportContractId, s.ReferenceClassificationId }).IsUnique();
        builder.HasIndex(s => new { s.ReferenceClassificationId, s.ImportContractId });

        builder.HasIndex(s => new { s.ImportContractId })
            .IncludeProperties(p => new
            {
                p.ReferenceClassificationId,
                p.ICRCType,
                p.CreatedDate,
                p.CreatedBy,
                p.LastUpdatedDate,
                p.LastUpdatedBy,
                p.ImportId
            });

        builder.HasIndex(s => new { s.ReferenceClassificationId, s.Id, s.ImportId });

        builder.HasIndex(s => new { s.ImportId, s.ReferenceClassificationId });

        builder.HasOne(icrc => icrc.ImportContract)
            .WithMany(ic => ic.ImportContractReferenceClassifications)
            .HasForeignKey(icrc => icrc.ImportContractId);

        builder.HasOne(icrc => icrc.ReferenceClassification)
            .WithMany(rc => rc.ImportContractReferenceClassifications)
            .HasForeignKey(icrc => icrc.ReferenceClassificationId);

        builder.HasOne(ic => ic.Import)
            .WithMany()
            .HasForeignKey(ic => ic.ImportId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

    }
}