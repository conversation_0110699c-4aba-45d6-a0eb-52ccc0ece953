﻿using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Core.Aggregator.Models;

/// <summary>
/// Results object from performing a search on an aggregator.
/// </summary>
/// <remarks>
/// Note that this is specifically for PubMed at the moment. To be revisited once we add another aggregator 
/// </remarks>
public class ReferenceResults
{
    public List<Reference> References { get; set; } = new List<Reference>();

    public bool SearchFailed { get; set; }

    public bool FetchFailed { get; set; }

    public bool SearchStringMissing { get; set; }

    public bool ResultsCapped { get; set; }
}