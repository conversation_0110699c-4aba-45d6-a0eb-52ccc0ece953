﻿using PharmaLex.Core.UserManagement.Users;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserManagement;
internal class UserSearchService<TUserEntity> : IUserSearchService<TUserEntity>
    where TUserEntity : EntityBase, IUserEntity
{
    private readonly IAzureAdGraphFindHelper _azureAdGraphFindHelper;

    public UserSearchService(IAzureAdGraphFindHelper azureAdGraphFindHelper)
    {
        _azureAdGraphFindHelper = azureAdGraphFindHelper;
    }

    public async Task<IEnumerable<UserFindResult>> Find(string searchTerm)
    {
        var result = await _azureAdGraphFindHelper.Find(searchTerm);
        var data = result.OrderBy(x => x.Name);
        return data;
    }
}