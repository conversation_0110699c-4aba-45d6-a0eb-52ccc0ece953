﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.7">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.267" />
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\PharmaLex.Core.UserSessionManagement.Entities\PharmaLex.Core.UserSessionManagement.Entities.csproj" />
	  <ProjectReference Include="..\PharmaLex.VigiLit.DataAccessLayer.Base\PharmaLex.VigiLit.DataAccessLayer.Base.csproj" />
	</ItemGroup>
</Project>