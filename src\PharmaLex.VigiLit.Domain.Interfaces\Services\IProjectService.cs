using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface IProjectService
{
    Task<IEnumerable<ProjectModel>> GetAllAsync();
    Task AddAsync(ProjectModel model);
    Task UpdateAsync(ProjectModel model);
    Task<ProjectModel> GetByIdAsync(int id);
    Task<bool> ValidateNameAsync(string name, int id, int companyId);
    Task<IEnumerable<ProjectModel>> GetByCompanyIdAsync(int companyId);
}
