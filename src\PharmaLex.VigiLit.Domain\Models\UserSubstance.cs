using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Domain.Models;

public class UserSubstance : VigiLitEntityBase
{
    public int UserId { get; set; }
    public User User { get; set; }

    public int SubstanceId { get; set; }
    public Substance Substance { get; set; }

    public UserSubstance(int userId, int substanceId)
    {
        UserId = userId;
        SubstanceId = substanceId;
    }
}
