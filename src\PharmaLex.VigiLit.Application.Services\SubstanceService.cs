using AutoMapper;
using PharmaLex.Caching.Data;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.Services;

public class SubstanceService : ISubstanceService
{
    private readonly ISubstanceRepository _substanceRepository;
    private readonly ISubstanceSynonymRepository _substanceSynonymRepository;
    private readonly IDistributedCacheServiceFactory _cacheServiceFactory;

    private readonly IMapper _mapper;

    public SubstanceService(ISubstanceRepository substanceRepository, ISubstanceSynonymRepository substanceSynonymRepository, IDistributedCacheServiceFactory cacheServiceFactory, IMapper mapper)
    {
        _substanceRepository = substanceRepository;
        _substanceSynonymRepository = substanceSynonymRepository;
        _cacheServiceFactory = cacheServiceFactory;
        _mapper = mapper;
    }

    public async Task<IEnumerable<SubstanceModel>> GetAllAsync()
    {
        return await _substanceRepository.GetAllAsync();
    }

    public async Task<SubstanceModel> GetByIdAsync(int id)
    {
        var substance = await _substanceRepository.GetByIdAsync(id);
        return _mapper.Map<SubstanceModel>(substance);
    }

    public async Task<SubstanceModel> GetByNameAsync(string name)
    {
        var substance = await _substanceRepository.GetByNameAsync(name);
        return _mapper.Map<SubstanceModel>(substance);
    }


    public async Task AddAsync(SubstanceModel model)
    {
        var isNameValid = await ValidateNameAsync(model.Name, model.Id);
        if (!isNameValid)
            throw new InvalidOperationException($"Substance with name {model.Name} already exists in the system.");

        var substance = new Substance(model.Name, model.Type)
        {
            SubstanceSynonyms = new List<SubstanceSynonym>()
        };

        _substanceRepository.Add(substance);

        if (model.SubstanceSynonyms != null)
        {
            await SaveSubstanceSynonyms(substance, model);
        }

        await _substanceRepository.SaveChangesAsync();
    }

    public async Task UpdateAsync(SubstanceModel model)
    {
        var isNameValid = await ValidateNameAsync(model.Name, model.Id);
        if (!isNameValid)
            throw new InvalidOperationException($"Substance with name {model.Name} already exists in the system.");

        var substance = await _substanceRepository.GetByIdAsync(model.Id);
        substance.Update(model.Name, model.Type);

        if (model.SubstanceSynonyms != null)
        {
            await SaveSubstanceSynonyms(substance, model);
        }
        else
        {
            await _substanceRepository.SaveChangesAsync();
        }
    }

    private async Task SaveSubstanceSynonyms(Substance substance, SubstanceModel model)
    {
        var newSynonyms = model.SubstanceSynonyms.Where(s => s.Id == 0);
        var nonExistingSynonyms = new List<SubstanceSynonymModel>();
        var existingSynonyms = new List<string>();

        foreach (var synonym in newSynonyms)
        {
            var existingSynonym = await _substanceSynonymRepository.GetByNameAsync(synonym.Name, substance.Id);
            if (existingSynonym == null)
            {
                nonExistingSynonyms.Add(synonym);
            }
            else
            {
                existingSynonyms.Add(synonym.Name);
            }
        }

        var substanceSynonyms = _mapper.Map<IEnumerable<SubstanceSynonym>>(nonExistingSynonyms);


        await _substanceRepository.AddNewSynonym(substance, substanceSynonyms);

        // Allow to save non existing synonyms and then check for any existing synonyms and show  message on view
        if (existingSynonyms.Count > 0)
        {
            throw new InvalidOperationException("Synonym with name " + string.Join(",", existingSynonyms) + " already exists in the system.");
        }
    }

    public async Task DeleteSynonymAsync(int id)
    {
        var substance = await _substanceSynonymRepository.GetByIdAsync(id);
        _substanceSynonymRepository.Remove(substance);
        await _substanceSynonymRepository.SaveChangesAsync();
    }

    public async Task<bool> ValidateNameAsync(string name, int id = 0)
    {
        var substance = await GetByNameAsync(name);

        if (substance == null)
        {
            return true;
        }

        return substance.Id == id;
    }

    public async Task<IEnumerable<SubstanceItemModel>> GetForSearch(User user)
    {
        return await _substanceRepository.GetForSearch(user);
    }

    public async Task<IEnumerable<SubstanceItemModel>> GetActiveSubstancesWithNoClassificationsForReference(int referenceId)
    {
        return await _substanceRepository.GetActiveSubstancesWithNoClassificationsForReference(referenceId);
    }

    public async Task<IEnumerable<SubstanceModel>> GetAllWithSynonymsCachedAsync()
    {
#pragma warning disable CS0618
        var substanceCache = _cacheServiceFactory.CreateMappedEntity<Substance, SubstanceModel>()
                            .Configure(x => x.Include(y => y.SubstanceSynonyms));
#pragma warning restore CS0618

        var substances = await substanceCache.AllAsync();

        return substances.ToList();
    }

    public async Task<Substance> Get(int substanceId)
    {
        return await _substanceRepository.Get(substanceId);
    }
}
