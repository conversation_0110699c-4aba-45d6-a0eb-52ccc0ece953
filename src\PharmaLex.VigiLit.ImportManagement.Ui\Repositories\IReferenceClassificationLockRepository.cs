﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public interface IReferenceClassificationLockRepository : ITrackingRepository<ReferenceClassificationLock>
{
    Task<List<int>> GetAllLockedClassificationIdsForPick();
    Task<IEnumerable<ReferenceClassificationLock>> GetLocksForUser(int userId);
    Task<LockingResult> Lock(List<int> referenceClassificationIds, int userId);
    Task<LockingResult> Unlock(int userId);
}