﻿using Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage;
using PharmaLex.BlobStorage.Services;
using PharmaLex.VigiLit.ImportManagement.Documents;
using PharmaLex.VigiLit.ImportManagement.Documents.Import;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.AdHoc;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.ImportManagement.Ui.FailedImportFiles;
using PharmaLex.VigiLit.ImportManagement.Ui.FileImport;
using PharmaLex.VigiLit.ImportManagement.Ui.Generics;
using PharmaLex.VigiLit.ImportManagement.Ui.ManualEntry;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.ImportManagement.Ui.Services;

namespace PharmaLex.VigiLit.ImportManagement.Ui;
public static class ConfigureServices
{
    public static void RegisterImportManagementUi(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IAdHocImportContractRepository, AdHocImportContractRepository>();
        services.AddScoped<IImportContractReferenceClassificationRepository, ImportContractReferenceClassificationRepository>();
        services.AddScoped<IImportSelectionRepository, ImportSelectionRepository>();
        services.AddScoped<IReferenceClassificationLockRepository, ReferenceClassificationLockRepository>();
        services.AddScoped<IImportContractRepository, ImportContractRepository>();
        services.AddScoped<IImportRepository, ImportRepository>();
        services.AddScoped<IImportFileRepository, ImportFileRepository>();

        services.AddScoped<IDashboardService, DashboardService>();
        services.AddScoped<ICompanyRepository, CompanyRepository>();
        services.AddScoped<IImportService, ImportService>();

        services.AddScoped<IImportReferenceRepository, ImportReferenceRepository>();

        services.AddScoped<IGenericCardRepository<ImportManualEntry>, GenericCardRepository<ImportManualEntry>>();
        services.AddScoped<IManualEntryService, ManualEntryService>();
        services.AddScoped<IGenericCardRepository<FailedImportFile>, GenericCardRepository<FailedImportFile>>();
        services.AddScoped<IFailedImportFileService, FailedImportFileService>();

        services.AddScoped<IImportFileRepository, ImportFileRepository>();
        services.AddScoped<IImportFileService, ImportFileService>();

        services.AddScoped<IAdHocImportRepository, AdHocImportRepository>();
        services.AddScoped<IAdHocService, AdHocService>();

        var assembly = typeof(ConfigureServices).Assembly;
        services.AddAutoMapper(assembly);

        services.AddControllersWithViews()
            .AddRazorRuntimeCompilation()
            .AddApplicationPart(assembly);

        services.Configure<MvcRazorRuntimeCompilationOptions>(options =>
            { options.FileProviders.Add(new EmbeddedFileProvider(assembly)); });
    }
    public static void AddImportFileUploadDocumentServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.RegisterDocumentBlobServices(configuration);
        services.AddImportFileDocumentServices(configuration);

        services.AddSingleton<IEnumerable<AzureStorageDocumentOptions>>(sp =>
        {
            return new List<AzureStorageDocumentOptions>
            {
                sp.GetRequiredService<IOptions<AzureStorageImportFileDocumentUploadOptions>>().Value,
                sp.GetRequiredService<IOptions<AzureStorageImportFileDocumentOptions>>().Value
            };
        });
    }

    private static void AddImportFileDocumentServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<AzureStorageImportFileDocumentUploadOptions>(configuration.GetSection(AzureStorageImportFileDocumentUploadOptions.ConfigurationKey));
        services.Configure<AzureStorageImportFileDocumentOptions>(configuration.GetSection(AzureStorageImportFileDocumentOptions.ConfigurationKey));
        services.AddScoped<IImportFileDocumentService, ImportFileDocumentService>();
    }
}
