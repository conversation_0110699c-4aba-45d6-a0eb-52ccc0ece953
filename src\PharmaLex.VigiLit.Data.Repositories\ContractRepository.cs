using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Data.Repositories;

public class ContractRepository(PlxDbContext context, IUserContext userContext)
    : TrackingGenericRepository<Contract>(context, userContext.User), IContractRepository
{
    public async Task<IEnumerable<Contract>> GetAllAsync()
    {
        return await context.Set<Contract>()
                .Include(c => c.Project).ThenInclude(p => p.Company)
                .Include(c => c.Substance)
                .Include(c => c.ContractVersionsInternal).ThenInclude(cvi => cvi.ContractVersionJournals)
                // Only contracts with current contract version Id
                .Where(c => c.CurrentContractVersionId != 0)
                .OrderBy(c => c.Project.Company.Name)
                   .ThenBy(c => c.Project.Name)
                   .ThenBy(c => c.Substance.Name)
                .AsNoTracking()
                .ToListAsync();
    }

    public async Task<IEnumerable<Contract>> GetAllToExportAsync()
    {
        return await context.Set<Contract>()
                .Include(c => c.Project).ThenInclude(p => p.Company)
                .Include(c => c.Substance)
                .Include(c => c.ContractVersionsInternal)
                .ThenInclude(x => x.ContractVersionJournals)
                .ThenInclude(x => x.Journal)
                // Only contracts with current contract version Id
                .Where(c => c.CurrentContractVersionId != 0)
                // Ordering should match GetAllAsync() as export order should match table view.
                .OrderBy(c => c.Project.Company.Name)
                    .ThenBy(c => c.Project.Name)
                    .ThenBy(c => c.Substance.Name)
                .AsNoTracking()
                .ToListAsync();
    }

    public async Task<IEnumerable<Contract>> GetForAdHocImportCreate(int companyId, int projectId = 0)
    {
        if (companyId == 0)
        {
            return [];
        }

        return await context.Set<Contract>()
                .Include(c => c.Project)
                .Include(c => c.Substance)
                .Include(c => c.ContractVersionsInternal)
                .Where(c =>
                    // for the selected company
                    c.Project.CompanyId == companyId
                    // if a project is selected then also for the selected project
                    && (projectId == 0 || c.ProjectId == projectId)
                    //  current contract version must be active
                    && c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.IsActive
                    // exclude contracts without a search string
                    && c.ContractVersionsInternal.Any())
                .OrderBy(c => c.Project.Name)
                .ThenBy(c => c.Substance.Name)
                .AsNoTracking()
                .ToListAsync();
    }

    public async Task<Contract?> GetContractDetails(int id)
    {
        return await context.Set<Contract>()
                .Include(c => c.Project)
                .Include(c => c.Substance)
                .Include(c => c.ContractVersionsInternal.OrderByDescending(cv => cv.Version))
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<Contract?> GetContractDetailsForAdminUsers(int id)
    {
        return await context.Set<Contract>()
            .Include(c => c.Project)
            .Include(c => c.Substance)
            .Include(c => c.ContractVersionsInternal.OrderByDescending(cv => cv.Version)).ThenInclude(cv => cv.User)
            .Include(c => c.ContractVersionsInternal.OrderByDescending(cv => cv.Version))
            .ThenInclude(x => x.ContractVersionJournals)
            .ThenInclude(x => x.Journal)
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<Contract?> GetContractWithLatestVersion(int contractId)
    {
        return await context.Set<Contract>()
            .Include(c => c.Project)
            .Include(c => c.Substance)
            .Include(c => c.ContractVersionsInternal.OrderByDescending(cv => cv.Version).Take(1))
            .ThenInclude(x => x.ContractVersionJournals)
            .ThenInclude(x => x.Journal)
            .AsNoTracking()
            .FirstOrDefaultAsync(c => c.Id == contractId);
    }

    public async Task<Contract?> GetByIdAsync(int id)
    {
        return await context.Set<Contract>()
                    .Include(c => c.ContractVersionsInternal)
                        .ThenInclude(x => x.ContractVersionJournals)
                        .ThenInclude(x => x.Journal)
                        .ThenInclude(x => x.Country)
                    .FirstOrDefaultAsync(u => u.Id == id);
    }

    public async Task<bool> DoesContractBelongToCompany(int companyId, int contractId)
    {
        return await context.Set<Contract>()
            .Include(c => c.Project)
            .AnyAsync(c => c.Id == contractId && c.Project.CompanyId == companyId);
    }

    public async Task<IEnumerable<Contract>> GetContractsForScheduledImport(DateTime importDate)
    {
        var query = context.Set<Contract>()
            .Include(c => c.Substance)
            .Include(c => c.ContractVersionsInternal)
            .Include(c => c.Project)
                .ThenInclude(p => p.Company)
            .Where(c =>
                // active 
                c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.IsActive
                // current contract version type is scheduled 
                && c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.ContractType == ContractType.Scheduled
                // contract is configured for this weekday
                && c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.ContractWeekday == (ContractWeekday)importDate.DayOfWeek
                // company must be active
                && c.Project.Company.IsActive
                // exclude contracts without a search string
                && c.ContractVersionsInternal.Any())
            .OrderBy(c => c.Project.Company.Name)
                .ThenBy(c => c.Project.Name)
                .ThenBy(c => c.Substance.Name)
            .AsNoTracking();

        return await query.ToListAsync();
    }
}
