using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public interface IImportSelectionRepository : ITrackingRepository<ImportSelection>
{
    Task Select(int userId, int importId);
    Task Deselect(int userId, int importId);
    Task<ImportSelectionModel?> GetSelectedImport(int userId);
    Task ClearSelections(int importId);
}