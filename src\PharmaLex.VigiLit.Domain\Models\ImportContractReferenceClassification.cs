﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Domain.Models;

public class ImportContractReferenceClassification : VigiLitEntityBase
{
    // Link a classification to an import contract
    public int ReferenceClassificationId { get; set; }
    public ReferenceClassification ReferenceClassification { get; set; }
    public int ImportContractId { get; set; }
    public ImportContract ImportContract { get; set; }

    public ICRCType ICRCType { get; set; }

    public int ImportId { get; set; }
    public Import Import { get; set; }

    public ImportContractReferenceClassification() 
    { 
    }

    public ImportContractReferenceClassification(ImportContract importContract, ReferenceClassification referenceClassification, ICRCType type)
    {
        ImportContract = importContract ?? throw new ArgumentNullException(nameof(importContract));
        ReferenceClassification = referenceClassification ?? throw new ArgumentNullException(nameof(referenceClassification));
        ICRCType = type;
        ImportId = importContract.ImportId;
    }
}

