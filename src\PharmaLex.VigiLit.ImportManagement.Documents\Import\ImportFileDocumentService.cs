﻿using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;

namespace PharmaLex.VigiLit.ImportManagement.Documents.Import;
internal class ImportFileDocumentService : IImportFileDocumentService
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageImportFileDocumentUploadOptions _importFileDocumentUploadOptions;
    private readonly AzureStorageImportFileDocumentOptions _importFileDocumentOptions;

    public ImportFileDocumentService(
        IDocumentService documentService,
        IOptions<AzureStorageImportFileDocumentUploadOptions> importFileImportDocumentUploadOptions,
        IOptions<AzureStorageImportFileDocumentOptions> importFileImportDocumentOptions)

    {
        _documentService = documentService;
        _importFileDocumentUploadOptions = importFileImportDocumentUploadOptions.Value;
        _importFileDocumentOptions = importFileImportDocumentOptions.Value;
    }

    public async Task Create(ImportFileUploadDescriptor importFileUploadDescriptor, Stream stream, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(importFileUploadDescriptor);
        stream.Position = 0;
        await _documentService.Create(documentDescriptorUpload, stream, cancellationToken);
    }

    public async Task Delete(ImportFileUploadDescriptor importFileUploadDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(importFileUploadDescriptor);
        await _documentService.Delete(documentDescriptor, cancellationToken);
    }
    public async Task<bool> Exists(ImportFileUploadDescriptor importFileUploadDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(importFileUploadDescriptor);
        var exists = await _documentService.Exists(documentDescriptor, cancellationToken);
        return exists;
    }

    public async Task<DocumentProperties> GetDocumentProperties(ImportFileUploadDescriptor importFileUploadDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(importFileUploadDescriptor);
        return await _documentService.GetDocumentProperties(documentDescriptorUpload, cancellationToken);
    }

    public async Task<Stream> OpenRead<T>(T importFileDescriptor, CancellationToken cancellationToken = default)
    {
        DocumentDescriptor documentDescriptor = importFileDescriptor switch
        {
            ImportFileUploadDescriptor uploadDescriptor => GetDocumentDescriptor(uploadDescriptor),
            ImportFileDescriptor fileDescriptor => GetDocumentDescriptor(fileDescriptor),
            _ => throw new ArgumentException($"Unsupported type: {typeof(T).Name}")
        };
        var stream = await _documentService.OpenRead(documentDescriptor, cancellationToken);
        return stream;
    }

    public async Task<Stream> OpenRead(ImportFileDescriptor importFileDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(importFileDescriptor);
        var stream = await _documentService.OpenRead(documentDescriptor, cancellationToken);
        return stream;
    }

    public async Task<DocumentProperties> Copy(ImportFileUploadDescriptor importFileUploadDescriptor, ImportFileDescriptor importFileDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(importFileUploadDescriptor);
        var documentDescriptor = GetDocumentDescriptor(importFileDescriptor);
        var documentProperties = await _documentService.CopyFrom(documentDescriptorUpload, documentDescriptor, cancellationToken);
        return documentProperties;
    }

    private DocumentDescriptor GetDocumentDescriptor(ImportFileUploadDescriptor importFileUploadDescriptor)
    {
        var blobName = importFileUploadDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_importFileDocumentUploadOptions.AccountName, _importFileDocumentUploadOptions.ContainerName, blobName);
        return documentDescriptor;
    }

    private DocumentDescriptor GetDocumentDescriptor(ImportFileDescriptor importFileDescriptor)
    {
        var blobName = importFileDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_importFileDocumentOptions.AccountName, _importFileDocumentOptions.ContainerName, blobName);
        return documentDescriptor;
    }
}
