﻿using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

/// <summary>
/// Interface for managing user claims.
/// </summary>
public interface IClaimRepository : ITrackingRepository<Claim>
{
    /// <summary>
    /// Retrieves all claims.
    /// </summary>
    /// <returns>A collection of claim models.</returns>
    IEnumerable<ClaimModel> GetAll();

    /// <summary>
    /// Asynchronously retrieves all claims.
    /// </summary>
    /// <returns>A collection of claim entities.</returns>
    Task<IEnumerable<Claim>> GetAllAsync();

    /// <summary>
    /// Asynchronously retrieves a claim by its ID.
    /// </summary>
    /// <param name="id">The ID of the claim to retrieve.</param>
    /// <returns>The claim with the specified ID, if found; otherwise, null.</returns>
    Task<Claim?> GetByIdAsync(int id);

    /// <summary>
    /// Asynchronously retrieves a claim by its name.
    /// </summary>
    /// <param name="name">The name of the claim to retrieve.</param>
    /// <returns>The claim with the specified name, if found; otherwise, null.</returns>
    Task<Claim?> GetByNameAsync(string name);
}
