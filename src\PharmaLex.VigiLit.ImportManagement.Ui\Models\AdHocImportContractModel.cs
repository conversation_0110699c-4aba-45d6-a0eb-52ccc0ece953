﻿namespace PharmaLex.VigiLit.ImportManagement.Ui.Models;

public class AdHocImportContractModel
{
    public int Id { get; set; }

    public int ContractId { get; set; }
    public int SubstanceId { get; set; }
    public int ProjectId { get; set; }
    public int CompanyId { get; set; }

    public required string SubstanceName { get; set; }
    public required string ProjectName { get; set; }
    public required string CompanyName { get; set; }

    public required string ContractType { get; set; }
    public required string SearchPeriod { get; set; }
}