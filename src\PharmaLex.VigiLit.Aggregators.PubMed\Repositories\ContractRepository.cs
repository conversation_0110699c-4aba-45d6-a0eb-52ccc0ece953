﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Repositories;

internal class ContractRepository : TrackingGenericRepository<Contract>, IContractRepository
{
    public ContractRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<Contract?> Get(int id)
    {
        return await context.Set<Contract>()
            .Include(c => c.ContractVersionsInternal)
            .FirstOrDefaultAsync(u => u.Id == id);

    }
}