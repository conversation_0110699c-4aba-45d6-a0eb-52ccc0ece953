﻿using PharmaLex.Core.UserManagement;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserSessionManagement.Entities;

public class UserSession : CoreEntityBase<int>
{
    public int UserId { get; set; }

#pragma warning disable CS8618
    public IUserEntity User { get; set; }
#pragma warning restore CS8618
    public string? SessionData { get; set; }

#pragma warning disable CS8618
    public string IpAddress { get; set; }
#pragma warning restore CS8618

    public DateTime SessionExpiry { get; set; }
}