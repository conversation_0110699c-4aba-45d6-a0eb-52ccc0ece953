﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class ReferenceClassificationMappingProfile : Profile
{
    public ReferenceClassificationMappingProfile()
    {
        CreateMap<ReferenceClassification, ReferenceClassificationModel>()
            .ForMember(d => d.ReferenceStateText,
                s => s.MapFrom(x => x.ReferenceState.ToString()))
            .ForMember(d => d.PreclassifierName,
                s => s.MapFrom(x => x.Classifier != null ? $"{x.Classifier.GivenName} {x.Classifier.FamilyName}" : null));

        CreateMap<ReferenceClassification, ReferenceClassificationWithReferenceModel>()
            .ForMember(d => d.ReferenceStateText,
                s => s.MapFrom(x => x.ReferenceState.ToString()))
            .ForMember(d => d.PreclassifierName,
                s => s.MapFrom(x => x.Classifier != null ? $"{x.Classifier.GivenName} {x.Classifier.FamilyName}" : null))
            .ForMember(d => d.PSURRelevanceAbstract,
                s => s.MapFrom(x => (int)x.PSURRelevanceAbstract));

        CreateMap<ReferenceClassification, ReferenceClassificationWithReferenceSupportModel>()
            .ForMember(d => d.ReferenceStateText,
                s => s.MapFrom(x => x.ReferenceState.ToString()))
            .ForMember(d => d.LastUpdatedDate,
                s => s.MapFrom(x => x.LastUpdatedDate.ToString("dd MMMM yyyy")))
            .ForMember(d => d.PreclassifierName,
                s => s.MapFrom(x => x.Classifier != null ? $"{x.Classifier.GivenName} {x.Classifier.FamilyName}" : null));

        CreateMap<ReferenceClassificationWithReferenceModel, ReferenceClassification>();

        CreateMap<ReferenceClassification, ReferenceClassificationWithSubstanceModel>()
            .ForMember(d => d.ReferenceStateText,
                s => s.MapFrom(x => x.ReferenceState.ToString()))
            .ForMember(d => d.PSURRelevanceAbstract,
                s => s.MapFrom(x => x.PSURRelevanceAbstract.GetDescription()))
            .ForMember(d => d.PreclassifierName,
                s => s.MapFrom(x => x.Classifier != null ? $"{x.Classifier.GivenName} {x.Classifier.FamilyName}" : null));

        CreateMap<ReferenceClassification, ReferenceClassificationSendGridTemplateModel>();

        CreateMap<ReferenceClassificationModel, ReferenceClassification>();

        CreateMap<ReferenceClassification, ReferenceClassificationSupportModel>()
            .ForMember(d => d.Id, s => s.MapFrom(x => x.Id))
            .ForMember(d => d.SourceId, s => s.MapFrom(x => x.Reference.SourceId))
            .ForMember(d => d.Doi, s => s.MapFrom(x => x.Reference.Doi))
            .ForMember(d => d.Title, s => s.MapFrom(x => x.Reference.Title))
            .ForMember(d => d.Abstract, s => s.MapFrom(x => x.Reference.Abstract))
            .ForMember(d => d.Authors, s => s.MapFrom(x => x.Reference.Authors))
            .ForMember(d => d.MeshTerms, s => s.MapFrom(x => x.Reference.MeshHeadings))
            .ForMember(d => d.Keywords, s => s.MapFrom(x => x.Reference.Keywords))
            .ForMember(d => d.AffiliationTextFirstAuthor, s => s.MapFrom(x => x.Reference.AffiliationTextFirstAuthor))
            .ForMember(d => d.FullPagination, s => s.MapFrom(x => x.Reference.FullPagination))
            .ForMember(d => d.Issn, s => s.MapFrom(x => x.Reference.Issn))
            .ForMember(d => d.Issue, s => s.MapFrom(x => x.Reference.Issue))
            .ForMember(d => d.Volume, s => s.MapFrom(x => x.Reference.Volume))
            .ForMember(d => d.PublicationType, s => s.MapFrom(x => x.Reference.PublicationType))
            .ForMember(d => d.PublicationYear, s => s.MapFrom(x => x.Reference.PublicationYear))
            .ForMember(d => d.ClassificationCategory, s => s.MapFrom(x => x.ClassificationCategory.Name))
            .ForMember(d => d.Substance, s => s.MapFrom(x => x.Substance.Name))
            .ForMember(d => d.JournalTitle, s => s.MapFrom(x => x.Reference.JournalTitle));

        CreateMap<AiSuggestedClassificationStoredProcResult, AiSuggestedClassificationModel>();
    }
}
