﻿using PharmaLex.VigiLit.ReferenceManagement;

namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;

internal interface IReferenceMatcher
{
    /// <summary>
    /// Matches the specified match condition against the reference.
    /// </summary>
    /// <param name="referenceOperand">The match operand which is a reference.</param>
    /// <param name="matchCondition">The match condition.</param>
    /// <param name="journalTitles">Collection of journal titles to match</param>
    /// <returns>True if the reference satisfies the condition, otherwise false</returns>
    Task<bool> Matches(IReference referenceOperand, string matchCondition, IReadOnlyCollection<string> journalTitles);
}