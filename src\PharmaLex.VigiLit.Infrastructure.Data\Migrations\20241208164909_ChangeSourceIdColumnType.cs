﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class ChangeSourceIdColumnType : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_References_SourceId",
                table: "References");

            migrationBuilder.DropIndex(
                name: "IX_References_Doi_Id",
                table: "References");

            migrationBuilder.Sql("ALTER TABLE [References] SET (SYSTEM_VERSIONING = OFF);");

            migrationBuilder.DropIndex(
                name: "IX_ReferencesHistory_Id_PeriodEnd_PeriodStart",
                table: "ReferencesHistory");

            migrationBuilder.Sql("ALTER TABLE [References] ALTER COLUMN SourceId NVARCHAR(450);");
            migrationBuilder.Sql("ALTER TABLE [ReferencesHistory] ALTER COLUMN SourceId NVARCHAR(450);");
            migrationBuilder.Sql("ALTER TABLE [References] SET (SYSTEM_VERSIONING = ON (HISTORY_TABLE = dbo.[ReferencesHistory]));");
            migrationBuilder.Sql("ALTER TABLE [ReferenceUpdates] ALTER COLUMN SourceId NVARCHAR(450);");
            migrationBuilder.Sql("CREATE NONCLUSTERED INDEX [IX_References_Doi_Id] ON [dbo].[References] ([Doi] ASC,[Id] ASC) INCLUDE(Abstract,AffiliationTextFirstAuthor,Authors,CountryOfOccurrence,DateRevised,FullPagination,Issn,Issue,[Language],SourceId,PublicationType,PublicationYear,Title,Volume,VolumeAbbreviation,PeriodEnd,PeriodStart,CreatedDate,CreatedBy,LastUpdatedDate,LastUpdatedBy,Keywords,MeshHeadings,JournalTitle)");

            migrationBuilder.Sql("ALTER TABLE [AISuggestions] ALTER COLUMN SourceId NVARCHAR(450);");

            migrationBuilder.CreateIndex(
                name: "IX_References_SourceId",
                table: "References",
                column: "SourceId",
                unique: true);

            migrationBuilder.Sql("CREATE NONCLUSTERED INDEX [IX_ReferencesHistory_Id_PeriodEnd_PeriodStart] ON [dbo].[ReferencesHistory] ([Id],[PeriodEnd],[PeriodStart]) INCLUDE(Abstract, AffiliationTextFirstAuthor, Authors, CountryOfOccurrence, DateRevised, Doi, [Language], SourceId, Title, Keywords, MeshHeadings)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // VigiLit does not go back in versions
        }
    }
}
