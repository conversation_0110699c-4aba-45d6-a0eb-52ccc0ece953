DECLARE @firstName varchar(50) = 'ADD FIRST NAME HERE'
DECLARE @surname varchar(50) = 'ADD SURNAME NAME HERE'
DECLARE @ClaimName varchar(50) = 'SuperAdmin'
DECLARE @email varchar(50) = SUSER_NAME()

DECLARE @claimId int

SELECT @claimId = id from Claims WHERE Name = @ClaimName

DECLARE @Id int

BEGIN TRAN
    INSERT INTO [dbo].[Users]
    ([Email],[GivenName],[FamilyName],[LastLoginDate],[QCPercentage],[ActivationExpiryDate],[InvitationEmailLink],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
     VALUES (@email,@firstName,@surname ,null,10,null,null,GETUTCDATE(),@email,GETUTCDATE(),@email)

    SET @Id = SCOPE_IDENTITY()

    INSERT INTO [dbo].[UserClaims] ([ClaimsInternalId],[UsersInternalId])
    VALUES (@claimId,@Id)    
ROLLBACK TRAN