﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;
internal interface IImportingContractRepository : ITrackingRepository<Contract>
{
    /// <summary>
    /// Retrieves all active and approved <see cref="ContractVersion"/> records from the database
    /// that are associated with active companies. If the contract is of type <c>Local</c>,
    /// it includes only those with at least one enabled journal.
    /// </summary>
    /// <remarks>
    /// A contract version with no journals is considered a global contract,
    /// while a contract version with journals (filtered by enabled ones for local screening)
    /// is considered a local contract.
    /// </remarks>
    /// <returns>
    /// A collection of <see cref="ContractVersion"/> entities meeting the specified criteria.
    /// </returns>
    Task<IEnumerable<ContractVersion>> GetContractsToMatchOn();

}
