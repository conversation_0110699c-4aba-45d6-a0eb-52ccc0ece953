﻿namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

public class ExtractRequest
{
    /// <summary>
    /// Gets or sets the batch identifier.
    /// </summary>
    /// <value>
    /// The batch identifier as a string (typically a GUID).
    /// </value>
    public required string BatchId { get; set; }

    /// <summary>
    /// Gets or sets the file name.
    /// </summary>
    /// <remarks>This is the name of the file stored in Azure Blob Storage.</remarks>
    /// <value>
    /// The name of the file.
    /// </value>
    public required string FileName { get; set; }

    /// <summary>
    /// Value unique to the extract/file (which gets passed to PhlexVision)
    /// </summary>
    /// <value>
    /// Tracking id 
    /// </value>
    public Guid CorrelationId { get; set; }
}

