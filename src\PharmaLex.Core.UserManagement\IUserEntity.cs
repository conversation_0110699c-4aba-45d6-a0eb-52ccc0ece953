﻿using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserManagement;
public interface IUserEntity : IEntity
{
    public string Email { get; set; }
    public string GivenName { get; set; }
    public string FamilyName { get; set; }
    public IReadOnlyCollection<IClaimEntity> GetClaims();
    public bool HasActiveCompany();
    public bool IsCompanyUser();
    public int GetActiveCompanyId();
}
