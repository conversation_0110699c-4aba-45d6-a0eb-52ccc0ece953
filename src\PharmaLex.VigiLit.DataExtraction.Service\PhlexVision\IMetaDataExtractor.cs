﻿using PharmaLex.VigiLit.DataExtraction.Service.Data;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

/// <summary>
/// Interface for extracting metadata from a response stream.
/// </summary>
public interface IMetaDataExtractor
{
    /// <summary>
    /// Gets the extracted metadata.
    /// </summary>
    /// <param name="responseBodyStream">The response body stream.</param>
    /// <param name="correlationId">The correlation identifier.</param>
    /// <returns></returns>
    Task<(ContextInfo, ExtractedMetadata?)> GetExtractedMetaData(Stream responseBodyStream, Guid correlationId);
}
