using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using NewRelic.Api.Agent;

namespace PharmaLex.VigiLit.ImportApp.Functions.HealthCheck
{
    public class ImportAppHealthCheck
    {
        private readonly ILogger<ImportAppHealthCheck> _logger;
        private readonly IConfiguration _configuration;
        private readonly IFeatureManager _featureManager;
        private const string AiMessageBusSend = "AiMessageBusSend";

        public ImportAppHealthCheck(ILogger<ImportAppHealthCheck> logger, IConfiguration configuration, IFeatureManager featureManager)
        {
            _logger = logger;
            _configuration = configuration;
            _featureManager = featureManager;
        }

        [Function("Import_HealthCheck")]
        public async Task<IActionResult> Run(
        [HttpTrigger("get", Route = "health")] HttpRequest req)
        {
            _logger.LogInformation("Health check endpoint hit.");
            var appName = _configuration.GetValue<string>("Static:App");
            var aiMessageBusSend = await _featureManager.IsEnabledAsync(AiMessageBusSend);

            return new OkObjectResult(new
            {
                appName = appName,
                aiMessageBusSend = aiMessageBusSend
            });
        }
    }
}
