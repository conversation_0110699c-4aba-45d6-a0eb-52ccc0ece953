using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using PharmaLex.Authentication.B2C;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Application.Services;

public class CompanyUserService : ICompanyUserService
{

    private readonly IEmailMaintenanceService _emailMaintenanceService;
    private readonly ICompanyUserRepository _companyUserRepository;
    private readonly IAzureAdB2CGraphService _graphService;
    private readonly IUserRepository _userRepository;
    private readonly IEmailPreferenceRepository _emailPreferenceRepository;
    private readonly IConfiguration _configuration;
    private readonly IEmailService _emailService;
    private readonly IUserService _userService;
    private readonly IOidcService _oidcService;
    private readonly HttpContext _context;
    private readonly IMapper _mapper;
    private readonly IClaimRepository _claimsRepository;

    DateTime activationExpiryDate => DateTime.UtcNow.AddDays(_configuration.GetValue<int>("AzureAdB2CPolicy:LinkExpiresAfterDays"));

    public CompanyUserService(ICompanyUserRepository companyUserRepository,
        IEmailMaintenanceService emailMaintenanceService,
        IAzureAdB2CGraphService graphService,
        IUserRepository userRepository,
        IEmailPreferenceRepository emailPreferenceRepository,
        IConfiguration configuration,
        IHttpContextAccessor context,
        IEmailService emailService,
        IOidcService oidcService,
        IUserService userService,
        IMapper mapper,
        IClaimRepository claimsRepository)
    {
        _emailMaintenanceService = emailMaintenanceService;
        _companyUserRepository = companyUserRepository;
        _userRepository = userRepository;
        _emailPreferenceRepository = emailPreferenceRepository;
        _configuration = configuration;
        _context = context.HttpContext;
        _emailService = emailService;
        _graphService = graphService;
        _oidcService = oidcService;
        _userService = userService;
        _mapper = mapper;
        _claimsRepository = claimsRepository;
    }

    public async Task<string> AddNewCompanyUser(CompanyUserModel companyUserModel)
    {
        var message = "";

        if (companyUserModel.Email.ToLower().Contains("pharmalex.com"))
        {
            return message = "A Pharmalex user cannot be added to an external company";
        }

        if (await _userService.GetUser(companyUserModel.Email) == null)
        {

            var invitation = await InitialiseInvitationEmail(companyUserModel);

            await CreateCompanyUser(companyUserModel.CompanyId, companyUserModel.GivenName, companyUserModel.FamilyName,
                companyUserModel.Email, activationExpiryDate, invitation.RegisterUrl, companyUserModel.EmailPreferenceIds, companyUserModel.CompanyUserType);

            await _emailService.SendInvitationEmail(invitation);
        }
        else
        {
            // Check do user email being added already belong to current company
            var emailExistForCurrentCompany = await _userService.GetCompanyUser(companyUserModel.Email, companyUserModel.CompanyId);
            if (emailExistForCurrentCompany != null)
            {
                return message = "User already exists";
            }
            else
            {
                // Check If user email exist and is an inactive user for another company
                var isInactiveUser = await CheckForInactiveCompanyUser(companyUserModel.Email);
                if (isInactiveUser != null)
                {
                    // Update the company Id for the user and set as active for current company
                    await UpdateCompanyUser(isInactiveUser.Id, companyUserModel.CompanyId, true, companyUserModel.GivenName, companyUserModel.FamilyName, companyUserModel.Email, companyUserModel.EmailPreferenceIds, companyUserModel.CompanyUserType);
                    var invitation = await InitialiseInvitationEmail(companyUserModel);
                    await _emailService.SendInvitationEmail(invitation);
                }
                else
                {
                    // Else user email exist and an active user belongs to another company
                    return message = "User already exists in another company";
                }

            }

        }

        return message;
    }

    public async Task UpdateCompanyUser(int id, int companyId, bool active, string givenName, string familyName, string email, List<int> emailPreferenceIds, CompanyUserType companyUserType)
    {
        var user = await _userRepository.GetById(id);

        if (user == null)
        {
            throw new ArgumentOutOfRangeException($"User with id '{id}' not found");
        }

        user.UpdateUserData(givenName, familyName, email);

        //Check and create associated company user if pharmalex internal user already exist
        if (companyUserType == CompanyUserType.Internal && user.CompanyUser == null)
        {
            var companyUser = new CompanyUser { User = user, CompanyId = companyId, Active = active, CompanyUserType = companyUserType };
            _companyUserRepository.Add(companyUser);
            user.AddClaim(await _claimsRepository.GetByNameAsync(Domain.UserManagement.Claims.PharmaLexClientResearcher));
        }

        user.CompanyUser.Active = active;
        user.CompanyUser.CompanyId = companyId;

        if (companyUserType == CompanyUserType.External)
        {

            var claim = user.GetClaims().Where(c => c.Name == Domain.UserManagement.Claims.ClientResearcher).SingleOrDefault();
            if (active)
            {
                if (claim == null)
                {
                    user.AddClaim(await _claimsRepository.GetByNameAsync(Domain.UserManagement.Claims.ClientResearcher));
                }
            }
            else
            {
                if (claim != null)
                {
                    user.RemoveClaim(claim.Id);
                }
            }

        }

        await UpdateUserEmailPreferencesAsync(id, emailPreferenceIds);
        await _userRepository.SaveChangesAsync();
    }

    private async Task UpdateUserEmailPreferencesAsync(int userId, List<int> emailPreferenceIds)
    {
        var availableEmailPreferences = await GetEmailPreferencesAsync();
        var selectedEmailPreferences = availableEmailPreferences.Where(ep => emailPreferenceIds.Contains(ep.Id));

        var selectedEmailPreferenceIds = selectedEmailPreferences.Select(ep => ep.Id).ToList();
        var user = await _userRepository.GetById(userId);

        var addedEmailpreferences = selectedEmailPreferences.Where(c => !user.UserEmailPreferences.Any(x => x.EmailPreferenceId == c.Id)).ToList();

        var removedEmailpreferences = user.UserEmailPreferences.Where(uep => !selectedEmailPreferenceIds.Contains(uep.EmailPreferenceId));

        foreach (var emailPreference in addedEmailpreferences)
        {
            UserEmailPreference userEmailPreference = new(user.Id, emailPreference.Id);
            user.UserEmailPreferences.Add(userEmailPreference);
        }

        foreach (var emailPreference in removedEmailpreferences)
        {
            user.UserEmailPreferences.Remove(emailPreference);
        }

    }

    public async Task<string> AddNewInternalUser(CompanyUserModel companyUser)
    {
        var message = "";

        var existingUser = await _userService.GetUser(companyUser.Email);

        if (existingUser == null)
        {
            await CreateCompanyUser(companyUser.CompanyId, companyUser.GivenName, companyUser.FamilyName, companyUser.Email, activationExpiryDate, null, companyUser.EmailPreferenceIds, companyUser.CompanyUserType);
        }
        else
        {
            // Check do user email being added already belong to current company
            var emailExistForCurrentCompany = await _userService.GetCompanyUser(companyUser.Email, companyUser.CompanyId);
            var existingInternalUser = await _userRepository.GetById(existingUser.Id);

            var claim = existingInternalUser.GetClaims().Where(c => c.Name == Claims.PharmaLexClientResearcher).SingleOrDefault();

            if (emailExistForCurrentCompany != null && claim != null)
            {
                return message = "User already exists";

            }
            else
            {
                // Update the company Id for the user for current company
                await UpdateCompanyUser(existingInternalUser.Id, companyUser.CompanyId, true, companyUser.GivenName, companyUser.FamilyName, companyUser.Email, companyUser.EmailPreferenceIds, companyUser.CompanyUserType);
            }
        }
        return message;
    }

    public async Task<UserModel> CreateCompanyUser(int companyId, string givenName, string familyName, string email, DateTime activationExpiryDate, string activationLink, List<int> emailPreferenceIds, CompanyUserType companyUserType)
    {
        var user = new User(givenName, familyName, email);
        var companyUser = new CompanyUser { User = user, CompanyId = companyId, Active = true, CompanyUserType = companyUserType };

        if (companyUserType == CompanyUserType.External)
        {
            user.ActivationExpiryDate = activationExpiryDate;
            user.InvitationEmailLink = activationLink;
            user.AddClaim(await _claimsRepository.GetByNameAsync(Domain.UserManagement.Claims.ClientResearcher));

        }
        else if (companyUserType == CompanyUserType.Internal)
        {
            user.AddClaim(await _claimsRepository.GetByNameAsync(Domain.UserManagement.Claims.PharmaLexClientResearcher));
        }
        user.CompanyUser = companyUser;
        _userRepository.Add(user);

        await AddEmailPreferences(emailPreferenceIds, user);

        await _userRepository.SaveChangesAsync();

        return _mapper.Map<UserModel>(user);
    }

    private async Task AddEmailPreferences(List<int> emailPreferenceIds, User user)
    {
        if (emailPreferenceIds != null && emailPreferenceIds.Any())
        {
            var emailPreferences = await _emailPreferenceRepository.GetAllAsync();

            var selectedEmailPreferences = emailPreferences.Where(c => emailPreferenceIds.Contains(c.Id));

            if (emailPreferenceIds.Count != selectedEmailPreferences.Count())
            {
                throw new ArgumentException("Invalid emailPreferenceId provided");
            }

            foreach (var emailPreference in selectedEmailPreferences)
            {
                var newUserEmailPreference = new UserEmailPreference() { EmailPreferenceId = emailPreference.Id };
                user.UserEmailPreferences.Add(newUserEmailPreference);
            }
        }
    }

    public async Task<CompanyUserModel> ResendSignOnInvitation(CompanyUserModel model)
    {
        if (model.LastLoginDate.HasValue)
        {
            throw new ArgumentException("User has already logged in");
        }

        var invitation = await InitialiseInvitationEmail(model);

        await _emailService.SendInvitationEmail(invitation);

        model.ActivationExpiryDate = activationExpiryDate;
        model.InvitationEmailLink = invitation.RegisterUrl;

        await _userService.Update(model);

        return await GetCompanyUser(model.CompanyId, model.Id);
    }

    public async Task<InvitationEmailModel> InitialiseInvitationEmail(CompanyUserModel companyUser)
    {
        var invitationLink = await CreateInvitationLink(companyUser);

        return new InvitationEmailModel
        {
            CompanyUser = companyUser,
            RegisterUrl = invitationLink,
            Subject = "VigitLit Registration",
            ExpiryDays = _configuration.GetValue<int>("AzureAdB2CPolicy:LinkExpiresAfterDays"),
        };
    }

    public async Task<string> CreateInvitationLink(CompanyUserModel companyUser)
    {
        var invitationLink = "";
        var adUser = await Find(companyUser.Email);

        if (adUser.Count == 0)
        {
            // user is not in Azure Active Directory so send the link to signup 
            invitationLink = _oidcService.GetSignupInvitationLink("signup-invitation",
                new Dictionary<string, string>
                {
                    { "displayName", $"{companyUser.GivenName} {companyUser.FamilyName}" },
                    { "givenName", companyUser.GivenName },
                    { "surname", companyUser.FamilyName },
                    { "email", companyUser.Email }
                });
        }
        else
        {
            //  user is already in Azure Active Directory so send link to log in
            invitationLink = $"{_context.Request.Scheme}://{_context.Request.Host}";
        }
        return invitationLink;
    }

    public async Task<List<UserFullModel>> Find(string term)
    {
        var allUsers = await _userRepository.GetAllAsync(shouldHaveClaims: false);

        List<UserFullModel> dbUsers = allUsers.Where(x =>
            x.Email.StartsWith(term, StringComparison.InvariantCultureIgnoreCase)
            || x.GivenName.StartsWith(term, StringComparison.InvariantCultureIgnoreCase)
            || x.FamilyName.StartsWith(term, StringComparison.InvariantCultureIgnoreCase)
        ).ToList();

        List<Microsoft.Graph.Models.User> graphUsers = await _graphService.FindUsers(term);

        var graphUsersNotInDb = _mapper.Map<List<UserFullModel>>(graphUsers.Where(x => !dbUsers.Any(y => y.Value.ToLower() == x.GetEmail().ToLower())));
        var graphUsersAlreadyInDb = dbUsers.Where(x => graphUsers.Any(y => y.GetEmail().ToLower() == x.Value.ToLower()));
        var users = new List<UserFullModel>(graphUsersNotInDb);
        users.AddRange(graphUsersAlreadyInDb);

        return users.OrderBy(x => x.Name).ToList();
    }

    private async Task<UserModel> CheckForInactiveCompanyUser(string email)
    {
        return await _userService.GetInactiveCompanyUser(email);
    }

    public async Task<CompanyUserModel> GetCompanyUser(int companyId, int userId)
    {
        var companyUser = await _companyUserRepository.GetCompanyUserByIdAsync(companyId, userId);
        return _mapper.Map<CompanyUserModel>(companyUser);
    }

    public async Task<CompanyUserWithCompanyModal> GetCompanyUserByEmail(string email)
    {
        var companyUser = await _companyUserRepository.GetCompanyUserByEmail(email);
        return _mapper.Map<CompanyUserWithCompanyModal>(companyUser);
    }

    public async Task<CompanyUserModel> DeleteUserFromBounceList(CompanyUserModel model)
    {
        await _emailMaintenanceService.DeleteBounce(model.Email);
        await _emailMaintenanceService.UpdateSuppressions();
        return await GetCompanyUser(model.CompanyId, model.Id);
    }

    public async Task<CompanyUserModel> DeleteUserFromBlockList(CompanyUserModel model)
    {
        await _emailMaintenanceService.DeleteBlock(model.Email);
        await _emailMaintenanceService.UpdateSuppressions();
        return await GetCompanyUser(model.CompanyId, model.Id);
    }

    public async Task<List<EmailPreference>> GetEmailPreferenceEntities()
    {
        var emailPreferenceEntities = await _emailPreferenceRepository.GetAllAsync();
        return emailPreferenceEntities.ToList();
    }

    public async Task<List<EmailPreferenceModel>> GetEmailPreferencesAsync() => _mapper.Map<List<EmailPreferenceModel>>(await GetEmailPreferenceEntities());
}