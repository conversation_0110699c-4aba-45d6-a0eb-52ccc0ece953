using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class UserSubstanceConfiguration : EntityBaseMap<UserSubstance>
{
    public override void Configure(EntityTypeBuilder<UserSubstance> builder)
    {
        base.Configure(builder);

        builder.ToTable("UserSubstances");

        builder.<PERSON><PERSON>ey(sc => new { sc.UserId, sc.SubstanceId });

        builder.HasIndex(p => new { p.UserId, p.SubstanceId });

        builder.HasOne(sc => sc.User)
            .WithMany(s => s.UserSubstances)
            .HasForeignKey(sc => sc.UserId);

        builder.HasOne(sc => sc.Substance)
            .WithMany(s => s.UserSubstances)
            .HasForeignKey(sc => sc.SubstanceId);
    }
}