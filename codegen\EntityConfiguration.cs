using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
{{#if Dependencies}}{{#each Dependencies}}using {{this}};
{{/each}}{{/if}}
namespace {{Namespace}}
{
    public class {{EntityName}}Configuration : EntityBaseMap<{{EntityName}}>
    {
        public void Configure(EntityTypeBuilder<{{EntityName}}> builder)
        {
            builder.ToTable("{{EntityNamePlural}}", t => t.IsTemporal());

            builder.HasQueryFilter(e => e.IsDeleted == false);
        }
    }
}
