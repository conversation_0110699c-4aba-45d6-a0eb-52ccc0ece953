﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.BlobStorage.Services;

namespace PharmaLex.Core.HealthCheck;

public class AzureStorageHealthCheck<TClientProvider, TStorageOptions> : IHealthCheck
    where TClientProvider : IBlobContainerClientProvider
    where TStorageOptions : AzureStorageDocumentOptions
{
    private readonly IEnumerable<AzureStorageDocumentOptions> _azureStorageOptions;
    private readonly IBlobContainerClientProvider _containerProvider;
    private readonly ILogger<AzureStorageHealthCheck<TClientProvider, TStorageOptions>> _logger;

    public AzureStorageHealthCheck(IEnumerable<AzureStorageDocumentOptions>? azureStorageOptions,
        IBlobContainerClientProvider containerProvider, ILoggerFactory loggerFactory)
    {
        ArgumentNullException.ThrowIfNull(azureStorageOptions);

        _azureStorageOptions = azureStorageOptions;
        _containerProvider = containerProvider;
        _logger = loggerFactory.CreateLogger<AzureStorageHealthCheck<TClientProvider, TStorageOptions>>();
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        bool isHealthy;

        try
        {
            isHealthy = await IsReachableAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while validating azure storage health.");
            isHealthy = false;
        }

        return await Task.FromResult(isHealthy ? HealthCheckResult.Healthy() : HealthCheckResult.Unhealthy());
    }

    private async Task<bool> IsReachableAsync()
    {
        foreach (var option in _azureStorageOptions)
        {
            var container = _containerProvider.Provide(new DocumentDescriptor(option.AccountName, option.ContainerName, string.Empty));

            if (!await container.ExistsAsync())
            {
                return false;
            }
        }

        return true;
    }
}