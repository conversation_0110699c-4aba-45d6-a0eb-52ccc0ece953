using AutoMapper;
using PharmaLex.VigiLit.ContractManagement.Enums;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.Services;

public class ContractService : IContractService
{
    private readonly IContractRepository _contractRepository;
    private readonly IMapper _mapper;
    private readonly IVigiLitUserContext _userContext;
    private readonly TimeProvider _timeProvider;

    public ContractService(IContractRepository contractRepository, IMapper mapper, IUserRepository userRepository, IVigiLitUserContext userContext, TimeProvider timeProvider)
    {
        _contractRepository = contractRepository;
        _mapper = mapper;
        _userContext = userContext;
        _timeProvider = timeProvider;
    }

    public async Task<IEnumerable<SimpleContractModel>> GetAllAsync()
    {
        var res = await _contractRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<SimpleContractModel>>(res);
    }

    public async Task<ContractDetailsModel> GetContractDetails(int id)
    {
        var res = await _contractRepository.GetContractDetails(id);
        return _mapper.Map<ContractDetailsModel>(res);
    }

    public async Task<ContractDetailsModel> GetContractDetailsForAdminUsers(int id)
    {
        var res = await _contractRepository.GetContractDetailsForAdminUsers(id);
        return _mapper.Map<ContractDetailsModel>(res);
    }

    public async Task<ContractDetailsModel> GetContractWithLatestVersion(int contractId)
    {
        var res = await _contractRepository.GetContractWithLatestVersion(contractId);
        return _mapper.Map<ContractDetailsModel>(res);
    }

    public async Task<IEnumerable<ExportContractModel>> GetAllToExportAsync()
    {
        var res = await _contractRepository.GetAllToExportAsync();
        return _mapper.Map<IEnumerable<ExportContractModel>>(res);
    }

    public async Task<IEnumerable<SimpleContractModel>> GetForAdHocImportCreate(int companyId, int projectId = 0)
    {
        var res = await _contractRepository.GetForAdHocImportCreate(companyId, projectId);
        return _mapper.Map<IEnumerable<SimpleContractModel>>(res);
    }

    public async Task<ContractModel> GetByIdAsync(int id)
    {
        var contract = await _contractRepository.GetByIdAsync(id);

        var contractModel = _mapper.Map<ContractModel>(contract);

        if (contract is not null)
        {
            contractModel.ContractVersionCurrent.Journals = new MultiSelectFilterRequest();

            var contractVersionJournals = contract.ContractVersions.Last().ContractVersionJournals;

            if (contractVersionJournals.Count > 0)
            {
                contractModel.ContractVersionCurrent.ScreeningType = (int)ScreeningType.Local;
                contractModel.ContractVersionCurrent.Country = contractVersionJournals.First().Journal.CountryId;

                var filterList = contractVersionJournals.Select(x => x.JournalId);

                contractModel.ContractVersionCurrent.Journals.SelectedIds = filterList;
            }
            else
            {
                contractModel.ContractVersionCurrent.ScreeningType = (int)ScreeningType.Global;
                contractModel.ContractVersionCurrent.Journals.SelectedIds = new List<int>();
            }
        }

        return contractModel;
    }

    /// <summary>
    /// Sets the contract start date to now if it's not been started yet and its active
    /// </summary>
    private void SetContractStartDate(Contract contract)
    {
        if (!contract.GetCurrentContractVersion().IsActive) return;

        if (!contract.HasContractStarted())
        {
            contract.ContractStartDate = _timeProvider.GetUtcNow().UtcDateTime;
        }
    }

    public async Task AddAsync(ContractModel model)
    {
        var contract = new Contract(model.SubstanceId, model.ProjectId);
        ValidateSearchPeriodDays(model.ContractVersionPending.SearchPeriodDays);

        if (!string.IsNullOrEmpty(model.ContractVersionPending?.SearchString))
        {
            model.ContractVersionPending.Version = 1;
            model.ContractVersionPending.UserId = _userContext.UserId;
            model.ContractVersionPending.TimeStamp = DateTime.UtcNow;
            var contractVersion = _mapper.Map<ContractVersion>(model.ContractVersionPending);
            contractVersion.SetContractVersion(ContractVersionStatus.Approved);
            contract.AddContractVersion(contractVersion);

            contract.ScreeningType = model.ContractVersionPending.ScreeningType;
            if (model.ContractVersionPending.ScreeningType == (int)ScreeningType.Local)
            {
                foreach (var selectedJournalId in model.ContractVersionPending.Journals.SelectedIds)
                {
                    contract.ContractVersionsInternal[0].ContractVersionJournals.Add(new ContractVersionJournal { JournalId = selectedJournalId });
                }
            }
        }

        SetContractStartDate(contract);

        _contractRepository.Add(contract);
        await _contractRepository.SaveChangesAsync();

        // Updating currentContractVersion Id after contract is created
        contract.CurrentContractVersionId = contract.GetCurrentContractVersion().Id;
        await _contractRepository.SaveChangesAsync();
    }

    public async Task UpdateAsync(ContractModel model)
    {
        // Remove weekday when changed from scheduled to ad-hoc
        if (model.ContractVersionCurrent.ContractType != ContractType.Scheduled)
        {
            model.ContractVersionCurrent.ContractWeekday = ContractWeekday.None;
        }
        ValidateSearchPeriodDays(model.ContractVersionPending.SearchPeriodDays);

        var contract = await _contractRepository.GetByIdAsync(model.Id);

        if (contract == default)
        {
            return;
        }

        // update values
        _mapper.Map(model, contract);

        if (model.ContractVersionPending.Id > 0)
        {
            if (model.ContractVersionPending.ContractVersionStatus == (int)ContractVersionStatus.Approved)
            {
                contract.GetContractVersion(model.ContractVersionCurrent.Id)?.SetContractVersion(ContractVersionStatus.History);
            }
            model.ContractVersionPending.Version = model.ContractVersionCurrent == null ? 1 : model.ContractVersionCurrent.Version + 1;
            model.ContractVersionPending.UserId = _userContext.UserId;
            _mapper.Map(model.ContractVersionPending, contract.ContractVersionsInternal.First(x => x.Id == model.ContractVersionPending.Id));
        }
        else
        {
            contract.GetContractVersion(model.ContractVersionCurrent.Id)?.SetContractVersion(ContractVersionStatus.History);
            model.ContractVersionPending.ContractVersionStatus = (int)ContractVersionStatus.Approved;
            model.ContractVersionPending.Version = model.ContractVersionCurrent == null ? 1 : model.ContractVersionCurrent.Version + 1;
            model.ContractVersionPending.UserId = _userContext.UserId;
            model.ContractVersionPending.TimeStamp = DateTime.UtcNow;
            contract.AddContractVersion(_mapper.Map<ContractVersion>(model.ContractVersionPending));
        }

        if (model.ContractVersionPending.ScreeningType == (int)ScreeningType.Local)
        {
            foreach (var selectedJournalId in model.ContractVersionPending.Journals.SelectedIds)
            {
                contract.ContractVersionsInternal[^1].ContractVersionJournals.Add(new ContractVersionJournal { JournalId = selectedJournalId });
            }
        }

        SetContractStartDate(contract);

        await _contractRepository.SaveChangesAsync();

        // Update latest currentContractVersion Id
        contract.CurrentContractVersionId = contract.GetCurrentContractVersion().Id;
        await _contractRepository.SaveChangesAsync();
    }

    private static void ValidateSearchPeriodDays(int searchPeriodDays)
    {
        var searchPeriods = new List<int> { 0, 7, 56, 365 };

        if (!searchPeriods.Contains(searchPeriodDays))
        {
            throw new ArgumentException("Invalid search period days");
        }
    }
}
