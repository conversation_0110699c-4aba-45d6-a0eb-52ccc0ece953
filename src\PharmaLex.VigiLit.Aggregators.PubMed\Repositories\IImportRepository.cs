﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Repositories;

internal interface IImportRepository : ITrackingRepository<Import>
{
    /// <summary>
    /// Gets the specified Import.
    /// </summary>
    /// <param name="id">The identifier of the import.</param>
    /// <returns></returns>
    Task<Import?> Get(int id);

    /// <summary>
    /// Gets the import with the import contracts.
    /// </summary>
    /// <param name="id">The identifier of the import.</param>
    /// <returns></returns>
    Task<Import?> GetWithContracts(int id);
}