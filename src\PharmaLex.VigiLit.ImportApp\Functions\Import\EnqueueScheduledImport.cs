using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Aggregators.PubMed.Scheduled;
using PharmaLex.VigiLit.ImportManagement.Enums;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.ImportApp.Functions.Import;

public class EnqueueScheduledImport
{
    private readonly IImportQueueService _importQueueService;

    public EnqueueScheduledImport(IImportQueueService importQueueService)
    {
        _importQueueService = importQueueService;
    }

    // 22:55 UTC - processor ticks and finds nothing
    // 22:56 UTC - this function enqueues the scheduled import
    // 23:00 UTC - processor ticks and begins processing the import
    [Transaction]
    [Function("Import_EnqueueScheduledImport")]
    public async Task Run([TimerTrigger("0 56 22 * * Sun-Thu")] TimerInfo timer, ILogger log)
    {
        await _importQueueService.EnqueueScheduledImport(ImportTriggerType.Auto);
    }
}