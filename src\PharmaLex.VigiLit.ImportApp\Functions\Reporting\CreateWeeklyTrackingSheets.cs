using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Reports.TrackingSheets.Domain;

namespace PharmaLex.VigiLit.ImportApp.Functions.Reporting;

public class CreateWeeklyTrackingSheets
{
    private readonly IService _service;

    public CreateWeeklyTrackingSheets(IService service)
    {
        _service = service;
    }

    [Transaction]
    [Function("Reporting_CreateWeeklyTrackingSheets")]
    public async Task Run([TimerTrigger("0 00 01 * * Mon")] TimerInfo timer, ILogger log)
    {
        await _service.CreateTrackingSheetsForLastWeek();
    }
}