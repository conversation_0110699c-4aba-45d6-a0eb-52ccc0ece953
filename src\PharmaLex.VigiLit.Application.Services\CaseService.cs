﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.Models.Document.Case;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;
using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.Application.Services;

public class CaseService : ICaseService
{
    private readonly ICaseRepository _caseRepository;
    private readonly ICaseCompanyRepository _caseCompanyRepository;
    private readonly ICaseFileRepository _caseFileRepository;
    private readonly ICaseFileDocumentTypeService _caseFileDocumentTypeService;
    private readonly ICaseDocumentUploadService _caseDocumentUploadService;
    private readonly ICaseDocumentService _caseDocumentService;
    private readonly IClassificationService _classificationService;
    private readonly IReferenceClassificationRepository _referenceClassificationRepository;
    private readonly IMapper _mapper;

    public CaseService(
        ICaseRepository caseRepository,
        ICaseCompanyRepository caseCompanyRepository,
        ICaseFileRepository caseFileRepository,
        ICaseFileDocumentTypeService caseFileDocumentTypeService,
        ICaseDocumentUploadService caseDocumentUploadService,
        ICaseDocumentService caseDocumentService,
        IClassificationService classificationService,
        IReferenceClassificationRepository referenceClassificationRepository,
        IMapper mapper)
    {
        _caseRepository = caseRepository;
        _caseCompanyRepository = caseCompanyRepository;
        _caseFileRepository = caseFileRepository;
        _caseFileDocumentTypeService = caseFileDocumentTypeService;
        _caseDocumentUploadService = caseDocumentUploadService;
        _caseDocumentService = caseDocumentService;
        _classificationService = classificationService;
        _referenceClassificationRepository = referenceClassificationRepository;
        _mapper = mapper;
    }

    public async Task<IEnumerable<CaseModel>> GetAllAsync()
    {
        var cases = await _caseRepository.GetAllWithDetailsAsync();
        return _mapper.Map<IEnumerable<CaseModel>>(cases);
    }

    public async Task<IEnumerable<CaseModel>> SearchAsync(CaseSearchRequest searchRequest)
    {
        var cases = await _caseRepository.GetAllWithDetailsAsync(searchRequest);
        return _mapper.Map<IEnumerable<CaseModel>>(cases);
    }

    public async Task<CaseModel> GetByIdAsync(int id)
    {
        var caseRecord = await _caseRepository.GetByIdWithDetailsAsync(id);
        return _mapper.Map<CaseModel>(caseRecord);
    }

    public CaseValidationResult Validate(SaveCaseRequest saveCaseRequest)
    {
        if (saveCaseRequest.PlxId is null or <= 0)
            return CaseValidationResult.Fail("Please enter a valid PLX ID.");

        if (saveCaseRequest.Companies == null || saveCaseRequest.Companies.Count == 0 || saveCaseRequest.Companies.Any(company => company.Id <= 0))
            return CaseValidationResult.Fail("Please select at least one company.");

        if (saveCaseRequest.PSUR == PSURRelevanceAbstract.NA)
            return CaseValidationResult.Fail("Please select the PSUR.");

        if (!string.IsNullOrEmpty(saveCaseRequest.PvSafetyDatabaseId))
        {
            if (!saveCaseRequest.PvSafetyDatabaseId.All(char.IsLetterOrDigit))
            {
                return CaseValidationResult.Fail("PV Safety Database ID must be alphanumeric.");
            }
            if (saveCaseRequest.PvSafetyDatabaseId.Length > 100)
            {
                return CaseValidationResult.Fail("PV Safety Database ID must be 100 characters or less");
            }
        }

        var isNewCase = !saveCaseRequest.CaseId.HasValue;
        if (isNewCase)
        {
            if (saveCaseRequest.UploadedFileNames == null || saveCaseRequest.UploadedFileNames.Count == 0 || saveCaseRequest.UploadedFileNames.Any(string.IsNullOrWhiteSpace))
                return CaseValidationResult.Fail("Please upload one or more case files.");

            if (saveCaseRequest.UploadedFileNames.Any(fileName => !ValidateDirectoryOrFileName(fileName)))
                return CaseValidationResult.Fail("Case files contain an invalid file name.");

            if (saveCaseRequest.UploadedFileNames.Any(fileName => !ValidateFileName(fileName, saveCaseRequest.PlxId.Value)))
                return CaseValidationResult.Fail("Case files do not contain PLX ID.");
        }

        if (!ValidateDirectoryOrFileName(saveCaseRequest.UploadId))
            return CaseValidationResult.Fail("Upload ID is invalid.");

        return CaseValidationResult.Success();
    }

    public async Task<CaseValidationResult> Validate(string fileName, Stream stream, int plxId, string uploadId)
    {
        if (plxId <= 0)
            return CaseValidationResult.Fail("Please enter a valid PLX ID before uploading case files.");

        if (string.IsNullOrWhiteSpace(fileName) || stream == null || stream.Length == 0)
            return CaseValidationResult.Fail("Please select a non-empty case file to upload.");

        if (!ValidateDirectoryOrFileName(fileName))
            return CaseValidationResult.Fail("File name is invalid.");

        var fileExtension = Path.GetExtension(fileName).TrimStart('.');
        var fileSizeInMb = stream.Length / 1024.0 / 1024.0;

        var validFileNameRegex = new Regex(@"^[a-zA-Z0-9\-_]+$", RegexOptions.None, TimeSpan.FromSeconds(10));
        if (!validFileNameRegex.IsMatch(Path.GetFileNameWithoutExtension(fileName)))
        {
            return CaseValidationResult.Fail($"Case file \"{fileName}\" must contain only letters, numbers, '-' and '_'");
        }

        if (!ValidateFileName(fileName, plxId))
            return CaseValidationResult.Fail($"Case file \"{fileName}\" does not contain PLX ID - e.g. \"{plxId}-Case-File.pdf\" or \"Case_File_{plxId}.xml\".");

        var documentTypes = (await _caseFileDocumentTypeService.GetAllAsync()).ToList();
        var documentType = documentTypes.FirstOrDefault(dt => string.Equals(dt.Extension, fileExtension, StringComparison.OrdinalIgnoreCase));

        if (documentType == null)
            return CaseValidationResult.Fail($"Case file type \"{fileExtension}\" is not allowed. Allowed types: '{string.Join("', '", documentTypes.Select(type => type.Extension).OrderBy(extension => extension))}'.");

        if (fileSizeInMb > documentType.MaxFileSizeMb)
            return CaseValidationResult.Fail($"Case file cannot be larger than {documentType.MaxFileSizeMb} MB.");

        if (!ValidateDirectoryOrFileName(uploadId))
            return CaseValidationResult.Fail("Upload ID is invalid.");

        return CaseValidationResult.Success();
    }

    public async Task<int> AddAsync(SaveCaseRequest saveCaseRequest)
    {
        if (!saveCaseRequest.PlxId.HasValue)
            throw new ArgumentOutOfRangeException(nameof(saveCaseRequest));

        await UpdateClassificationAttributesAsync(saveCaseRequest, false);

        var caseRecord = new Cases(saveCaseRequest.PlxId.Value, saveCaseRequest.MLM, saveCaseRequest.Comment);
        _caseRepository.Add(caseRecord);
        await _caseRepository.SaveChangesAsync();

        var companyIds = saveCaseRequest.Companies.Select(company => company.Id);
        await AddCaseCompanies(caseRecord, companyIds);

        await AddCaseFiles(caseRecord, saveCaseRequest.UploadId, saveCaseRequest.UploadedFileNames);

        return caseRecord.Id;
    }

    public async Task UpdateAsync(SaveCaseRequest request)
    {
        if (!request.CaseId.HasValue || !request.PlxId.HasValue)
        {
            throw new ArgumentException("Case file updates must contain the Case ID.");
        }

        var caseRecord = await _caseRepository.GetByIdWithDetailsAsync(request.CaseId.Value);
        if (caseRecord.Status != CaseStatus.Pending)
        {
            throw new ArgumentException("Can only update pending cases.");
        }

        if (!ValidateCaseUpdateHasFiles(caseRecord, request))
        {
            throw new ArgumentException("Cases must contain at least 1 file.");
        }

        caseRecord.MLMDuplicate = request.MLM;
        caseRecord.Comment = request.Comment;
        await UpdateClassificationAttributesAsync(request, true);
        await UpdateCaseCompanies(caseRecord, request.Companies.Select(c => c.Id));
        await DeleteCaseFiles(caseRecord, request.DeletedFileIds);
        await AddCaseFiles(caseRecord, request.UploadId, request.UploadedFileNames);

        await _caseRepository.SaveChangesAsync();
    }

    public async Task DeleteAsync(int id)
    {
        var caseRecord = await _caseRepository.GetByIdWithDetailsAsync(id);

        if (caseRecord == null)
        {
            throw new KeyNotFoundException($"Could not find a case with ID: {id}");
        }
        if (caseRecord.Status != CaseStatus.Pending)
        {
            throw new InvalidOperationException("Can only delete pending cases.");
        }

        await DeleteCaseFiles(caseRecord, caseRecord.CaseFiles.Select(c => c.Id).ToList());
        await DeleteCaseCompanies(caseRecord);

        _caseRepository.Remove(caseRecord);
        await _caseRepository.SaveChangesAsync();
    }

    public async Task PublishPendingCasesAsync()
    {
        var caseRecords = await _caseRepository.GetAllAsync(CaseStatus.Pending);

        foreach (var caseRecord in caseRecords)
            caseRecord.Status = CaseStatus.Published;

        await _caseRepository.SaveChangesAsync();
    }

    private async Task UpdateClassificationAttributesAsync(SaveCaseRequest request, bool isCaseEdit)
    {
        if (await HaveClassificationAttributesChangedAsync(request))
        {
            await UpdatePSUR(request.PlxId.Value, request.PSUR);
            await UpdatePvSafetyDatabaseId(request.PlxId.Value, request.PvSafetyDatabaseId);
            _classificationService.AddReferenceHistoryAction(request.PlxId.Value, isCaseEdit ? ReferenceHistoryActionType.CaseEdit : ReferenceHistoryActionType.CaseUpload);
            await _classificationService.SaveReferenceHistoryChangesAsync();
        }
    }

    private async Task<bool> HaveClassificationAttributesChangedAsync(SaveCaseRequest request)
    {
        var classification = await _classificationService.GetByIdAsync(request.PlxId.Value);
        if (classification == null)
        {
            return false;
        }

        return !classification.PSURRelevanceAbstract.Equals(request.PSUR) || !object.Equals(request.PvSafetyDatabaseId, classification.PvSafetyDatabaseId);
    }


    private static bool ValidateDirectoryOrFileName(string directoryOrFileName)
    {
        if (string.IsNullOrWhiteSpace(directoryOrFileName))
            return false;

        var sanitizedName = Path.GetFileName(directoryOrFileName);
        if (sanitizedName != directoryOrFileName)
            return false;

        return true;
    }

    private static bool ValidateFileName(string fileName, int plxId)
    {
        var fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
        var fileNameUnified = fileNameWithoutExtension.Replace('-', '_');

        return fileNameUnified.Equals(plxId.ToString())
               || fileNameUnified.StartsWith($"{plxId}_")
               || fileNameUnified.EndsWith($"_{plxId}")
               || fileNameUnified.Contains($"_{plxId}_");
    }

    private async Task UpdatePSUR(int plxId, PSURRelevanceAbstract psurAbstract)
    {
        var psur = psurAbstract.ToString();
        if (plxId <= 0 || string.IsNullOrWhiteSpace(psur))
            return;

        var referenceClassification = await _referenceClassificationRepository.GetByIdAsync(plxId);
        if (referenceClassification == null)
            return;

        referenceClassification.UpdatePSUR(psur);
        await _referenceClassificationRepository.SaveChangesAsync();
    }

    private async Task UpdatePvSafetyDatabaseId(int plxId, string pvSafetyDatabaseId)
    {
        if (plxId <= 0)
            return;

        var referenceClassification = await _referenceClassificationRepository.GetByIdAsync(plxId);
        if (referenceClassification == null)
            return;

        referenceClassification.PvSafetyDatabaseId = pvSafetyDatabaseId;
        await _referenceClassificationRepository.SaveChangesAsync();
    }

    private async Task AddCaseCompanies(Cases caseRecord, IEnumerable<int> companyIds)
    {
        foreach (var companyId in companyIds)
        {
            _caseCompanyRepository.Add(new CaseCompanies(caseRecord, companyId));
        }
        await _caseCompanyRepository.SaveChangesAsync();
    }

    private async Task UpdateCaseCompanies(Cases caseRecord, IEnumerable<int> newCompanyIds)
    {
        var currentCompanyIds = caseRecord.CaseCompanies.Select(c => c.CompanyId).ToList();

        foreach (var companyId in currentCompanyIds)
        {
            if (!newCompanyIds.Contains(companyId))
            {
                _caseCompanyRepository.Remove(caseRecord.CaseCompanies.FirstOrDefault(c => c.CompanyId == companyId));
            }
        }

        foreach (var newCompanyId in newCompanyIds)
        {
            if (!currentCompanyIds.Contains(newCompanyId))
            {
                _caseCompanyRepository.Add(new CaseCompanies(caseRecord, newCompanyId));
            }
        }
        await _caseCompanyRepository.SaveChangesAsync();
    }

    private async Task DeleteCaseCompanies(Cases caseRecord)
    {
        foreach (var company in caseRecord.CaseCompanies)
        {
            _caseCompanyRepository.Remove(company);
        }
        await _caseCompanyRepository.SaveChangesAsync();
    }

    private bool ValidateCaseUpdateHasFiles(Cases caseModel, SaveCaseRequest request)
    {
        var caseFileCount = caseModel.CaseFiles.ToList().Count;
        var deletedFileCount = request.DeletedFileIds?.Count ?? 0;
        var newFileCount = request.UploadedFileNames?.Count ?? 0;

        var proposedFileCount = caseFileCount - deletedFileCount + newFileCount;

        return proposedFileCount > 0;
    }

    private async Task AddCaseFiles(Cases caseRecord, string uploadId, IReadOnlyCollection<string> uploadedFileNames)
    {
        if (uploadedFileNames == null || uploadedFileNames.Count == 0)
            return;

        var saveCaseFileTasks = uploadedFileNames
            .Select(uploadedFileName => AddCaseFile(caseRecord, uploadId, uploadedFileName))
            .ToList();
        await Task.WhenAll(saveCaseFileTasks);

        await _caseFileRepository.SaveChangesAsync();
    }

    private async Task AddCaseFile(Cases caseRecord, string uploadId, string fileName)
    {
        var caseDocumentUploadDescriptor = new CaseDocumentUploadDescriptor(uploadId, fileName);
        var caseDocumentDescriptor = new CaseDocumentDescriptor(caseRecord.Id, fileName);

        var documentProperties = await _caseDocumentUploadService.Save(caseDocumentUploadDescriptor, caseDocumentDescriptor);

        await _caseDocumentUploadService.Delete(caseDocumentUploadDescriptor);

        _caseFileRepository.Add(new CaseFiles(caseRecord, fileName, (int)documentProperties.FileSize));
    }

    private async Task DeleteCaseFiles(Cases caseRecord, IReadOnlyCollection<int> deletedFileIds)
    {
        if (deletedFileIds == null || deletedFileIds.Count == 0)
            return;

        var deleteCaseFileTasks = deletedFileIds
            .Select(deletedFileId => DeleteCaseFile(caseRecord, deletedFileId))
            .ToList();
        await Task.WhenAll(deleteCaseFileTasks);

        await _caseFileRepository.SaveChangesAsync();
    }

    private async Task DeleteCaseFile(Cases caseRecord, int caseFileId)
    {
        var caseFile = caseRecord.CaseFiles.FirstOrDefault(caseFile => caseFile.Id == caseFileId);
        if (caseFile == null)
            return;

        var caseDocumentDescriptor = new CaseDocumentDescriptor(caseRecord.Id, caseFile.FileName);
        var exists = await _caseDocumentService.Exists(caseDocumentDescriptor);
        if (exists)
            await _caseDocumentService.Delete(caseDocumentDescriptor);

        _caseFileRepository.Remove(caseFile);
    }

    private static CaseFileModel GetCaseFileModel(int caseFileId, CaseModel caseModel)
    {
        var caseFile = caseModel.CaseFiles.Find(caseFile => caseFile.Id == caseFileId);
        if (caseFile == null)
        {
            throw new NotFoundException("Case File does not exist.");
        }

        return caseFile;
    }

    private async Task<CaseDocumentDescriptor> GetCaseDocumentDescriptor(int caseId, CaseFileModel caseFile)
    {
        var caseDocumentDescriptor = new CaseDocumentDescriptor(caseId, caseFile.FileName);
        var exists = await _caseDocumentService.Exists(caseDocumentDescriptor);
        if (!exists)
        {
            throw new NotFoundException("Case File does not exist.");
        }

        return caseDocumentDescriptor;
    }

    public async Task<(Stream, string)> GetDocumentStream(CaseModel caseModel, int caseFileId)
    {
        var caseFile = GetCaseFileModel(caseFileId, caseModel);
        var caseDocumentDescriptor = await GetCaseDocumentDescriptor(caseModel.Id, caseFile);

        var stream = await _caseDocumentService.OpenRead(caseDocumentDescriptor);
        return (stream, caseFile.FileName);
    }
}