﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Data.Repositories;

public class ClaimRepository : TrackingGenericRepository<Claim>, IClaimRepository
{
    protected readonly IMapper _mapper;

    public ClaimRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public IEnumerable<ClaimModel> GetAll()
    {
        var query = context.Set<Claim>()
            .AsNoTracking();

        return _mapper.ProjectTo<ClaimModel>(query).ToList();
    }

    public async Task<IEnumerable<Claim>> GetAllAsync()
    {
        return await context.Set<Claim>().ToListAsync();
    }

    public Task<Claim?> GetByIdAsync(int id)
    {
        return context.Set<Claim>().FirstOrDefaultAsync(c => c.Id == id);
    }

    public Task<Claim?> GetByNameAsync(string name)
    {
        return context.Set<Claim>().FirstOrDefaultAsync(c => c.Name == name);
    }
}
