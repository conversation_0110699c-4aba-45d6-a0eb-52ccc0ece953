using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface IReferenceService
{
    Task<IEnumerable<ReferenceModel>> GetAllAsync();
    Task<ReferenceDetailedModel> GetReferenceByIdAsync(int id);
    Task<ReferenceDetailsPageModel> GetReferenceDetails(int referenceId, User user);
    Task<ReferenceSplitPageModel> GetSplitReferenceDetails(int referenceId);
    Task<bool> IsDoiExisting(string doiId);
    Task<bool> IsDoiValid(string doi);
}
