﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class RenameAdHocImportsField : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PubMedModificationDateTo",
                table: "AdHocImports",
                newName: "SourceSearchStartDate");

            migrationBuilder.RenameColumn(
                name: "PubMedModificationDateFrom",
                table: "AdHocImports",
                newName: "SourceSearchEndDate");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "SourceSearchStartDate",
                table: "AdHocImports",
                newName: "PubMedModificationDateTo");

            migrationBuilder.RenameColumn(
                name: "SourceSearchEndDate",
                table: "AdHocImports",
                newName: "PubMedModificationDateFrom");
        }
    }
}
