using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.ApplicationInsights.Extensibility.Implementation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using PharmaLex.Authentication.B2C;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.BlobStorage.Services;
using PharmaLex.Caching;
using PharmaLex.Caching.Data;
using PharmaLex.Core.HealthCheck;
using PharmaLex.Core.HealthCheck.Extensions;
using PharmaLex.Core.UserManagement;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.DataAccess;
using PharmaLex.FeatureManagement.Extensions;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.AiAnalysis.Entities;
using PharmaLex.VigiLit.Application.Services;
using PharmaLex.VigiLit.Application.UserManagement.Users;
using PharmaLex.VigiLit.Case;
using PharmaLex.VigiLit.ContractManagement;
using PharmaLex.VigiLit.ContractManagement.Ui;
using PharmaLex.VigiLit.Core.Ui;
using PharmaLex.VigiLit.Data.Repositories;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.DataExtraction.Service;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ImportManagement.Service;
using PharmaLex.VigiLit.ImportManagement.Ui;
using PharmaLex.VigiLit.Infrastructure;
using PharmaLex.VigiLit.Infrastructure.Import.PubMed;
using PharmaLex.VigiLit.ReferenceManagement.Service;
using PharmaLex.VigiLit.Reporting;
using PharmaLex.VigiLit.Reporting.Contracts.AutoMapper;
using PharmaLex.VigiLit.Scraping.Service;
using PharmaLex.VigiLit.Search.Ui;
using PharmaLex.VigiLit.Web.Authentication;
using PharmaLex.VigiLit.Web.Extensions;
using PharmaLex.VigiLit.Web.Helpers;
using Phlex.Core.MessageBus.Extensions;
using System.Collections.Generic;
using AppSettingsHelper = PharmaLex.Core.Configuration.AppSettingsHelper;


namespace PharmaLex.VigiLit.Web;

public class Startup
{
    public Startup(IConfiguration configuration, IWebHostEnvironment environment)
    {
        Configuration = configuration;
        Environment = environment;
    }

    public IConfiguration Configuration { get; }
    public IWebHostEnvironment Environment { get; }

    // This method gets called by the runtime. Use this method to add services to the container.
    public void ConfigureServices(IServiceCollection services)
    {
        services.RegisterRepositories();
        services.RegisterReporting(Configuration);
        services.RegisterAiAnalysisClient();
        services.RegisterFeatureFlagService();
        services.RegisterAiAnalysisEntities();
        services.RegisterReferenceManagement(Configuration);
        //PubmedImportServices registration to be handled in different ticket.
        services.RegisterPubmedImportServices();
        services.RegisterImportManagement(Configuration);
        services.RegisterImportManagementUi(Configuration);
        services.RegisterContractManagement(Configuration);
        services.RegisterContractManagementUi(Configuration);
        services.RegisterCoreUi(Configuration);
        services.RegisterApplicationServices();
        services.RegisterDataExtractionServices(Configuration);
        services.RegisterImportManagementClient();
        services.RegisterDataExtractionClient();
        services.RegisterSearchUi(Configuration);

        // These two are related to importing so will need moving
        services.RegisterPubMedAggregator(); // This is the one in "PharmaLex.VigiLit.Aggregators.PubMed", needs combining
        services.AddScoped<IContractService, ContractService>();
        // These two are related to importing so will need moving

        services.AddScoped<IUserService, UserService>();
        services.AddScoped<ICompanyService, CompanyService>();
        services.AddScoped<ISubstanceService, SubstanceService>();
        services.AddScoped<IProjectService, ProjectService>();
        services.AddScoped<ICompanyUserService, CompanyUserService>();
        services.AddScoped<IReferenceService, ReferenceService>();
        services.AddScoped<IContractService, ContractService>();
        services.AddScoped<IClassificationCategoryService, ClassificationCategoryService>();
        services.AddScoped<IClassificationService, ClassificationService>();
        services.AddScoped<ICountryService, CountryService>();
        services.AddScoped<ICaseService, CaseService>();
        services.AddScoped<ICaseFileDocumentTypeService, CaseFileDocumentTypeService>();

        services.AddScoped<IPreClassificationService, PreClassificationService>();
        services.AddScoped<ISplitReferenceService, SplitReferenceService>();
        services.AddScoped<IUserSessionService, UserSessionService>();

        services.AddScoped<IReportingCompanyRepository, CompanyRepository>();

        services.RegisterScrapingServices(Configuration);
        services.RegisterScrapingClients(Configuration);

        services.AddAutoMapper(typeof(UserMappingProfile).Assembly);
        services.AddAutoMapper(typeof(ReportMappingProfile).Assembly);



        services.AddScoped<IUserContext, PlxUserContext>();
        services.AddScoped<IVigiLitUserContext, VigilitUserContext>();
        services.AddScoped<IWebsiteUriProvider, WebsiteUriProvider>();
        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddScoped<IDistributedCacheService, DistributedCacheService>();
        services.AddScoped<IDistributedCacheServiceFactory, DistributedCacheServiceFactory>();

        services.AddEmailServices(Configuration);
        services.AddDocumentServices(Configuration);
        services.AddImportFileUploadDocumentServices(Configuration);

        services.AddSingleton<ITicketReceivedCallback, AddClaimsCallback>();

        services.AddSingleton<CdnHelper>();

        services.AddAuthorization(options =>
        {
            options.FallbackPolicy = new AuthorizationPolicyBuilder()
              .RequireAuthenticatedUser()
              .Build();
        }).AddPolicies();

        services.AddAccessControl();
        services.AddCasesAccessControl();

        services.AddApplicationInsightsTelemetry();

        services
            .AddOptions()
            .Configure<AzureAdGraphOptions>(Configuration.GetSection("AzureAdGraph"))
            .Configure<AzureAdB2CGraphOptions>(Configuration.GetSection("AzureAdB2CGraph"))
            .Configure<CookiePolicyOptions>(options =>
            {
                options.CheckConsentNeeded = context => true;
                options.MinimumSameSitePolicy = SameSiteMode.Unspecified;
                options.HandleSameSiteCookieCompatibility();
            })
            .ConfigureMicrosoftItentityWebAuthentication(Configuration)
            .ConfigureCustomAuthentication(Configuration)
            .AddMessageBus(Configuration, configurator =>
            {
                configurator.UsingTransportFromConfiguration(Configuration);
            });

        services
          .AddSession(o => o.Cookie.IsEssential = true)
          .AddControllersWithViews(options =>
          {
              options.Filters.Add(new AutoValidateAntiforgeryTokenAttribute());
          })
          .AddMicrosoftIdentityUI()
          .AddAzureAdB2CAuthenticationUI()
          .AddRazorRuntimeCompilation();


        services.AddTransient<IGraphClientProvider<AzureAdB2CGraphOptions>, AzureAdB2CGraphClientProvider>();
        services.AddScoped<IAzureAdB2CGraphService, AzureAdB2CGraphService>();
        services.AddUserServices<User, UserRepository>(Configuration);
        services.AddSingleton<AppSettingsHelper>();

        services.AddPharmaLexFeatureManagement();

        services.AddHealthChecks()
            .AddCheck<DatabaseHealthCheck<VigiLitDbContext>>("Database")
            .AddHealthCheck<IBlobContainerClientProvider, IEnumerable<AzureStorageDocumentOptions>>("AzureStorage")
            .AddMailHealthCheck("MailProvider", Configuration.GetValue<string>("SendGridApiKey"))
            .AddFeatureManagerHealthCheck("DisplayAiSuggestions");

        JsonConvert.DefaultSettings = () => new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver()
        };

        // Max form fields. Default 1024. Edit assessor page has 2500 fields (one per substance).
        services.Configure<FormOptions>(options => options.ValueCountLimit = 5000);
    }

    // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
    public void Configure(IApplicationBuilder app, IWebHostEnvironment env, TelemetryConfiguration tc)
    {
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
            app.UseStatusCodePages();

            tc.DisableTelemetry = true;
            TelemetryDebugWriter.IsTracingDisabled = true;
        }
        else
        {
            app.UseExceptionHandler("/Home/Error");
            // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
            app.UseHsts();
        }

        app.Use(async (context, next) =>
        {
            if (!context.Response.Headers.ContainsKey("X-Frame-Options"))
            {
                context.Response.Headers.Append("X-Frame-Options", "DENY");
            }

            if (!context.Response.Headers.ContainsKey("Content-Security-Policy"))
            {
                var cdn = Configuration.GetValue<string>("Static:Cdn");

                context.Response.Headers.Append("Content-Security-Policy", "default-src 'self'; "
                    + "connect-src 'self' " + (env.IsDevelopment() ? "wss: ws: http:" : "") + "; "
                    + $"script-src 'self' 'unsafe-inline' 'unsafe-eval' {cdn}; "
                    + $"style-src 'self' 'unsafe-inline' {cdn}; "
                    + $"img-src 'self' data: {cdn}; "
                    + $"font-src 'self' {cdn}; "
                    + $"manifest-src 'self' {cdn}; "
                    + "frame-ancestors 'none'; "
                    + "object-src 'none';");
            }

            await next();
        });

        app.UseHttpsRedirection();
        app.UseStaticFiles();

        app.UseRouting();

        app.UseAuthentication();
        app.UseAuthorization();

        app.UseSession();

        var appName = Configuration.GetValue<string>("Static:App");
        var buildVersion = Configuration.GetValue<string>("AppSettings:BuildNumber");
        var cdnVersion = Configuration.GetValue<string>("Static:env");

        var tags = new Dictionary<string, string> {
            { "cdn", cdnVersion}
        };

        app.UseEndpoints(endpoints =>
        {
            endpoints.MapDefaultControllerRoute();
            endpoints.MapRazorPages();
            endpoints.MapHealthChecks("/Health", new HealthCheckOptions
            {
                ResponseWriter = new HealthCheckResponseFormats(appName, buildVersion, tags).WriteResponse
            }).AllowAnonymous();

            // Allow anonymous access to Apify webhook endpoint
            endpoints.MapControllerRoute(
                name: "apify-webhook",
                pattern: "api/apify/webhook",
                defaults: new { }
            ).AllowAnonymous();
        });
    }
}
