using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class AdHocImportContractConfiguration : EntityBaseMap<AdHocImportContract>
{
    public override void Configure(EntityTypeBuilder<AdHocImportContract> builder)
    {
        base.Configure(builder);

        builder.ToTable("AdHocImportContracts");

        builder.HasOne(ic => ic.AdHocImport).WithMany(i => i.AdHocImportContracts).HasForeignKey(ic => ic.AdHocImportId);
        builder.HasOne(ic => ic.Contract).WithMany(c => c.AdHocImportContracts).HasForeignKey(ic => ic.ContractId);
    }
}