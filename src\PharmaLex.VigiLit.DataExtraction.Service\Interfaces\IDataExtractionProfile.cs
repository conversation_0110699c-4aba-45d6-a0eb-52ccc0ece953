using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.ImportManagement.Client.Dto;

namespace PharmaLex.VigiLit.DataExtraction.Service.Interfaces;

public interface IDataExtractionProfile
{
    /// <summary>
    /// Method for mapping ExtractedReference to ReferenceDto.
    /// </summary>
    /// <param name="source"></param>
    /// <returns></returns>
    ReferenceDto Copy(ExtractedReference source);
    /// <summary>
    /// Method for mapping ExtractedReference to FailedImportReferenceDto.
    /// </summary>
    /// <param name="source"></param>
    /// <param name="minMandatoryConfidenceLevel"></param>
    /// <returns></returns>
    FailedImportReferenceDto CopyFailed(ExtractedReference source, float minMandatoryConfidenceLevel);
}