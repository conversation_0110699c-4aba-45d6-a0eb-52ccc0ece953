﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;
public class ImportReferenceConfiguration : EntityBaseMap<ImportReference>
{
    public override void Configure(EntityTypeBuilder<ImportReference> builder)
    {
        base.Configure(builder);

        builder.ToTable("ImportReferences");

        builder.Property(e => e.Doi)
            .HasMaxLength(250);
        builder.Property(e => e.SourceId)
            .HasMaxLength(450);
        builder.Property(e => e.DocumentLocation)
            .HasMaxLength(2000);
    }
}
