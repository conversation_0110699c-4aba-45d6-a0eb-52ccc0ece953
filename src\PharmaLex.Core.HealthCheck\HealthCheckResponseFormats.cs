﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Text.Json;

namespace PharmaLex.Core.HealthCheck;

public class HealthCheckResponseFormats
{
    private readonly IDictionary<string, string> _tags = new Dictionary<string, string>();


    public HealthCheckResponseFormats(string appName, string buildVersion)
    {
        _tags.Add("app", appName);
        _tags.Add("version", buildVersion);
    }

    public HealthCheckResponseFormats(string appName, string buildVersion, IDictionary<string, string> tags) : this(appName, buildVersion)
    {
        foreach (var tag in tags)
        {
            _tags.Add(tag);
        }
    }

    public async Task WriteResponse(HttpContext context, HealthReport healthReport)
    {
        context.Response.ContentType = "application/json; charset=utf-8";

        var options = new JsonWriterOptions { Indented = true };

        await using var memoryStream = new MemoryStream();
        await using (var jWriter = new Utf8JsonWriter(memoryStream, options))
        {
            jWriter.WriteStartObject();
            jWriter.WriteString("status", healthReport.Status.ToString());
            WriteTags(jWriter);
            WriteHealthEntries(healthReport, jWriter);
            jWriter.WriteEndObject();
        }

        memoryStream.Position = 0;
        await memoryStream.CopyToAsync(context.Response.Body);
        await context.Response.Body.FlushAsync();
    }

    private static void WriteHealthEntries(HealthReport healthReport, Utf8JsonWriter jWriter)
    {
        foreach (var keyValue in healthReport.Entries)
        {
            jWriter.WriteString(keyValue.Key,
                CheckConfigValue(keyValue) ? keyValue.Value.Description : keyValue.Value.Status.ToString());
        }
    }

    private static bool CheckConfigValue(KeyValuePair<string, HealthReportEntry> keyValue)
    {
        return !string.IsNullOrEmpty(keyValue.Value.Description);
    }


    private void WriteTags(Utf8JsonWriter jWriter)
    {
        foreach (var keyValue in _tags)
        {
            jWriter.WriteString(keyValue.Key, keyValue.Value);
        }
    }
}