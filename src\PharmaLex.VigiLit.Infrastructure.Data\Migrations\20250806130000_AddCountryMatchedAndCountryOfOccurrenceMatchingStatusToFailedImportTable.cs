﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCountryMatchedAndCountryOfOccurrenceMatchingStatusToFailedImportTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryOfOccurrenceMatchingCheckPassed",
                table: "FailedImportFiles");

            migrationBuilder.AddColumn<string>(
                name: "CountryMatched",
                table: "FailedImportFiles",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "CountryOfOccurrenceMatchingStatus",
                table: "FailedImportFiles",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CountryMatched",
                table: "FailedImportFiles");

            migrationBuilder.DropColumn(
                name: "CountryOfOccurrenceMatchingStatus",
                table: "FailedImportFiles");

            migrationBuilder.AddColumn<bool>(
                name: "CountryOfOccurrenceMatchingCheckPassed",
                table: "FailedImportFiles",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
