using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ReferenceClassificationConfiguration : EntityBaseMap<ReferenceClassification>
{
    public override void Configure(EntityTypeBuilder<ReferenceClassification> builder)
    {
        base.Configure(builder);

        builder.ToTable("ReferenceClassifications", t =>
        {
            t.IsTemporal();
        });

        builder.HasIndex(p => new { p.ReferenceId, p.SubstanceId }).IsUnique();
        builder.HasIndex(p => new { p.ReferenceState, p.PreAssessorId, p.Id, p.ClassificationCategoryId, p.SubstanceId, p.ReferenceId, p.ClassifierId });
        builder.HasIndex(p => new { p.Id, p.ReferenceState, p.PreAssessorId });
        builder.HasIndex(p => new { p.ReferenceId, p.SubstanceId, p.Id, p.ClassifierId });
        builder.HasIndex(p => new { p.Id, p.ReferenceId, p.SubstanceId, p.ReferenceState });
        builder.HasIndex(p => new { p.ReferenceState, p.Id, p.ClassificationCategoryId, p.SubstanceId, p.ReferenceId, p.ClassifierId });

        // Search
        builder.HasIndex(p => new { p.LastUpdatedDate });
        builder.HasIndex(p => new { p.LastUpdatedDate, p.Id, p.ClassificationCategoryId, p.ReferenceId });
        builder.HasIndex(p => new { p.SubstanceId, p.Id, p.ReferenceId, p.LastUpdatedDate });
        builder.HasIndex(p => new { p.LastUpdatedDate, p.PSURRelevanceAbstract });

        // Apogepha Client Report
        builder.HasIndex(p => new { p.CreatedDate })
            .IncludeProperties(p => new { p.ReferenceId, p.SubstanceId, p.ClassificationCategoryId });

        builder.Property(e => e.MinimalCriteria)
            .HasMaxLength(256)
            .IsRequired()
            .HasDefaultValue(string.Empty);

        builder.HasOne(rc => rc.ClassificationCategory)
            .WithMany()
            .HasForeignKey(rc => rc.ClassificationCategoryId)
            .IsRequired(false);

        builder.HasOne(rc => rc.Classifier)
            .WithMany()
            .HasForeignKey(rc => rc.ClassifierId)
            .IsRequired(false);

        builder.HasOne(rc => rc.Substance)
            .WithMany()
            .HasForeignKey(rc => rc.SubstanceId)
            .IsRequired(true)
            .OnDelete(DeleteBehavior.Restrict);

        builder.Property(e => e.DosageForm)
            .HasMaxLength(100)
            .IsRequired(false);

        builder.Property(e => e.CountryOfOccurrence)
            .HasMaxLength(200)
            .IsRequired(false);

        builder.Property(e => e.ReasonForChange)
            .HasMaxLength(256)
            .IsRequired(false);
        builder.Property(e => e.AiCategoryReason).HasMaxLength(4000);
        builder.Property(e => e.AiSuggestedCategory).HasMaxLength(256);
        builder.Property(e => e.AiSuggestedDosageForm).HasMaxLength(100);
        builder.Property(e => e.AiDosageFormReason).HasMaxLength(4000);

        builder.Property(e => e.PotentialCaseAdditionalInformation).HasMaxLength(200);
    }
}
