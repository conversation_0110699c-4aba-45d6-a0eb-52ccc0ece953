﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.VigiLit.DataExtraction.Client;
using PharmaLex.VigiLit.DataExtraction.Entities;
using PharmaLex.VigiLit.DataExtraction.Entities.Enums;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;
using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Logging;
using Source = PharmaLex.VigiLit.DataExtraction.Entities.Enums.Source;

namespace PharmaLex.VigiLit.DataExtraction.Service;

internal class ExtractDataCommandHandler : IExtractDataCommandHandler
{
    private readonly ILogger<ExtractDataCommandHandler> _logger;
    private readonly IPhlexVisionService _phlexVisionService;
    private readonly IMdeQueueItemRepository _mdeQueueItemRepository;
    private readonly IExtractDataFileDownload _extractDataFileDownload;

    public ExtractDataCommandHandler(ILogger<ExtractDataCommandHandler> logger, IMapper mapper, IPhlexVisionService phlexVisionService, IMdeQueueItemRepository mdeQueueItemRepository, IExtractDataFileDownload extractDataFileDownload)
    {
        _logger = logger;
        _phlexVisionService = phlexVisionService;
        _mdeQueueItemRepository = mdeQueueItemRepository;
        _extractDataFileDownload = extractDataFileDownload;
    }

    public async Task Consume(ExtractDataCommand command)
    {
        DocumentProperties? documentProperties = null;

        try
        {
            documentProperties = await MoveImportFile(command.FileName, Guid.Parse(command.BatchId));
            if (documentProperties == null)
            {
                throw new InvalidOperationException($"Failed to move import file: {command.FileName} for batch ID: {command.BatchId}");
            }

            var mdeQueueItem = new MdeQueueItem()
            {
                CorrelationId = command.CorrelationId,
                Filename = command.FileName,
                BatchId = Guid.Parse(command.BatchId),
                Status = MdeQueueItemStatus.Queued,
                DocumentLocation = documentProperties.Uri,
                Source = (Source)command.Source,
            };

            _mdeQueueItemRepository.Add(mdeQueueItem);
            await _mdeQueueItemRepository.SaveChangesAsync();

            _logger.LogInformation("ExtractDataCommandCommandHandler:Consume:{ImportReferenceTitle}", LogSanitizer.Sanitize(command.BatchId));

            var request = new ExtractRequest()
            {
                BatchId = command.BatchId,
                FileName = command.FileName,
                CorrelationId = command.CorrelationId
            };

            await _phlexVisionService.RequestDataExtraction(request);
            _logger.LogInformation("ExtractDataCommandCommandHandler:Consume: Completed.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ExtractDataCommandHandler: Error during processing for Batch ID: {BatchId}, FileName: {FileName}",
                LogSanitizer.Sanitize(command.BatchId),
                LogSanitizer.Sanitize(command.FileName));

            if (documentProperties != null)
            {
                try
                {
                    await RollbackImportFileMove(command.FileName, Guid.Parse(command.BatchId));
                    _logger.LogInformation("Successfully rolled back file move...");
                }
                catch (Exception rollbackEx)
                {
                    _logger.LogError(rollbackEx, "Rollback failed...");
                }
            }
            throw new InvalidOperationException($"Failed to process extract command for Batch ID: {command.BatchId}, FileName: {command.FileName}", ex);
        }

    }

    private async Task<DocumentProperties> MoveImportFile(string fileName, Guid batchId)
    {
        var importFileUploadDescriptor = new ImportFileUploadDescriptor(batchId, fileName);
        var exists = await _extractDataFileDownload.Exists(importFileUploadDescriptor);
        if (!exists)
        {
            throw new NotFoundException("Import File does not exist.");
        }
        var importFileDescriptor = new ImportFileDescriptor(batchId, fileName);
        return await _extractDataFileDownload.Copy(importFileUploadDescriptor, importFileDescriptor);
    }


    private async Task<DocumentProperties> RollbackImportFileMove(string fileName, Guid batchId)
    {
        var importFileDescriptor = new ImportFileDescriptor(batchId, fileName);
        var exists = await _extractDataFileDownload.Exists(importFileDescriptor);
        if (!exists)
        {
            throw new NotFoundException("Import File does not exist.");
        }
        var importFileUploadDescriptor = new ImportFileUploadDescriptor(batchId, fileName);
        var documentProperties = await _extractDataFileDownload.CopyBack(importFileDescriptor, importFileUploadDescriptor);
        await _extractDataFileDownload.Delete(importFileDescriptor);

        return documentProperties;

    }
}
