﻿using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using Phlex.Core.MessageBus;

namespace PharmaLex.VigiLit.AiAnalysis.Client;

public class AiReferencePublisher : IAiReferencePublisher
{
    private readonly IServiceProvider _serviceProvider;

    public AiReferencePublisher(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public async Task Send(PreClassifyReferenceCommand referenceCommand)
    {
        using var scope = _serviceProvider.CreateScope();
        var messageBus = scope.ServiceProvider.GetRequiredService<IMessageBus>();

        var correlationId = Guid.NewGuid();
        await messageBus.SendAsync(referenceCommand, CorrelationContext.From(correlationId));
    }
}
