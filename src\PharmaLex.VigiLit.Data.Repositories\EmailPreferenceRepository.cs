﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Data.Repositories;

public class EmailPreferenceRepository : TrackingGenericRepository<EmailPreference>, IEmailPreferenceRepository
{
    protected readonly IMapper _mapper;

    public EmailPreferenceRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<EmailPreference>> GetAllAsync()
    {
        return await context.Set<EmailPreference>().ToListAsync();
    }
}