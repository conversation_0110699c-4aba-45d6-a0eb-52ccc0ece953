﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.Core.UserSessionManagement.Entities;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class UserSessionConfiguration : EntityBaseMap<UserSession>
{
    public override void Configure(EntityTypeBuilder<UserSession> builder)
    {
        base.Configure(builder);

        builder.HasOne(us => (User)us.User);

        builder.ToTable("UserSessions");

        builder.HasIndex(c => new { c.UserId });

        builder.Property(e => e.IpAddress)
            .IsRequired(false)
            .HasMaxLength(39);

        builder.Property(e => e.CreatedBy)
            .IsRequired(false);

        builder.Property(e => e.LastUpdatedBy)
            .IsRequired(false);
    }
}