﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ImportManagement.Client;
using PharmaLex.VigiLit.ImportManagement.Client.Dto;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.Logging;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;
internal class ManualCorrectionCommandHandler : IManualCorrectionCommandHandler
{
    private readonly IMapper _mapper;
    private readonly ILogger<ManualCorrectionCommandHandler> _logger;
    private readonly IFailedImportFileRepository _failedImportFileRepository;

    public ManualCorrectionCommandHandler(ILogger<ManualCorrectionCommandHandler> logger, IMapper mapper, IFailedImportFileRepository failedImportFileRepository)
    {
        _logger = logger;
        _mapper = mapper;
        _failedImportFileRepository = failedImportFileRepository;
    }

    public async Task Consume(ManualCorrectionCommand command)
    {
        _logger.LogInformation("ManualCorrectionCommandHandler:Consume:{failedImportFileTitle}", LogSanitizer.Sanitize(command.Reference.Title));

        var failedImportFile = MapFailedImportFileStatusNew(command.Reference);

        try
        {
            await SaveFailedImportFile(failedImportFile);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "ManualCorrectionCommandHandler:Consume: Exception occurred while saving FailedImportFile.");
            throw new InvalidOperationException("ManualCorrectionCommandHandler:Consume: Exception occurred while saving FailedImportFile.", ex);

        }

        _logger.LogInformation("ManualCorrectionCommandHandler:Consume: Completed.");
    }



    private async Task SaveFailedImportFile(FailedImportFile failedImportFile)
    {
        _failedImportFileRepository.Add(failedImportFile);
        await _failedImportFileRepository.SaveChangesAsync();
    }

    private FailedImportFile MapFailedImportFileStatusNew(FailedImportReferenceDto reference)
    {

        try
        {
            var failedImportFile = _mapper.Map<FailedImportFile>(reference);
            return failedImportFile;
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to map reference to FailedImportFile.", ex);
        }
    }
}
