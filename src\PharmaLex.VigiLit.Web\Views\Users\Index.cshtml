﻿@model PharmaLex.VigiLit.Ui.ViewModels.UserManagement.ManageUsersViewModel
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.Web.Helpers;

@{
    ViewData["Title"] = "Users";
}

<div id="users">
    <div class="sub-header">
        <h2>Users</h2>
        <div class="controls">
            <a class="button btn-default" href="/users/create">Add user</a>
        </div>
    </div>
    <section>
        <filtered-table :items="filteredUsers" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section Scripts {

    <script type="text/javascript">

        var pageConfig = {
            appElement: "#users",
            data: function () {
                return {
                    link: '/users/edit/',
                    users: @Html.Raw(AntiXss.ToJson(Model.Users)),
                    columns: {
                        idKey: 'guid',
                        config: [
                            {
                                dataKey: 'displayFullName',
                                sortKey: 'displayFullName',
                                header: 'Name',
                                type: 'text',
                                style: 'width: 33%;'
                            },
                            {
                                dataKey: 'email',
                                sortKey: 'email',
                                header: 'Email',
                                type: 'text',
                                style: 'width: 33%;'
                            },
                            {
                                dataKey: 'displayClaimsText',
                                sortKey: 'displayClaimsText',
                                header: 'Roles',
                                type: 'text',
                                style: 'width: 34%;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'displayFullName',
                            options: [],
                            type: 'search',
                            header: 'Search Name',
                            fn: v => p => p.displayFullName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'email',
                            options: [],
                            type: 'search',
                            header: 'Search Email',
                            fn: v => p => p.email.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'displayClaimsText',
                            options: @Html.Raw(AntiXss.ToJson(Model.Claims)),
                            filterCollection: 'claimIds',
                            display: 'displayName',
                            type: 'select',
                            header: 'Filter By Role',
                            fn: v => p => p.claimIds.includes(v),
                            convert: v => parseInt(v)
                        }
                    ]
                };
            },           
            computed: {
                filteredUsers() {
                    return this.users.filter(function (user) {
                        // Filter out if user only has role of Pharmalex Client Researcher
                        if (user.claimIds.length === 1){
                            return !user.claimIds.includes(8);
                        }else{
                          return user;
                        }
                    });
                }
            }
        };
    </script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/FilteredTable" />
}