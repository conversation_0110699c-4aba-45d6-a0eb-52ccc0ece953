using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class EmailMessageConfiguration : EntityBaseMap<EmailMessage>
{
    public override void Configure(EntityTypeBuilder<EmailMessage> builder)
    {
        base.Configure(builder);

        builder.ToTable("EmailMessages");

        builder.Property(e => e.EmailAddress).IsRequired().HasMaxLength(256);
        builder.Property(e => e.Subject).IsRequired().HasMaxLength(250);

        builder.HasOne(em => em.Email).WithMany(e => e.EmailMessages).HasForeignKey(em => em.EmailId);
    }
}