apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "vigilit-ai.fullname" . }}
  labels:
    azure.workload.identity/use: 'true'
  annotations:
    azure.workload.identity/client-id: {{ .Values.azureWorkload.clientId }}
    azure.workload.identity/tenant-id: {{ .Values.azureWorkload.tenantId }}
    azure.workload.identity/service-account-token-expiration: {{ quote .Values.azureWorkload.tokenExpiration }}
