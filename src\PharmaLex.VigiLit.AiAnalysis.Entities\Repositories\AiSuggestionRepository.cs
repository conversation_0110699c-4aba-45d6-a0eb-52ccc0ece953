﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.AiAnalysis.Entities.Interfaces;

namespace PharmaLex.VigiLit.AiAnalysis.Entities.Repositories;
public class AiSuggestionRepository : TrackingGenericRepository<AiSuggestion>, IAiSuggestionRepository
{
    public AiSuggestionRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<AiSuggestion> GetBySourceIdAndSubstance(string sourceId, string substance)
    {
        return await context.Set<AiSuggestion>()
            .Where(i => i.SourceId == sourceId && i.Substance == substance)
            .OrderByDescending(x => x.LastUpdatedDate)
            .FirstOrDefaultAsync();
    }
}