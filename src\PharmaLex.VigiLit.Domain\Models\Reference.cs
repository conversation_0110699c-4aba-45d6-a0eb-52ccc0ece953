using PharmaLex.VigiLit.ReferenceManagement;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace PharmaLex.VigiLit.Domain.Models;

public class Reference : ReferenceBase
{
    protected internal IList<ReferenceClassification> ReferenceClassificationsInternal { get; protected set; } = new List<ReferenceClassification>();
    public IReadOnlyCollection<ReferenceClassification> ReferenceClassifications => new ReadOnlyCollection<ReferenceClassification>(ReferenceClassificationsInternal);

    public bool ShouldSilentUpdate(IReference source)
    {
        // If all these fields are unchanged, then we can do a silent update.
        if (string.Equals(Abstract, source.Abstract)
            && string.Equals(AffiliationTextFirstAuthor, source.AffiliationTextFirstAuthor)
            && string.Equals(Authors, source.Authors)
            && string.Equals(CountryOfOccurrence, source.CountryOfOccurrence)
            && string.Equals(Doi, source.Doi)
            && string.Equals(Keywords, source.Keywords)
            && string.Equals(Language, source.Language)
            && string.Equals(MeshHeadings, source.MeshHeadings)
            && string.Equals(Title, source.Title))
        {
            return true;
        }

        return false;
    }

    public bool IsNew(ReferenceIdentifiers identifiers)
    {
        return identifiers == null;
    }

    public bool IsNewer(DateTime dateRevised)
    {
        return DateTime.Compare(DateRevised, dateRevised) > 0;
    }
}
