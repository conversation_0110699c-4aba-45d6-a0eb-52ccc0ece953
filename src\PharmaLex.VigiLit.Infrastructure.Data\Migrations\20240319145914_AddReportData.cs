﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddReportData : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("exec('INSERT INTO [dbo].[Reports] ( [Name] ,[Description], [ReportType], [AllCompanies], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT ''Tracking Sheets'', ''Weekly reports of imported contracts.'', 1, 0, GETUTCDATE(),''Script'', GETUTCDATE(), ''Script'';" +

                                 "INSERT INTO [dbo].[Reports] ( [Name] ,[Description], [ReportType], [AllCompanies], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                                 "SELECT ''Apogepha Client Report'', ''Monthly reports of new classifications.'', 2, 0, GETUTCDATE(),''Script'', GETUTCDATE(), ''Script'';')");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // VigiLit does not go back in versions
        }
    }
}
