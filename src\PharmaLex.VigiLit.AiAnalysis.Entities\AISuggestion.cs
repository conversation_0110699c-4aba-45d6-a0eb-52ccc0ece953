﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.AiAnalysis.Entities.Enums;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.AiAnalysis.Entities;
public class AiSuggestion : EntityBase
{
    public int Id { get; set; }
    public AiAnalysisStatus Status { get; set; }
    [MaxLength(450)]
    public string SourceId { get; set; }
    public string CorrelationId { get; set; }
    public string Substance { get; set; }
    public string Category { get; set; }
    public string CategoryReason { get; set; }
    public string DosageForm { get; set; }
    public string DosageFormReason { get; set; }

    public void Update(string category, string categoryReason, string dosageForm, string dosageFormReason, AiAnalysisStatus status)
    {
        Category = category;
        CategoryReason = categoryReason;
        DosageForm = dosageForm;
        DosageFormReason = dosageFormReason;
        Status = status;
    }

    public void Update(AiAnalysisStatus status)
    {
        Status = status;
    }
}
