﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ReferenceManagement;

namespace PharmaLex.VigiLit.Domain.Models;

public class ReferenceHistoryAction : VigiLitEntityBase
{
    public int ReferenceClassificationId { get; set; }

    public ReferenceClassification ReferenceClassification { get; set; }

    public ReferenceHistoryActionType ReferenceHistoryActionType { get; set; }

    public int? UserId { get; protected set; }

    public User User { get; set; }

    public DateTime TimeStamp { get; set; }

    public ReferenceHistoryAction() { }
    public ReferenceHistoryAction(ReferenceHistoryActionType referenceHistoryActionType, int? userId)
    {
        ReferenceHistoryActionType = referenceHistoryActionType;
        UserId = userId;
    }
    public ReferenceHistoryAction(int classificationId, ReferenceHistoryActionType referenceHistoryActionType, int? userId)
    {
        ReferenceClassificationId = classificationId;
        ReferenceHistoryActionType = referenceHistoryActionType;
        UserId = userId;
    }
}