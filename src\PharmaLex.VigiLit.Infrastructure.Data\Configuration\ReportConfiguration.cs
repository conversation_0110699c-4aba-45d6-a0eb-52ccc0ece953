using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ReportConfiguration : EntityBaseMap<Report>
{
    public override void Configure(EntityTypeBuilder<Report> builder)
    {
        base.Configure(builder);

        builder.ToTable("Reports");

        builder.Property(e => e.Name).IsRequired().HasMaxLength(250);
        builder.Property(e => e.Description).IsRequired().HasMaxLength(500);
        builder.Property(e => e.ControllerName).IsRequired().HasMaxLength(250);
        builder.Property(e => e.Claims).IsRequired().HasMaxLength(250);

        builder.HasIndex(c => new { c.ControllerName });
    }
}