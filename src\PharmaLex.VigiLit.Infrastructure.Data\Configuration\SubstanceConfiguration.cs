using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class SubstanceConfiguration : EntityBaseMap<Substance>
{
    public override void Configure(EntityTypeBuilder<Substance> builder)
    {
        base.Configure(builder);

        builder.ToTable("Substances", t =>
        {
            t.IsTemporal();
        });

        builder.Property(s => s.Name).IsRequired().HasMaxLength(250);

        builder.HasIndex(s => new { s.Name, s.Type }).IsUnique();
    }
}