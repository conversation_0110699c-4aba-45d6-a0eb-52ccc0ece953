using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ContractManagement.Enums;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;

internal class ImportingContractRepository : TrackingGenericRepository<Contract>, IImportingContractRepository
{
    public ImportingContractRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<ContractVersion>> GetContractsToMatchOn()
    {
        var query = context.Set<ContractVersion>()
          .Include(cv => cv.ContractVersionJournals)
              .ThenInclude(j => j.Journal)
          .Include(cv => cv.Contract)
              .ThenInclude(p => p.Project)
                  .ThenInclude(y => y.Company)
          .Where(cv => cv.IsActive
              && cv.ContractVersionStatus == ContractVersionStatus.Approved
              && cv.Contract.Project.Company.IsActive
              && (cv.Contract.ScreeningType != (int)ScreeningType.Local
              || cv.ContractVersionJournals.Any(cvj => cvj.Journal.Enabled)));

        return await query.ToListAsync();
    }
}
