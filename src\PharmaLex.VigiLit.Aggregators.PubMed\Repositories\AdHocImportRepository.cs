﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Repositories;

internal class AdHocImportRepository : TrackingGenericRepository<AdHocImport>, IAdHocImportRepository
{
    protected readonly IMapper _mapper;

    public AdHocImportRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<AdHocImport> GetForEnqueue(int id)
    {
        AdHocImport? adHocImport = null;
        adHocImport = await context.Set<AdHocImport>()
            .Include(i => i.AdHocImportContracts)
            .ThenInclude(i => i.Contract)
            .ThenInclude(c => c.ContractVersionsInternal)
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
        return adHocImport!;
    }
}