﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.ImportManagement.Enums;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class ImportContract : VigiLitEntityBase
{
    public int ImportId { get; set; }
    public int ContractId { get; set; }
    public int ContractVersionId { get; set; }

    public DateTime? ImportDate { get; set; }

    public DateTime? PubMedModificationDate { get; set; }
    public DateTime? PubMedCreateDateFrom { get; set; }

    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public ImportContractStatusType ImportContractStatusType { get; set; }

    public int ReferencesCount { get; set; }
    public int NewReferencesCount { get; set; }
    public int UpdatesCount { get; set; }
    public int SilentUpdatesCount { get; set; }

    public Import Import { get; set; }
    public Contract Contract { get; set; }
    public ICollection<ImportContractReferenceClassification> ImportContractReferenceClassifications { get; set; } = new List<ImportContractReferenceClassification>();

    public ImportContract()
    {
    }

    public ImportContract(Contract contract, DateTime pubMedModificationDate, DateTime? pubMedCreateDateFrom)
    {
        ContractId = contract.Id;
        ContractVersionId = contract.GetCurrentContractVersion().Id;
        PubMedModificationDate = pubMedModificationDate;
        ImportDate = pubMedModificationDate.AddDays(2);
        ImportContractStatusType = ImportContractStatusType.Queued;
        PubMedCreateDateFrom = pubMedCreateDateFrom;
    }

    public void StartProcessing()
    {
        if (ImportContractStatusType == ImportContractStatusType.Queued)
        {
            StartDate = DateTime.UtcNow;
            ImportContractStatusType = ImportContractStatusType.Started;
        }

        Import.StartProcessing();
    }

    public void EndProcessing(ImportContractStatusType status)
    {
        EndDate = DateTime.UtcNow;
        ImportContractStatusType = status;
    }

    public static ImportContract CreateCompletedImportContract(Import import, ContractVersion contractVersion, TimeProvider timeProvider)
    {
        return new ImportContract
        {
            Import = import,
            ContractId = contractVersion.ContractId,
            ContractVersionId = contractVersion.Id,
            ImportDate = timeProvider.GetUtcNow().UtcDateTime,
            ImportContractStatusType = ImportContractStatusType.Completed,
            NewReferencesCount = 1,
            StartDate = timeProvider.GetUtcNow().UtcDateTime,
            ReferencesCount = 1,
            EndDate = timeProvider.GetUtcNow().UtcDateTime
        };
    }
}
