using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.UserManagement;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class Substance : VigiLitEntityBase
{
    public string Name { get; private set; }
    public string Type { get; private set; }

    protected internal ICollection<User> UsersInternal { get; set; }

    public ICollection<UserSubstance> UserSubstances { get; set; }

    public ICollection<SubstanceSynonym> SubstanceSynonyms { get; set; }

    public Substance()
    {
        UsersInternal = new HashSet<User>();
    }

    public Substance(string name, string type)
    {
        Name = name ?? throw new ArgumentNullException(nameof(name));
        Type = type ?? throw new ArgumentNullException(nameof(type));
    }

    public void Update(string name, string type)
    {
        Name = name;
        Type = type;
    }
}
