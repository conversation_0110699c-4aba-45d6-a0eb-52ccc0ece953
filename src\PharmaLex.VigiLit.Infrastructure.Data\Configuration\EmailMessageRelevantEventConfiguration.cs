using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class EmailMessageRelevantEventConfiguration : EntityBaseMap<EmailMessageRelevantEvent>
{
    public override void Configure(EntityTypeBuilder<EmailMessageRelevantEvent> builder)
    {
        base.Configure(builder);

        builder.ToTable("EmailMessageRelevantEvents");

        builder.HasOne(em => em.EmailMessage).WithMany(e => e.EmailMessageRelevantEvents).HasForeignKey(em => em.EmailMessageId);
    }
}