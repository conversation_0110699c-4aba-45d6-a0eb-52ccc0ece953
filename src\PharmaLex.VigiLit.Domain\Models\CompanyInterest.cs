﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class CompanyInterest : VigiLitEntityBase
{
    public int CompanyId { get; set; }
    public int ReferenceId { get; set; }
    public int ReferenceClassificationId { get; set; }

    public Company Company { get; set; }
    public Reference Reference { get; set; }
    public ReferenceClassification ReferenceClassification { get; set; }

    public ICollection<EmailRelevantEvent> EmailRelevantEvents { get; set; } = new List<EmailRelevantEvent>();

    public CompanyInterest() 
    { 
    }

    public void AddEmailRelevantEvent(EmailRelevantEventActionType actionType, int? classificationCategoryId)
    {
        EmailRelevantEvents.Add(new EmailRelevantEvent()
        {
            EmailRelevantEventActionType = actionType,
            ClassificationCategoryId = classificationCategoryId,
            EmailRelevantEventEmailStatusType = EmailRelevantEventEmailStatusType.New
        });
    }
}
