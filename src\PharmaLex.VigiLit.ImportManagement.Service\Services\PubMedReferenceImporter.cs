﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.FeatureManagement;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.Core.Aggregator.Models;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Contracts;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Helpers;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.ReferenceManagement.Contracts;
using System.Diagnostics.CodeAnalysis;

// Legacy PubMed code so turn off the NRT checking for now
#pragma warning disable CS8600, CS8602,CS8603

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;

internal class PubMedReferenceImporter : IImportProcessingService
{
    private readonly ImportOptions _options;
    private readonly ILogger<PubMedReferenceImporter> _logger;
    private readonly IImportingImportRepository _importRepository;
    private readonly IImportingImportContractRepository _importContractRepository;
    private readonly IImportingReferenceRepository _referenceRepository;
    private readonly IImportingReferenceClassificationRepository _referenceClassificationRepository;
    private readonly IImportingReferenceUpdateRepository _referenceUpdateRepository;
    private readonly IFeatureManager _featureManager;
    private readonly IReferenceResolver _importProcessingHelperService;
    private readonly IReferenceManager _referenceManager;
    private readonly IAiReferencePublisher _aiReferencePublisher;
    private const string AiMessageBusSend = "AiMessageBusSend";

    [SuppressMessage("Major Code Smell", "S107:Methods should not have too many parameters", Justification = "Configured limit is 7, we need 8 here and that's ok.")]
    public PubMedReferenceImporter(
        IOptions<ImportOptions> options,
        ILoggerFactory loggerFactory,
        IImportingImportRepository importRepository,
        IImportingImportContractRepository importContractRepository,
        IImportingReferenceRepository referenceRepository,
        IImportingReferenceClassificationRepository referenceClassificationRepository,
        IImportingReferenceUpdateRepository referenceUpdateRepository,
        IFeatureManager featureManager,
        IReferenceResolver importProcessingHelperService,
        IReferenceManager referenceManager,
        IAiReferencePublisher aiReferencePublisher)
    {
        _options = options.Value;
        _logger = loggerFactory.CreateLogger<PubMedReferenceImporter>();
        _importRepository = importRepository;
        _importContractRepository = importContractRepository;
        _referenceRepository = referenceRepository;
        _referenceClassificationRepository = referenceClassificationRepository;
        _referenceUpdateRepository = referenceUpdateRepository;
        _featureManager = featureManager;
        _importProcessingHelperService = importProcessingHelperService;
        _referenceManager = referenceManager;
        _aiReferencePublisher = aiReferencePublisher;
    }

    [Trace]
    public async Task<ImportProcessingParams> ImportProcessingActivity(ImportProcessingParams input)
    {
        if (!input.HasWorkToDo())
        {
            throw new ArgumentException("Input has no remaining work to do. Orchestration shouldn't have called the activity.");
        }

        // whilst it's not usual to have a zero import contract, it can happen:
        // - import may contain no import contracts at all (enqueue scheduled import called twice on the same usa day)
        // - import may contain no import contracts which still require processing (shouldn't happen)
        if (input.ImportContractId <= 0)
        {
            await UpdateImportStatus(input.ImportId);
            return await GetNextImportProcessingParams();
        }

        _logger.LogInformation("Importing ImportContractId={importContractId}.", input.ImportContractId);

        ImportContract importContract = await _importContractRepository.GetByIdForImport(input.ImportContractId);

        DateTime timeout = DateTime.UtcNow.AddMinutes(_options.TimeoutMinutes);

        var aiReferences = new List<Reference>();

        try
        {
            importContract.StartProcessing();

            ESearchCriteria criteria = GetESearchCriteria(importContract);

            ReferenceResults results = await _importProcessingHelperService.GetReferences(criteria, timeout);

            aiReferences.AddRange(results.References);

            importContract = await ProcessResults(input, importContract, timeout, results);
        }
        catch (TimeoutException ex)
        {
            _logger.LogError(ex, "Timeout importing ImportContractId={importContractId} after {minutes} minutes. \nError: {error}", input.ImportContractId, _options.TimeoutMinutes, ex.ToString());
            importContract.EndProcessing(ImportContractStatusType.Timeout);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing ImportContractId={importContractId}. \nError: {error}", input.ImportContractId, ex.ToString());
            importContract.EndProcessing(ImportContractStatusType.Failed);
        }

        await _referenceUpdateRepository.SaveChangesAsync(); // updates
        await _referenceRepository.SaveChangesAsync(); // silent updates
        await _importContractRepository.SaveChangesAsync();

        try
        {
            await SendToAi(aiReferences, importContract);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending references to Ai Message Bus. \nError: {Error}", ex.ToString());
        }

        await UpdateImportStatus(input.ImportId);
        return await GetNextImportProcessingParams();
    }

    private async Task<ImportContract> ProcessResults(ImportProcessingParams input, ImportContract importContract, DateTime timeout, ReferenceResults results)
    {
        if (results.SearchStringMissing)
        {
            importContract.EndProcessing(ImportContractStatusType.InvalidSearchString);
        }
        else if (results.SearchFailed)
        {
            importContract.EndProcessing(ImportContractStatusType.SearchFailed);
        }
        else if (results.FetchFailed)
        {
            importContract.EndProcessing(ImportContractStatusType.Failed);
        }
        else
        {
            importContract = await ProcessReferences(input, timeout, importContract, results);

            if (results.ResultsCapped)
            {
                importContract.EndProcessing(ImportContractStatusType.CompletedCapped);
            }
            else
            {
                importContract.EndProcessing(ImportContractStatusType.Completed);
            }
        }

        return importContract;
    }

    [Trace]
    [SuppressMessage("Major Bug", "S2583:Conditionally executed code should be reachable", Justification = "Code is reachable.")]
    private async Task<ImportContract> ProcessReferences(ImportProcessingParams input, DateTime timeout, ImportContract importContract, ReferenceResults results)
    {
        // count references from 0 even if processing is resumed to avoid double-counting
        int referencesCount = 0;

        foreach (var reference in results.References)
        {
            CheckTimeout(timeout);

            referencesCount++;

            if (referencesCount > importContract.ReferencesCount)
            {
                importContract.ReferencesCount = referencesCount;
            }

            var identifiers = await _referenceRepository.GetReferenceIdentifiers(reference.SourceId);

            if (reference.IsNew(identifiers))
            {
                _referenceManager.UpdateImportContractWithReference(importContract, reference);
            }
            else
            {
                await ProcessUpdate(identifiers, importContract, reference);
            }

            // process results in batches because a 10k result set will cause efcore perf to drop off a cliff
            if (referencesCount % 250 == 0)
            {
                // save changes
                await _referenceUpdateRepository.SaveChangesAsync(); // updates
                await _referenceRepository.SaveChangesAsync(); // silent updates
                await _importContractRepository.SaveChangesAsync();

                // clear all the change trackers
                // - it should not be necessary to do this for each repo because PlxDbContext is Scoped all the repos should contain the same object
                // - but that cannot be relied upon as per https://github.com/Azure/azure-functions-host/issues/5098 
                _importContractRepository.ClearChangeTracker();
                _referenceUpdateRepository.ClearChangeTracker();
                _referenceRepository.ClearChangeTracker();
                _referenceClassificationRepository.ClearChangeTracker();

                // reload the import contract to continue
                importContract = await _importContractRepository.GetByIdForImport(input.ImportContractId);
            }
        }

        return importContract;
    }

    [Trace]
    private async Task SendToAi(List<Reference> aiReferences, ImportContract importContract)
    {
        if (aiReferences.Count > 0 && await _featureManager.IsEnabledAsync(AiMessageBusSend))
        {
            var substance = await _importProcessingHelperService.GetSubstance(importContract.Contract.SubstanceId);

            foreach (var reference in aiReferences)
            {
                var sourceSystem = ((int)SourceSystem.PubMed).ToString();
                var synonyms = substance.SubstanceSynonyms.Select(x => x.Name).ToList();
                var referenceData = new PreClassifyReferenceCommand(reference.Title, reference.Abstract, reference.SourceId, sourceSystem, substance.Name, synonyms);

                await _aiReferencePublisher.Send(referenceData);
            }
        }
    }

    [Trace]
    public async Task<ImportProcessingParams> GetNextImportProcessingParams()
    {
        // Get the highest priority import for processing
        var import = await _importRepository.GetPriorityImportForProcessing();

        if (import == null)
        {
            return NoImportAvailable();
        }
        if (import.HasNoImportContracts())
        {
            return NoImportContracts(import.Id);
        }
        var importContract = GetNextQueuedImportContract(import);

        if (importContract == null)
        {
            return NoImportContracts(import.Id);
        }
        return ProcessNextImportContract(import.Id, importContract.Id);
    }

    private static ImportProcessingParams NoImportAvailable() => new ImportProcessingParams(0, 0);

    private static ImportProcessingParams NoImportContracts(int importId) => new ImportProcessingParams(importId, 0);

    private static ImportProcessingParams ProcessNextImportContract(int importId, int contractId) => new ImportProcessingParams(importId, contractId);

    private static ImportContract? GetNextQueuedImportContract(Import import)
    {
        return import.ImportContracts
            .Where(ic => ImportContractStatusTypeGroups.Queued.Contains(ic.ImportContractStatusType))
            .OrderBy(ic => ic.Id)
            .FirstOrDefault();
    }


    private static ESearchCriteria GetESearchCriteria(ImportContract importContract)
    {
        ESearchCriteria criteria = new()
        {
            Term = importContract.Contract.GetSearchTerm(importContract.ContractVersionId),
            ModifiedDate = importContract.PubMedModificationDate,
            CreateDateFrom = importContract.PubMedCreateDateFrom
        };

        return criteria;
    }

    private static void CheckTimeout(DateTime timeout)
    {
        if (DateTime.UtcNow >= timeout)
        {
            throw new TimeoutException("Timeout from ImportService.");
        }
    }

    private async Task EnsureAllocation(ReferenceIdentifiers identifiers, ImportContract importContract, ICRCType icrcType)
    {
        bool alreadyAllocated = importContract.ImportContractReferenceClassifications
            .Any(icrc => icrc.ReferenceClassification.ReferenceId == identifiers.ReferenceId && icrc.ReferenceClassification.SubstanceId == importContract.Contract.SubstanceId);

        if (!alreadyAllocated)
        {
            // Find the classification for this substance
            var classification = await _referenceClassificationRepository.GetClassificationForImport(identifiers.ReferenceId, importContract.Contract.SubstanceId);

            // If it exists, allocate it.
            if (classification != null)
            {
                importContract.ImportContractReferenceClassifications.Add(new ImportContractReferenceClassification(importContract, classification, icrcType));
            }
            // If it doesn't exist, this means the updated reference hasn't been a search result
            // for a contract with this substance yet, so make a new classification and allocate it.
            else
            {
                var referenceClassification = new ReferenceClassification(identifiers.ReferenceId, importContract.Contract.SubstanceId);
                var importContractReferenceClassification = new ImportContractReferenceClassification(importContract, referenceClassification, icrcType);
                importContract.ImportContractReferenceClassifications.Add(importContractReferenceClassification);

                // Add Reference History Action - New Reference
                var referenceHistoryAction = new ReferenceHistoryAction(ReferenceHistoryActionType.New, null);
                referenceClassification.ReferenceHistoryActions.Add(referenceHistoryAction);
            }
        }
    }

    private async Task ProcessUpdate(ReferenceIdentifiers identifiers, ImportContract importContract, Reference reference)
    {
        var updates = await _referenceUpdateRepository.GetUpdatesByReferenceId(identifiers.ReferenceId);
        var updateForSubstance = updates.FirstOrDefault(u => u.SubstanceId == importContract.Contract.SubstanceId);

        // Update any existing updates
        // - 0-N of them can be updates from other contracts
        // - 0-1 of them can be updates from this contract
        foreach (var update in updates)
        {
            // Only update the update if the incoming reference is newer
            if (reference.IsNewer(update.DateRevised))
            {
                update.TakeUpdates(reference);

                // There is an existing update and we just updated it
                if (update.Is(updateForSubstance))
                {
                    await EnsureAllocation(identifiers, importContract, ICRCType.UpdatedUpdate);
                }
            }
            // There is an existing update but it didn't need updating
            else if (update.Is(updateForSubstance))
            {
                await EnsureAllocation(identifiers, importContract, ICRCType.CurrentUpdate);
            }
        }

        // There is no update for this substance but one is needed
        if (updateForSubstance == null && reference.IsNewer(identifiers.DateRevised))
        {
            // Silent update if you can, otherwise make a new update.
            bool silentlyUpdated = await TrySilentUpdate(reference, identifiers, importContract);

            if (silentlyUpdated)
            {
                await EnsureAllocation(identifiers, importContract, ICRCType.SilentUpdate);
            }
            else
            {
                _referenceManager.AddReferenceUpdate(importContract, identifiers, reference);

                await EnsureAllocation(identifiers, importContract, ICRCType.NewUpdate);
            }
        }
        // The reference is already up to date
        // - maybe another contract in this import created it
        // - maybe another contract in this import silently updated it
        else if (updateForSubstance == null && !reference.IsNewer(identifiers.DateRevised))
        {
            await EnsureAllocation(identifiers, importContract, ICRCType.Current);
        }
    }

    private async Task<bool> TrySilentUpdate(Reference reference, ReferenceIdentifiers identifiers, ImportContract importContract)
    {
        Reference existingReference = await _referenceRepository.GetByIdForImport(identifiers.ReferenceId);

        if (existingReference.ShouldSilentUpdate(reference))
        {
            _logger.LogInformation("Silent Update: ReferenceId={referenceId}, SourceId={SourceId}", identifiers.ReferenceId, reference.SourceId);

            existingReference.TakeUpdates(reference);

            importContract.SilentUpdatesCount++;

            return true;
        }

        return false;
    }

    private async Task UpdateImportStatus(int importId)
    {
        var import = await _importRepository.GetByIdForImport(importId);
        if (import == null)
        {
            throw new ArgumentException($"import could not be found");
        }
        import.StartProcessing();

        var statusCounts = GetStatusCounts(import.ImportContracts);
        var queuedCount = GetGroupCount(ImportContractStatusTypeGroups.Queued, statusCounts);
        // Determine the import status based on the counts
        if (queuedCount == 0)
        {
            var importStatus = GetGroupCount(ImportContractStatusTypeGroups.Succeeded, statusCounts) == import.ImportContracts.Count
            ? ImportStatusType.Completed
            : ImportStatusType.CompletedWithFailedContracts;

            import.EndProcessing(importStatus);
            await _importRepository.SaveChangesAsync();
            _logger.LogInformation("Import Completed. ImportId={importId}.", importId);
        }
        else
        {
            _logger.LogInformation("Import is still in progress. ImportId={importId}.", importId);
        }
    }

    private static Dictionary<ImportContractStatusType, int> GetStatusCounts(IEnumerable<ImportContract> importContracts)
    {
        return importContracts
            .GroupBy(ic => ic.ImportContractStatusType)
            .ToDictionary(g => g.Key, g => g.Count());
    }
    private static int GetGroupCount(IEnumerable<ImportContractStatusType> statusGroup, Dictionary<ImportContractStatusType, int> statusCounts)
    {
        return statusGroup.Sum(status => statusCounts.GetValueOrDefault(status, 0));
    }

}
