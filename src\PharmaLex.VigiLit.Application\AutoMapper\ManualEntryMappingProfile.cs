﻿using AutoMapper;
using PharmaLex.VigiLit.ImportManagement.Entities;

using PharmaLex.VigiLit.ImportManagement.Ui.ManualEntry;

namespace PharmaLex.VigiLit.Application.AutoMapper;
public class ManualEntryMappingProfile : Profile
{

    public ManualEntryMappingProfile()
    {
        CreateMap<ImportManualEntry, ManualEntryModel>().ReverseMap();
        CreateMap<ImportManualEntry, ImportReference>()
            .ForMember(x => x.Id, opt => opt.Ignore());
    }
}