using AutoMapper;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Application.Services;

public class CompanyService : ICompanyService
{
    private readonly ICompanyRepository _companyRepository;
    private readonly IUserSessionService _userSessionService;
    private readonly IMapper _mapper;

    public CompanyService(ICompanyRepository companyRepository, IUserSessionService userSessionService, IMapper mapper)
    {
        _companyRepository = companyRepository;
        _userSessionService = userSessionService;
        _mapper = mapper;
    }

    public async Task<IEnumerable<CompanyModel>> GetAllAsync()
    {
        var companies = await _companyRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<CompanyModel>>(companies);
    }

    public async Task<CompanyModel> GetByIdAsync(int id)
    {
        var company = await _companyRepository.GetByIdAsync(id);
        return _mapper.Map<CompanyModel>(company);
    }

    public async Task AddAsync(CompanyModel model)
    {
        var company = new Company(model.Name, model.ContactPersonName, model.ContactPersonEmail, model.IsActive);
        _companyRepository.Add(company);
        await _companyRepository.SaveChangesAsync();
    }

    public async Task UpdateAsync(CompanyModel model)
    {
        var company = await _companyRepository.GetByIdAsync(model.Id);
        company.Update(model.Name, model.ContactPersonName, model.ContactPersonEmail, model.IsActive);

        await _companyRepository.SaveChangesAsync();

        if (!model.IsActive)
        {
            var companyUsers = await GetCompanyUsers(model.Id);
            var users = companyUsers.CompanyUsers.Select(x => x.Id).ToList();
            await _userSessionService.DeleteUserSessionsAsync(users);
        }
    }

    public async Task<bool> ValidateNameAsync(string name, int id)
    {
        var company = await _companyRepository.GetByNameAsync(name);

        if (company == null)
        {
            return true;
        }

        return company.Id == id;
    }

    public async Task<CompanyWithContractsModel> GetCompanyWithContracts(int companyId)
    {
        var company = await _companyRepository.GetWithContractsByIdAsync(companyId);
        return _mapper.Map<CompanyWithContractsModel>(company);
    }

    public async Task<CompanyWithProjectsModel> GetCompanyWithProjects(int companyId)
    {
        var company = await _companyRepository.GetWithProjectsByIdAsync(companyId);
        return _mapper.Map<CompanyWithProjectsModel>(company);
    }

    public async Task<CompanyUserListModel> GetCompanyUsers(int companyId)
    {
        var companies = await _companyRepository.GetWithUsersAndSuppressionsByIdAsync(companyId);
        return _mapper.Map<CompanyUserListModel>(companies);
    }

    public async Task<IEnumerable<CompanyEmailModel>> GetActiveCompaniesWithUsers()
    {
        var companies = await _companyRepository.GetActiveCompaniesWithUsers();
        return companies;
    }

    public async Task<IEnumerable<CompanyModel>> GetCompaniesForSubstanceAsync(int substanceId)
    {
        var companies = await _companyRepository.GetCompaniesForSubstanceAsync(substanceId);
        return _mapper.Map<IEnumerable<CompanyModel>>(companies);
    }

    public async Task<IEnumerable<CompanyItemModel>> GetForSearch(User user)
    {
        return await _companyRepository.GetCompanyItems(user);
    }

    public async Task<IEnumerable<CompanyModel>> GetActiveCompaniesWithActiveContractsForSubstance(int substanceId)
    {
        return await _companyRepository.GetActiveCompaniesWithActiveContractsForSubstance(substanceId);
    }
}