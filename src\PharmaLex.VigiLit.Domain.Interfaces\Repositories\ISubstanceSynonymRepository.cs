﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface ISubstanceSynonymRepository : ITrackingRepository<SubstanceSynonym>
{
    Task<IEnumerable<SubstanceSynonym>> GetAllAsync();
    Task<SubstanceSynonym?> GetByIdAsync(int id);
    public Task<SubstanceSynonym?> GetByNameAsync(string name, int substanceId);
}