using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class ImportContractMappingProfile : Profile
{
    public ImportContractMappingProfile()
    {
        CreateMap<ImportContract, ImportContractModel>()
            .ForMember(i => i.CompanyName, s => s.MapFrom(c => c.Contract.Project.Company.Name))
            .ForMember(i => i.ProjectName, s => s.MapFrom(c => c.Contract.Project.Name))
            .ForMember(i => i.SubstanceName, s => s.MapFrom(c => c.Contract.Substance.Name))
            .ForMember(d => d.ImportDate, s => s.MapFrom(x => x.ImportDate.HasValue ? x.ImportDate.Value.ToString("dd MMM yyyy") : string.Empty))
            .ForMember(d => d.PubMedModificationDate, s => s.MapFrom(x => x.PubMedModificationDate.HasValue ? x.PubMedModificationDate.Value.ToString("dd MMM yyyy") : string.Empty))
            .ForMember(d => d.StartDate, s => s.MapFrom(x => x.StartDate.HasValue ? x.StartDate.Value.ToString("dd MMM yyyy HH:mm:ss") : string.Empty))
            .ForMember(d => d.EndDate, s => s.MapFrom(x => x.EndDate.HasValue ? x.EndDate.Value.ToString("dd MMM yyyy HH:mm:ss") : string.Empty))
            .ForMember(d => d.Duration, s => s.MapFrom(x => x.EndDate.HasValue ? string.Format("{0:hh\\:mm\\:ss}", x.EndDate.Value - x.StartDate) : string.Empty))
            .ForMember(i => i.ImportContractStatusType, s => s.MapFrom(c => c.ImportContractStatusType.GetDescription()));
    }
}

