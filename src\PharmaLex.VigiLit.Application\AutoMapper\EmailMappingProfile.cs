using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class EmailMappingProfile : Profile
{
    public EmailMappingProfile()
    {
        CreateMap<Email, EmailModel>()
            .ForMember(d => d.EmailType, s => s.MapFrom(x => x.EmailType.GetDescription()))
            .ForMember(d => d.EmailTriggerType, s => s.MapFrom(x => x.EmailTriggerType.GetDescription()))
            .ForMember(d => d.EmailStatusType, s => s.MapFrom(x => x.EmailStatusType.GetDescription()))
            .ForMember(d => d.StartDate, s => s.MapFrom(x => x.StartDate.ToString("dd MMM yyyy HH:mm:ss")))
            .ForMember(d => d.EndDate, s => s.MapFrom(x => x.EndDate.HasValue ? x.EndDate.Value.ToString("dd MMM yyyy HH:mm:ss") : string.Empty))
            .ForMember(d => d.Duration, s => s.MapFrom(x => x.EndDate.HasValue ? string.Format("{0:hh\\:mm\\:ss}", x.EndDate.Value - x.StartDate) : string.Empty));
    }
}
