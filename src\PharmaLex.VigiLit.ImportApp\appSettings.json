{"AzureAdGraph": {"ClientId": "7bc93d0f-ddf4-40da-8c80-542d161538e6", "Domain": "yes-services.eu"}, "AzureAdB2CGraph": {"ClientId": "5c76d17d-764a-4e8e-bef7-c39afd6fb3bd", "Domain": "smartphlexb2c.onmicrosoft.com"}, "KeyVaultName": "vgt-dev-kv-eun", "AzureStorage": {"CaseDocument": {"AccountName": "vgtdevsharedeun", "ContainerName": "case-document"}, "TrackingSheetDocument": {"AccountName": "vgtdevsharedeun", "ContainerName": "tracking-sheet-document"}}, "VisualStudioTenantId": "66b904a2-2bfc-4d24-a410-96b77b32bf77", "ConnectionStrings": {"default": "takenFromVaultOrSecrets"}, "Static": {"App": "VigiLit", "Env": "local"}, "HostUri": "https://vigilit-dev.smartphlex.com/", "EmailSettings": {"NoReplyEmail": "<EMAIL>", "NoReplyEmailName": "VigiLit 7", "InfoEmail": "<EMAIL>", "OrderingEmail": "<EMAIL>", "DailyReferenceClassificationEmailTemplateId": "d-cbb1271e7c624dd299a3032f44bf73f9", "CaseEmailTemplateId": "d-90ecabd7bad948a9b1cb1e20e0904f92", "CaseEmailAttachmentMaxBytes": ********, "WebsiteUri": "https://localhost:5001"}, "ImportSettings": {"TimeoutMinutes": "60"}, "MessageBus": {"TransportType": "AzureServiceBus", "RabbitMq": {"Host": "localhost", "Port": 5672, "VirtualHost": "/"}, "AzureServiceBus": {"Namespace": "vgt-dev-servicebus-eun", "ConnectionString": "vault"}}, "NLog": {"autoReload": true, "internalLogLevel": "Info", "internalLogFile": "/app/logs/internal-nlog.log", "extensions": [{"assembly": "NLog.Web.AspNetCore"}], "targets": {"console-default": {"type": "<PERSON><PERSON><PERSON>", "layout": {"type": "JsonLayout", "includeMdlc": true, "attributes": [{"name": "time", "layout": "${longdate}"}, {"name": "level", "layout": "${uppercase:${level}}"}, {"name": "traceId", "layout": "${activityid:whenEmpty=${mdlc:item=RequestId:whenEmpty=${aspnet-TraceIdentifier}}}"}, {"name": "threadId", "layout": "${threadid}"}, {"name": "logger", "layout": "${logger}"}, {"name": "errorCode", "layout": "${event-properties:item=ErrorCode}"}, {"name": "errorClass", "layout": "${exception:format=Type}"}, {"name": "errorMessage", "layout": "${exception:format=Message}"}, {"name": "errorStack", "layout": "${exception:format=StackTrace}"}, {"name": "message", "layout": "${event-properties:item=Message:whenEmpty=${message}}"}, {"name": "applicationName", "layout": "${appdomain:format={1\\}}"}, {"name": "area", "layout": "${event-properties:item=Area:whenEmpty=${logger}}"}, {"name": "entity.name", "layout": "${gdc:item=entity.name}"}, {"name": "entity.type", "layout": "${gdc:item=entity.type}"}, {"name": "entity.guid", "layout": "${gdc:item=entity.guid}"}, {"name": "hostname", "layout": "${gdc:item=hostname}"}]}}, "filelog-default": {"type": "File", "fileName": "/app/logs/api-${shortdate}.log", "layout": "${longdate}|${event-properties:item=EventId_Id}|${uppercase:${level}}|CorrelationId=${aspnet-item:variable=CorrelationId}|ClientIP=${aspnet-item:variable=ClientIP}|${logger}|${message} ${exception:format=tostring}|url: ${aspnet-request-url}|action: ${aspnet-mvc-action}"}}, "rules": [{"logger": "*", "minlevel": "<PERSON><PERSON>", "writeTo": "filelog-default"}, {"logger": "*", "minlevel": "Info", "writeTo": "console-default"}, {"logger": "Microsoft.*", "minlevel": "Info", "final": true}]}, "PhlexVision": {"Url": "https://phlexvision-integration.phlexglobal.com/api/ocrapi/v1/documents", "ConsumerKeyHeaderValue": "mock", "OpenAiConfigId": "6981a1f6-828d-4928-a1ce-09a65aa0fc39", "Secret": "", "CallbackBaseUrl": ""}}