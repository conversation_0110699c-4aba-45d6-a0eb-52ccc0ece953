﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.ImportManagement.Client;
using PharmaLex.VigiLit.ImportManagement.Contracts;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Service.Auditing;
using PharmaLex.VigiLit.ImportManagement.Service.Clients;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.ImportManagement.Service.Services;

namespace PharmaLex.VigiLit.ImportManagement.Service;

public static class ConfigureServices
{
    public static void RegisterImportManagementClient(this IServiceCollection services)
    {
        services.AddScoped<IImportManagementClient, ImportManagementClient>();
        services.AddScoped<IEnqueueReferenceCommandHand<PERSON>, EnqueueReferenceCommandHandler>();
        services.AddScoped<IManualCorrectionCommandHandler, ManualCorrectionCommandHandler>();
       
    }

    public static void RegisterImportManagement(this IServiceCollection services, IConfiguration configuration)
    {
        // PubMed services - these need moving out to "PharmaLex.VigiLit.Aggregators.PubMed" - nothing useful to share until we maybe get to CrossRef
        services.AddScoped<IImportingImportContractRepository, ImportContractRepository>();
        services.AddScoped<IImportProcessingService, PubMedReferenceImporter>();
        services.AddScoped<IReferenceResolver, ImportProcessingHelperService>();
        services.AddScoped<IImportingImportRepository, ImportRepository>();
        services.AddScoped<IImportingContractRepository, ImportingContractRepository>();
        services.AddScoped<ISubstanceRepository, SubstanceRepository>();

        // UnIndexed reference source registrations - these will stay here
        services.AddScoped<IUnIndexedReferenceImporter, UnIndexedReferenceImporter>();
        services.AddScoped<ICSharpScriptFactory, CSharpScriptFactory>();
        services.AddScoped<IExpressionBuilder, ExpressionBuilder>();
        services.AddScoped<IAbstractTextSearcher, AbstractTextSearcher>();
        services.AddScoped<IReferenceMatcher, ReferenceMatcher>();
        services.AddScoped<IContractMatcherService, ContractMatcherService>();
        services.AddScoped<IImportingImportContractReferenceClassificationRepository, ImportingImportContractReferenceClassificationRepository>();
        services.AddScoped<IReferenceClassificationRepository, ReferenceClassificationRepository>();
        services.AddScoped<IReferenceHistoryActionRepository, ReferenceHistoryActionRepository>();
        services.AddScoped<IReferenceRepository, ReferenceRepository>();
        services.AddScoped<IAuditHandler, AuditHandler>();
        services.AddSingleton(TimeProvider.System);

        // Used by all 
        services.AddScoped<IFailedImportFileRepository, FailedImportFileRepository>();

        services.AddAutoMapper(typeof(ImportReferenceMappingProfile).Assembly);

        // Added for the UI functionality rather than the backend import process
        // This is referenced by "UnIndexedReferenceImporter" and needs splitting out based on comment in line above
        services.AddScoped<IImportReferenceRepository, ImportReferenceRepository>();
    }
}