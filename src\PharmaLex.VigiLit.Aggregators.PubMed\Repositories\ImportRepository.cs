﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Repositories;

internal class ImportRepository : TrackingGenericRepository<Import>, IImportRepository
{
    protected readonly IMapper _mapper;

    public ImportRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<Import?> Get(int id)
    {
        return await context.Set<Import>()
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<Import?> GetWithContracts(int id)
    {
        return await context.Set<Import>()
            .Include(i => i.ImportContracts)
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
    }
}
