﻿using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.Services.Helpers;

/// <summary>
/// Helper class for getting the PreClassifyModel (ViewModel).
/// </summary>
public interface IPreClassifyModelHelper
{
    /// <summary>
    /// Gets pre-classify model populated.
    /// </summary>
    /// <param name="selectedImport">The selected import.</param>
    /// <param name="preClassifyReferenceModels"></param>
    /// <returns></returns>
    Task<PreclassifyModel> GetPreClassifyModel(ImportSelectionModel selectedImport, IEnumerable<PreclassifyReferenceModel> preClassifyReferenceModels);
}