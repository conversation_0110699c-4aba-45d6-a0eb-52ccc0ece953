﻿using AutoMapper;
using System.Linq;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class CompanyUserMappingProfile : Profile
{
    public CompanyUserMappingProfile()
    {
        CreateMap<CompanyUser, CompanyUserModel>()
            .ForMember(d => d.Active, s => s.MapFrom(c => c.Active))
            .ForMember(d => d.Id, s => s.MapFrom(c => c.User.Id))
            .ForMember(d => d.Email, s => s.MapFrom(c => c.User.Email))
            .ForMember(d => d.GivenName, s => s.MapFrom(c => c.User.GivenName))
            .ForMember(d => d.FamilyName, s => s.MapFrom(c => c.User.FamilyName))
            .ForMember(d => d.LastLoginDate, s => s.MapFrom(c => c.User.LastLoginDate))
            .ForMember(d => d.InvitationEmailLink, s => s.MapFrom(c => c.User.InvitationEmailLink))
            .ForMember(d => d.ActivationExpiryDate, s => s.MapFrom(c => c.User.ActivationExpiryDate))
            .ForMember(d => d.EmailPreferenceIds, s => s.MapFrom(c => c.User.UserEmailPreferences.Select(ep => ep.EmailPreferenceId)))
            .ForMember(d => d.UserEmailPreferences, s => s.MapFrom(c => c.User.UserEmailPreferences))
            .ForMember(d => d.EmailSuppression, s => s.MapFrom(c => c.User.EmailSuppression));

        CreateMap<CompanyUser, CompanyUserWithCompanyModal>()
            .ForMember(d => d.Id, s => s.MapFrom(c => c.User.Id))
            .ForMember(d => d.IsActive, s => s.MapFrom(c => c.Active))
            .ForMember(d => d.Email, s => s.MapFrom(c => c.User.Email))
            .ForMember(d => d.GivenName, s => s.MapFrom(c => c.User.GivenName))
            .ForMember(d => d.FamilyName, s => s.MapFrom(c => c.User.FamilyName))
            .ForMember(d => d.LastLoginDate, s => s.MapFrom(c => c.User.LastLoginDate))
            .ForMember(d => d.ClaimIds, s => s.MapFrom(c => c.User.ClaimsInternal))
            .ForMember(d => d.CompanyId, s => s.MapFrom(c => c.Company.Id))
            .ForMember(d => d.IsCompanyActive, s => s.MapFrom(c => c.Company.IsActive))
            .ForMember(d => d.CompanyUserType, s => s.MapFrom(c => c.CompanyUserType));

        CreateMap<CompanyUser, CompanyUserTableModel>()
            .ForMember(d => d.Id, s => s.MapFrom(c => c.User.Id))
            .ForMember(d => d.FamilyName, s => s.MapFrom(c => c.User.FamilyName))
            .ForMember(d => d.GivenName, s => s.MapFrom(c => c.User.GivenName))
            .ForMember(d => d.Email, s => s.MapFrom(c => c.User.Email))
            .ForMember(d => d.Active, s => s.MapFrom(c => c.Active))
            .ForMember(d => d.EmailSuppressionName, s => s.MapFrom(c => c.User.EmailSuppression.EmailSuppressionType.ToString()))
            .ForMember(d => d.DisplayLastLoginDate, s => s.MapFrom(c => c.User.LastLoginDate.HasValue ? c.User.LastLoginDate.Value.ToString("dd MMMM yyyy HH:mm") : string.Empty))
            .ForMember(d => d.DisplayActivationExpiryDate, s => s.MapFrom(c => c.User.ActivationExpiryDate.HasValue ? c.User.ActivationExpiryDate.Value.ToString("dd MMMM yyyy HH:mm") : string.Empty));
    }
}