﻿using Ganss.Xss;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Text.RegularExpressions;

namespace PharmaLex.Core.Web.Helpers;

public static class AntiXss
{
    static readonly Regex HtmlRegex = new(@"</?([a-z]+[1-6]?)", RegexOptions.IgnoreCase, TimeSpan.FromSeconds(10));
    static readonly HashSet<string> HtmlTags = new(StringComparer.OrdinalIgnoreCase) { "a", "abbr", "acronym", "address", "applet", "area", "article", "aside", "audio", "b", "base", "bdi", "bdo", "big", "blockquote", "body", "br", "button", "canvas", "caption", "center", "cite", "code", "col", "colgroup", "command", "datalist", "dd", "del", "details", "dfn", "dir", "div", "dl", "dt", "em", "embed", "fieldset", "figcaption", "figure", "font", "footer", "form", "frame", "h1", "h2", "h3", "h4", "h5", "h6", "head", "header", "hgroup", "hr", "html", "i", "iframe", "img", "input", "ins", "isindex", "kbd", "keygen", "label", "legend", "li", "link", "map", "mark", "menu", "meta", "meter", "nav", "noscript", "object", "ol", "optgroup", "option", "output", "p", "param", "pre", "progress", "q", "rp", "rt", "ruby", "s", "samp", "script", "section", "select", "small", "source", "span", "strike", "strong", "style", "sub", "summary", "sup", "table", "tbody", "td", "textarea", "tfoot", "th", "thead", "time", "title", "tr", "track", "tt", "u", "ul", "var", "video", "wbr" };

    public static string SanitizeJsonTagSoup(string html)
    {
        // Ganss HtmlSanitizer requires us to pre-process less-than symbols in tag soup.
        // Otherwise "for <example" breaks.
        html = HtmlRegex.Replace(html, m =>
        {
            var tagName = m.Groups[1].Value;

            if (!HtmlTags.Contains(tagName))
            {
                return string.Concat("&lt;", m.Value.AsSpan(1));
            }

            return m.Value;
        });

        // Replace codes that become double-quotes because they invalidate json.
        html = html.Replace("&quot;", "\\\"").Replace("&#34;", "\\\"").Replace("&#x22;", "\\\"");

        // Use Ganss HtmlSanitizer to sanitise HTML for XSS protection.
        var sanitizer = new HtmlSanitizer();
        html = sanitizer.Sanitize(html);

        if (!string.IsNullOrWhiteSpace(html))
        {
            //// New lines in the JSON invalidates the Vue component. These can be in EFetch XML: 
            //// - Carriage Return is &#xD; or &#13; sometimes &#xD appears which also breaks
            //// - Line Feed is &#xA; or &#10;
            var token = JToken.Parse(html);
            html = token.ToString(Formatting.None);
        }

        return html;
    }

    public static string SanitizeHtml(string html)
    {
        // Ganss HtmlSanitizer requires us to pre-process less-than symbols in tag soup.
        // Otherwise "for <example" breaks.
        html = HtmlRegex.Replace(html, m =>
        {
            var tagName = m.Groups[1].Value;

            if (!HtmlTags.Contains(tagName))
            {
                return string.Concat("&lt;", m.Value.AsSpan(1));
            }

            return m.Value;
        });

        // Use Ganss HtmlSanitizer to sanitise HTML for XSS protection.
        var sanitizer = new HtmlSanitizer();
        html = sanitizer.Sanitize(html);

        return html;
    }

    public static string ToJson(object obj)
    {
        string json = JsonConvert.SerializeObject(obj);
        json = SanitizeJsonTagSoup(json);
        return json;
    }
}