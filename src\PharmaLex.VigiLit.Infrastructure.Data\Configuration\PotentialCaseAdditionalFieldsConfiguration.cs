﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class PotentialCaseAdditionalFieldsConfiguration : EntityBaseMap<PotentialCaseAdditionalField>
{
    public override void Configure(EntityTypeBuilder<PotentialCaseAdditionalField> builder)
    {
        base.Configure(builder);

        builder.ToTable("PotentialCaseAdditionalFields");

        builder.Property(e => e.Name).IsRequired().HasMaxLength(500);
    }
}