using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ImportSelectionConfiguration : EntityBaseMap<ImportSelection>
{
    public override void Configure(EntityTypeBuilder<ImportSelection> builder)
    {
        base.Configure(builder);

        builder.ToTable("ImportSelections");

        builder.Property(e => e.UserId).IsRequired();
        builder.Property(e => e.ImportId).IsRequired();

        builder.HasIndex(c => new { c.UserId });
        builder.HasIndex(c => new { c.ImportId });
    }
}