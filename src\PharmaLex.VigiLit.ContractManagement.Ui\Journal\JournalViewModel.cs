﻿using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Journals;

public class JournalViewModel
{
    public required int Id { get; set; }

    [Required]
    public required string Name { get; set; }

    [Required]
    public required int CountryId { get; set; }

    public string? Issn { get; set; }

    public string? Url { get; set; }

    public bool Enabled { get; set; }
    public required string CountryName { get; set; }

    public required string CronExpression { get; set; }
}