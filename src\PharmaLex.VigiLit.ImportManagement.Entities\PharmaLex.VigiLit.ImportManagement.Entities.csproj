﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.VigiLit.DataAccessLayer.Base\PharmaLex.VigiLit.DataAccessLayer.Base.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Entities\PharmaLex.VigiLit.DataExtraction.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Client\PharmaLex.VigiLit.ImportManagement.Client.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Contracts\PharmaLex.VigiLit.ImportManagement.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement\PharmaLex.VigiLit.ImportManagement.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ReferenceManagement\PharmaLex.VigiLit.ReferenceManagement.csproj" />
  </ItemGroup>

</Project>
