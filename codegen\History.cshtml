{{#if Dependencies}}{{#each Dependencies}}@using {{this}};
{{/each}}{{/if}}
@model IEnumerable<{{EntityName}}HistoryModel>

 @{
    ViewData["Title"] = "{{EntityNamePlural}}";
 }

<div id="{{EntityNameCamelCase}}" v-cloak>
    <section>
        <div class="flex justify-space-between">
            <h2 class="brand-color">{{EntityName}} History </h2>
            <div class="controls">
                <a asp-action="Index" class="button primary">Back to {{EntityName}} list</a>
            </div>
        </div>

        <div>
            <filtered-table :items="{{EntityNameCamelCase}}" :columns="columns" :filters="filters" :link="link"></filtered-table>
        </div>
    </section>
</div>

    @section Scripts {
  
<script type="text/javascript">

    var pageConfig = {
        appElement: "#{{EntityNameCamelCase}}",
        data: function () {
            return {
                link: '',
                {{EntityNameCamelCase}}: @Html.Raw(Model.ToJson()),
                columns: {
                    idKey: 'id',
                    config: [{{#if PropertiesCamelCase}}{{#each PropertiesCamelCase}}
                        {
                            dataKey: '{{this.Name}}',
                            sortKey: '{{this.Name}}',
                            header: '{{this.Name}}',
                            {{#if this.IsNumber}}type: 'number'{{/if}}
                            {{#if this.IsString}}type: 'text'{{/if}}
                            {{#if this.IsDateTime}}type: 'date'{{/if}}
                            {{#if this.IsUnknownType}}type: 'text'{{/if}}

                        },{{/each}}{{/if}}
                        {
                            dataKey: 'lastUpdatedDate',
                            sortKey: 'lastUpdatedDate',
                            header: 'lastUpdatedDate',
                            edit: {
                                convert: e => moment(e).format('DD MMM YYYY')
                            },
                            type: 'date'
                        },
                    ]
                },
                filters: [
                    {{#if PropertiesCamelCase}}{{#each PropertiesCamelCase}}
                        {
                            key: '{{this.Name}}',
                            options: [],
                            type: 'search',
                            header: 'Search {{this.Name}}',
                            {{#if this.IsString}}fn: v => p => p.{{this.Name}}.toLowerCase().includes(v.toLowerCase()), {{/if}}
                            {{#if this.IsNumber}} fn: v => p => p.{{this.Name}}.toString().includes(v),{{/if}}
                            {{#if this.IsDateTime}} v => p => convertDate({{this.Name}}).toLowerCase().includes(v.toLowerCase()),{{/if}}
                            {{#if this.IsUnknownType}}fn: v => p => p.{{this.Name}}.toLowerCase().includes(v.toLowerCase()), {{/if}}
                            convert: v => v
                    },{{/each}}{{/if}}
                    {
                        key: 'lastUpdatedDate',
                        options: [],
                        type: 'search',
                        header: 'Search lastUpdatedDate',
                        fn: v => p => {
                            console.log(v.toLowerCase());
                            return moment(p.lastUpdatedDate).format('DD MMM YYYY').toLowerCase().includes(v.toLowerCase());
                        },
                        convert: v => v
                    }
                ]
            };
        }
    };
</script>
    }

    @section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
}