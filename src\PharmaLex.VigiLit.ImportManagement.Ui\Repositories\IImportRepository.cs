using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public interface IImportRepository : ITrackingRepository<Import> // Used by ImportQueueService
{
    Task<Import?> GetById(int id);
    Task<Import?> GetByIdForImport(int id);
    Task<IEnumerable<ImportModel>> GetImportLog(int days);
    Task<IEnumerable<ImportDashboardModel>> GetImportsForDashboard();
    Task<ImportDashboardModel?> GetImportForDashboardDetails(int importId);
    Task Archive(int importId);
}