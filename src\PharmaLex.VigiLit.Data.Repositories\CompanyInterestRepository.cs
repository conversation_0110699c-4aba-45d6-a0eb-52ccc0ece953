using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Data.Repositories;

public class CompanyInterestRepository : TrackingGenericRepository<CompanyInterest>, ICompanyInterestRepository
{
    public CompanyInterestRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<bool> CompanyHasInterestInReference(int companyId, int referenceId)
    {
        return await context.Set<CompanyInterest>()
            .AnyAsync(ci => ci.CompanyId == companyId && ci.ReferenceId == referenceId);
    }

    public async Task<bool> CompanyHasInterestInClassification(int companyId, int referenceClassificationId)
    {
        return await context.Set<CompanyInterest>()
            .AnyAsync(ci => ci.CompanyId == companyId && ci.ReferenceClassificationId == referenceClassificationId);
    }

    public async Task<IEnumerable<CompanyInterest>> GetForLoggingEmailRelevantEvent(int referenceClassificationId)
    {
        return await context.Set<CompanyInterest>()
            .Where(ci => ci.ReferenceClassificationId == referenceClassificationId)
            .ToListAsync();
    }

    public async Task<IEnumerable<CompanyInterest>> GetForSendingDailyEmail(int companyId)
    {
        return await context.Set<CompanyInterest>()
            .Include(ci => ci.EmailRelevantEvents)
                .ThenInclude(eme => eme.ClassificationCategory)
            .Where(ci =>
                // for this company
                ci.CompanyId == companyId
                // has a new event
                && ci.EmailRelevantEvents.Any(eme => eme.EmailRelevantEventEmailStatusType == EmailRelevantEventEmailStatusType.New))
            .ToListAsync();
    }

    public void ClearChangeTracker()
    {
        context.ChangeTracker.Clear();
    }
}
