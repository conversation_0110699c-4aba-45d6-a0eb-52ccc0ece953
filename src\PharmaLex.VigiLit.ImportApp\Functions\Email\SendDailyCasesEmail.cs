using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.ImportApp.Functions.Email;

public class SendDailyCasesEmail
{
    private readonly ICaseFilesEmailService _caseFilesEmailService;

    public SendDailyCasesEmail(ICaseFilesEmailService caseFilesEmailService)
    {
        _caseFilesEmailService = caseFilesEmailService;
    }

    [Transaction]
    [Function("Email_SendDailyCasesEmail")]
    public async Task Run([TimerTrigger("0 00 22 * * Sun-Thu")] TimerInfo timer, ILogger log)
    {
        await _caseFilesEmailService.Send(EmailTriggerType.Auto);
    }
}