﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;

namespace PharmaLex.Core.HealthCheck;

public class FeatureManagerHealthCheck : IHealthCheck
{
    private readonly IFeatureManager _featureManager;

    private readonly string _featureName;
    private readonly ILogger<FeatureManagerHealthCheck> _logger;

    public FeatureManagerHealthCheck(string featureName, ILoggerFactory loggerFactory, IFeatureManager featureManager)
    {
        _featureName = featureName;
        _featureManager = featureManager;
        _logger = loggerFactory.CreateLogger<FeatureManagerHealthCheck>();
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext? context, CancellationToken cancellationToken = default)
    {
        bool isEnabled;
        try
        {
            isEnabled = await _featureManager.IsEnabledAsync(_featureName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while validating feature management health.");
            isEnabled = false;
        }

        return new HealthCheckResult(isEnabled ? HealthStatus.Healthy : HealthStatus.Unhealthy, $"{isEnabled}");
    }
}