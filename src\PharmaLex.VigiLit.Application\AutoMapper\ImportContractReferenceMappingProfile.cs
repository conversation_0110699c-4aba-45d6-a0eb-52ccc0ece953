﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class ImportContractReferenceMappingProfile : Profile
{
    public ImportContractReferenceMappingProfile()
    {
        CreateMap<ImportContractReferenceClassification, DashboardDetailsRowModel>()
            .ForMember(i => i.CompanyName, s => s.MapFrom(c => c.ImportContract.Contract.Project.Company.Name))
            .ForMember(i => i.ProjectName, s => s.MapFrom(c => c.ImportContract.Contract.Project.Name))
            .ForMember(i => i.SubstanceName, s => s.MapFrom(c => c.ImportContract.Contract.Substance.Name))
            .ForMember(i => i.SourceModificationDate, s => s.MapFrom(c => c.ImportContract.PubMedModificationDate.HasValue ? c.ImportContract.PubMedModificationDate.Value.ToString("dd MMM yyyy") : string.Empty))
            .ForMember(i => i.ICRCType, s => s.MapFrom(c => c.ICRCType.GetDescription()))
            .ForMember(i => i.ReferenceClassificationId, s => s.MapFrom(c => c.ReferenceClassification.Id))
            .ForMember(i => i.SourceId, s => s.MapFrom(c => c.ReferenceClassification.Reference.SourceId))
            .ForMember(i => i.ClassificationStatus, s => s.MapFrom(c => c.ReferenceClassification.ReferenceState.ToString()))
            .ForMember(i => i.ContractId, s => s.MapFrom(c => c.ImportContract.ContractId))
            .ForMember(i => i.CompanyId, s => s.MapFrom(c => c.ImportContract.Contract.Project.CompanyId));
    }
}
