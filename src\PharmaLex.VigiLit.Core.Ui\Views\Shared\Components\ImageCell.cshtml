﻿<script type="text/javascript">
    vueApp.component('image-cell', {
        template: '<td><div v-html="cellContents"></div></td>',
        props: ['val'],
        computed: {
            cellContents() {
                let background = "";
                if (this.val && this.val.toUpperCase() == 'BOUNCE') {
                    background = "email-bounce-background";
                }
                else if (this.val && this.val.toUpperCase() == 'BLOCK') {
                    background = "email-block-background";
                }
                else if (this.val && this.val.toUpperCase() == 'SPAM') {
                    background = "email-spam-background";
                }
                return this.val ? `<span class='emailSuppressTableText ${background}'>${this.val}</span>` : "";
            }
        }
    });
</script>