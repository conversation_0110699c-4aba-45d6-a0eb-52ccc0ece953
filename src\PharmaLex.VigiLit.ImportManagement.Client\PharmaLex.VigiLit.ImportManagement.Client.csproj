﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Contracts\PharmaLex.VigiLit.ImportManagement.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ReferenceManagement\PharmaLex.VigiLit.ReferenceManagement.csproj" />
  </ItemGroup>

</Project>
