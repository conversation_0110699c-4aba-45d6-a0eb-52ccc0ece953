﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserManagement.Mappers;
using PharmaLex.Core.UserManagement.Users;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserManagement;

public static class ConfigureServices
{
    /// <summary>
    ///     Add user services to the services collection.
    /// </summary>
    /// <remarks>
    ///     This method should be called on the services collection in the startup.
    ///     It registers everything required to use the user management services including:
    ///     - User Search
    /// </remarks>
    /// <typeparam name="TUserEntity"></typeparam>
    /// <typeparam name="TUserRepository"></typeparam>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <exception cref="Exception"></exception>
    public static void AddUserServices<TUserEntity, TUserRepository>(this IServiceCollection services,
        IConfiguration configuration)
        where TUserEntity : EntityBase, IUserEntity
        where TUserRepository : class, IUserRepository<TUserEntity>
    {
        services.AddAutoMapper(cfg =>
        {
            var generic = typeof(UserEntityMapper<>);
            var mapType = generic.MakeGenericType(typeof(TUserEntity));

            if (Activator.CreateInstance(mapType) is not Profile mapperInstance)
                throw new TypeInitializationException(
                    $"Failed to create instance of mapper for type:{typeof(TUserEntity)}", null);

            cfg.AddProfile(mapperInstance);
            cfg.AddProfile<GraphUserMapper>();
        });

        // These should probably really be in the B2C nuget package registration
        services.Configure<AzureAdGraphOptions>(configuration.GetSection("AzureAdGraph"));
        services.AddTransient<IGraphClientProvider<AzureAdGraphOptions>, AzureAdGraphClientProvider>();
        services.AddScoped<IAzureAdGraphService, AzureAdGraphService>();


        services.AddScoped<IAzureAdGraphFindHelper, AzureAdGraphFindHelper<TUserEntity>>();
        services.AddScoped<IUserSearchService<TUserEntity>, UserSearchService<TUserEntity>>();
        services.AddScoped<IUserRepository<TUserEntity>, TUserRepository>();
    }
}