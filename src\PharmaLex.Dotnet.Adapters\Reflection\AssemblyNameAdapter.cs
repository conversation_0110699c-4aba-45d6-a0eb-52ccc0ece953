using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

[System.Diagnostics.DebuggerDisplay("{FullName}")]
public class AssemblyNameAdapter : IAssemblyName
{
    private AssemblyName _assemblyName;

    public AssemblyNameAdapter(AssemblyName assemblyName)
    {
        _assemblyName = assemblyName;
    }

    public string FullName => _assemblyName.FullName;

    public string Name => _assemblyName.Name;

    public Version Version => _assemblyName.Version;
}