﻿using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models.Document.Case;

namespace PharmaLex.VigiLit.Infrastructure.Document.Case;

internal class CaseDocumentService : ICaseDocumentService
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageCaseDocumentOptions _caseDocumentOptions;

    public CaseDocumentService(
        IDocumentService documentService,
        IOptions<AzureStorageCaseDocumentOptions> caseDocumentOptions)
    {
        _documentService = documentService;
        _caseDocumentOptions = caseDocumentOptions.Value;
    }

    public async Task<bool> Exists(CaseDocumentDescriptor caseDocumentDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(caseDocumentDescriptor);
        var exists = await _documentService.Exists(documentDescriptor, cancellationToken);
        return exists;
    }

    public async Task<Stream> OpenRead(CaseDocumentDescriptor caseDocumentDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(caseDocumentDescriptor);
        var stream = await _documentService.OpenRead(documentDescriptor, cancellationToken);
        return stream;
    }

    public async Task Delete(CaseDocumentDescriptor caseDocumentDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptor = GetDocumentDescriptor(caseDocumentDescriptor);
        await _documentService.Delete(documentDescriptor, cancellationToken);
    }

    private DocumentDescriptor GetDocumentDescriptor(CaseDocumentDescriptor caseDocumentDescriptor)
    {
        var blobName = caseDocumentDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_caseDocumentOptions.AccountName, _caseDocumentOptions.ContainerName, blobName);
        return documentDescriptor;
    }
}