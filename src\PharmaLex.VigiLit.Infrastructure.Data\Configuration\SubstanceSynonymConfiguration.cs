﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class SubstanceSynonymConfiguration : EntityBaseMap<SubstanceSynonym>
{
    public override void Configure(EntityTypeBuilder<SubstanceSynonym> builder)
    {
        base.Configure(builder);

        builder.ToTable("SubstanceSynonyms");

        builder.HasIndex(c => new { c.SubstanceId, c.Name });
    }
}