﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;

#pragma warning disable S4487


namespace PharmaLex.VigiLit.ImportManagement.Ui.FileImport;
public class ImportFileRepository : TrackingGenericRepository<ImportFile>, IImportFileRepository
{

    protected readonly IMapper _mapper;
    private readonly ILogger<ImportFileRepository> _logger;

    public ImportFileRepository(PlxDbContext context, IMapper mapper, IUserContext userContext, ILoggerFactory loggerFactory) : base(context, userContext.User)
    {
        _mapper = mapper;
        _logger = loggerFactory.CreateLogger<ImportFileRepository>();
    }

    public async Task<IEnumerable<ImportFile>> GetImportFilesByBatchId(Guid batchId)
    {
        return await context.Set<ImportFile>()
           .Where(i => i.BatchId == batchId)
            .OrderByDescending(i => i.Id)
             .AsNoTracking().ToListAsync();

    }

    public async Task<IEnumerable<ImportFile>> GetAll()
    {
        return await context.Set<ImportFile>()
            .AsNoTracking().ToListAsync();
    }

    public async Task<ImportFile> GetImportFile(string fileName, Guid batchId)
    {
        var importFile = await context.Set<ImportFile>()
       .FirstOrDefaultAsync(i => i.FileName == fileName && i.BatchId == batchId);

        return importFile ?? throw new KeyNotFoundException($"No import file found with FileName '{fileName}' and BatchId '{batchId}'.");
    }
}


#pragma warning restore S4487