-- =============================================
-- Author:      <PERSON>
-- Create date: 2023-08-22
-- Description: Creates an external company user.
-- =============================================

DECLARE @CompanyName nvarchar(250) = 'My Company' 		-- name of existing company to add user to
DECLARE @FirstName nvarchar(512) = 'Mike' 				-- your user's first name 
DECLARE @LastName nvarchar(512) = 'WTest' 				-- your user's last name 
DECLARE @Email nvarchar(256) = '<EMAIL>' -- your user's email address


-- DO NOT EDIT BELOW THIS LINE --
	
BEGIN TRAN

	DECLARE @UserId int
	DECLARE @ClaimId int
	DECLARE @CompanyId int
	
	SELECT @ClaimId = [Id] from [Claims] WHERE [Name] = 'ClientResearcher'
	
	SELECT @CompanyId = [Id] from [Companies] WHERE [Name] = @CompanyName
	
	-- create user
	INSERT INTO [Users] ([Email], [GivenName], [FamilyName], [LastLoginDate], [ActivationExpiryDate], [InvitationEmailLink], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
			VALUES (@Email, @FirstName, @LastName, NULL, GETUTCDATE(), 'https://localhost:5001', GETUTCDATE(), 'script', GETUTCDATE(), 'script')

	SET @UserId = SCOPE_IDENTITY()

	-- create claims
	INSERT INTO [UserClaims] ([ClaimsInternalId], [UsersInternalId])
		VALUES (@ClaimId, @UserId)
		
	-- create email preferences
	INSERT INTO [UserEmailPreferences] ([UserId], [EmailPreferenceId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
		VALUES (@UserId, 1, GETUTCDATE(), 'script', GETUTCDATE(), 'script')
	INSERT INTO [UserEmailPreferences] ([UserId], [EmailPreferenceId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
		VALUES (@UserId, 2, GETUTCDATE(), 'script', GETUTCDATE(), 'script')
		
	-- create company user 
	INSERT INTO [CompanyUsers] ([CompanyId], [UserId], [Active], [CompanyUserType], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
		VALUES (@CompanyId, @UserId, 1, 2, GETUTCDATE(), 'script', GETUTCDATE(), 'script')
	
COMMIT TRAN
