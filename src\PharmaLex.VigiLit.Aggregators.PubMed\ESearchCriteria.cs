﻿using PharmaLex.VigiLit.Core.Aggregator;

namespace PharmaLex.VigiLit.Aggregators.PubMed;

/// <summary>
/// PubMed implementation of ISearchCriteria
/// </summary>
/// <seealso cref="PharmaLex.VigiLit.Core.Aggregator.ISearchCriteria" />
public class ESearchCriteria : ISearchCriteria
{
    /// <summary>
    /// Gets the search term.
    /// </summary>
    /// <value>
    /// The term.
    /// </value>
    public required string Term { get; init; }

    /// <summary>
    /// Gets or sets the modified date that is used in the PubMed query string.
    /// </summary>
    /// <value>
    /// The modified date.
    /// </value>
    public DateTime? ModifiedDate { get; set; }

    /// <summary>
    /// Gets or sets the create date from that is used in the PubMed query string.
    /// </summary>
    /// <value>
    /// The create date from.
    /// </value>
    public DateTime? CreateDateFrom { get; set; }
}
