using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;

internal class ImportContractRepository : TrackingGenericRepository<ImportContract>, IImportingImportContractRepository
{
    protected readonly IMapper _mapper;

    public ImportContractRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<ImportContract?> GetByIdForImport(int id)
    {
        return await context.Set<ImportContract>()
            .Include(ic => ic.Import)
            .Include(ic => ic.ImportContractReferenceClassifications) // need to avoid duplicate allocation
                .ThenInclude(icrc => icrc.ReferenceClassification) // needed to tell what a duplicate allocation is
            .Include(ic => ic.Contract)
                .ThenInclude(c => c.ContractVersionsInternal) // need to find search term
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
    }

    public void ClearChangeTracker()
    {
        context.ChangeTracker.Clear();
    }
}
