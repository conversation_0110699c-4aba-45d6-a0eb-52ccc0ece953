﻿using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface IEmailMaintenanceService
{
    Task UpdateSuppressions();

    /// <summary>
    /// Returns list of bounced email addresses with reason for bounce.
    /// Documentation:   https://docs.sendgrid.com/api-reference/bounces-api/retrieve-all-bounces
    /// </summary>
    Task<IEnumerable<SendGridSuppressionResponse>> GetBounceList(DateTime? startDate);

    /// <summary>
    /// Returns list of blocked email addresses with reason for block.
    /// Documentation:   https://docs.sendgrid.com/api-reference/blocks-api/retrieve-all-blocks
    /// </summary>
    Task<IEnumerable<SendGridSuppressionResponse>> GetBlockList(DateTime? startDate);

    /// <summary>
    /// Returns list of email addresses that have marked us as spam.
    /// Documentation:   https://docs.sendgrid.com/api-reference/spam-reports-api/retrieve-all-spam-reports
    /// </summary>
    Task<IEnumerable<SendGridSuppressionResponse>> GetSpamList(DateTime? startDate);

    /// <summary>
    /// Deletes given user from SendGrid block list
    /// https://docs.sendgrid.com/api-reference/blocks-api/retrieve-a-specific-block
    /// </summary>
    Task<bool> DeleteBlock(string email);

    /// <summary>
    /// Deletes given user from SendGrid bounce list
    /// https://docs.sendgrid.com/api-reference/bounces-api/delete-a-bounce
    /// </summary>
    Task<bool> DeleteBounce(string email);
    Task<EmailSuppressionModel> GetUserSuppression(int userId);
}
