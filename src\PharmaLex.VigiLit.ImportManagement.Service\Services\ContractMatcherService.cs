﻿using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;
using PharmaLex.VigiLit.AiAnalysis.Client;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Service.Matching;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.MessageBroker.Contracts;
using PharmaLex.VigiLit.ReferenceManagement;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;

internal class ContractMatcherService : IContractMatcherService
{
    private readonly IImportingContractRepository _importingContractRepository;
    private readonly IReferenceMatcher _referenceMatcher;
    private readonly IFeatureManager _featureManager;
    private readonly ISubstanceRepository _substanceRepository;
    private readonly IAiReferencePublisher _aiReferencePublisher;
    private const string AiMessageBusSend = "AiMessageBusSend";

    public ContractMatcherService(IImportingContractRepository importingContractRepository,
        IReferenceMatcher referenceMatcher, 
        IFeatureManager featureManager,
        ISubstanceRepository substanceRepository, 
        ILogger<ContractMatcherService> logger, 
        IAiReferencePublisher aiReferencePublisher)
    {
        _importingContractRepository = importingContractRepository;
        _referenceMatcher = referenceMatcher;
        _substanceRepository = substanceRepository;
        _featureManager = featureManager;
        _aiReferencePublisher = aiReferencePublisher;
    }

    public async Task<List<ContractVersion>> FindMatchingContractVersions(IReference reference)
    {
        var activeContractVersions = await _importingContractRepository.GetContractsToMatchOn();
        var contractVersions = new List<ContractVersion>();

        foreach (var contractVersion in activeContractVersions)
        {
            var journalTitles = contractVersion.ContractVersionJournals
                .Where(x => x.Journal.Enabled)
                .Select(x => x.Journal.Name)
                .ToList();
            if (await _referenceMatcher.Matches(reference, contractVersion.SearchString, journalTitles))
            {
                contractVersions!.Add(contractVersion);
            }
        }

        return contractVersions;
    }

    public async Task SendToAi(Reference reference, IList<ImportContract> importContracts) // Shouldn't be here
    {
        if (await _featureManager.IsEnabledAsync(AiMessageBusSend))
        {
            foreach (var importContract in importContracts)
            {
                var substance = await _substanceRepository.Get(importContract.Contract.SubstanceId);
                var sourceSystem = Convert.ToString(reference.SourceSystem);
                var synonyms = substance.SubstanceSynonyms.Select(x => x.Name).ToList();
                var referenceData = new PreClassifyReferenceCommand(reference.Title, reference.Abstract,
                    reference.SourceId,
                    sourceSystem, substance.Name, synonyms);
                await _aiReferencePublisher.Send(referenceData);
            }
        }
    }
}