﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Service\PharmaLex.VigiLit.ImportManagement.Service.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.MessageBroker.Contracts\PharmaLex.VigiLit.MessageBroker.Contracts.csproj" />
  </ItemGroup>
</Project>
