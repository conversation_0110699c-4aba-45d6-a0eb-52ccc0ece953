#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.

FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
USER app
WORKDIR /app

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/PharmaLex.VigiLit.AiAnalysis.Service/PharmaLex.VigiLit.AiAnalysis.Service.csproj", "src/PharmaLex.VigiLit.AiAnalysis.Service/"]
COPY ["src/PharmaLex.VigiLit.DataAccessLayer/PharmaLex.VigiLit.DataAccessLayer.csproj", "src/PharmaLex.VigiLit.DataAccessLayer/"]
COPY ["src/PharmaLex.VigiLit.AiAnalysis.Entities/PharmaLex.VigiLit.AiAnalysis.Entities.csproj", "src/PharmaLex.VigiLit.AiAnalysis.Entities/"]
COPY ["src/PharmaLex.VigiLit.MessageBroker.Contracts/PharmaLex.VigiLit.MessageBroker.Contracts.csproj", "src/PharmaLex.VigiLit.MessageBroker.Contracts/"]
RUN dotnet restore "./src/PharmaLex.VigiLit.AiAnalysis.Service/PharmaLex.VigiLit.AiAnalysis.Service.csproj"

WORKDIR "/src/src/PharmaLex.VigiLit.AiAnalysis.Service"
RUN dotnet build "./PharmaLex.VigiLit.AiAnalysis.Service.csproj" -c "$BUILD_CONFIGURATION" -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./PharmaLex.VigiLit.AiAnalysis.Service.csproj" \
	-c "$BUILD_CONFIGURATION" \
	-o /app/publish \
	/p:UseAppHost=false

FROM base AS final
WORKDIR /app
RUN net user /add nonroot
USER nonroot
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "PharmaLex.VigiLit.AiAnalysis.Service.dll"]