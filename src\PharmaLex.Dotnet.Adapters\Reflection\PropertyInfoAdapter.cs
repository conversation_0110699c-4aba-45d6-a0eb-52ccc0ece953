using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

public class PropertyInfoAdapter : MemberInfoAdapter, IPropertyInfo
{
    readonly PropertyInfo _propertyInfo;

    public PropertyInfoAdapter(PropertyInfo propertyInfo) : base((MemberInfo)propertyInfo)
    {
        this._propertyInfo = propertyInfo;
    }

    public bool CanRead => _propertyInfo.CanRead;

    public bool CanWrite => _propertyInfo.CanWrite;

    public bool IsSpecialName => _propertyInfo.IsSpecialName;

    public IType PropertyType => new TypeAdapter(_propertyInfo.PropertyType);

    public IMethodInfo GetGetMethod()
    {
        return GetGetMethod(false);
    }

    public IMethodInfo GetGetMethod(bool nonPublic)
    {
        var mi = _propertyInfo.GetGetMethod(nonPublic);
        if (mi != null)
        {
            return new MethodInfoAdapter(mi);
        }

        return null;
    }

    public IParameterInfo[] GetIndexParameters()
    {
        var parameters = _propertyInfo.GetIndexParameters();
        var parameterInfoAdapters = new IParameterInfo[parameters.Length];

        for (int i = 0; i < parameters.Length; i++)
        {
            parameterInfoAdapters[i] = new ParameterInfoAdapter(parameters[i]);
        }

        return parameterInfoAdapters;
    }

    public IMethodInfo GetSetMethod()
    {
        return GetSetMethod(false);
    }

    public IMethodInfo GetSetMethod(bool nonPublic)
    {
        var mi = _propertyInfo.GetSetMethod(nonPublic);
        if (mi != null)
        {
            return new MethodInfoAdapter(mi);
        }

        return null;
    }

    public void SetValue(object instance, object value, object[] index)
    {
        _propertyInfo.SetValue(instance, value, index);
    }
}