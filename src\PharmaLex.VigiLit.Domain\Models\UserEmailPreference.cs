﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Domain.Models;

public class UserEmailPreference : VigiLitEntityBase
{
    public int UserId { get; set; }
    public User User { get; set; }

    public int EmailPreferenceId { get; set; }
    public EmailPreference EmailPreference { get; set; }

    public UserEmailPreference(int userId, int emailPreferenceId)
    {
        UserId = userId;
        EmailPreferenceId = emailPreferenceId;
    }
    public UserEmailPreference() { }
}