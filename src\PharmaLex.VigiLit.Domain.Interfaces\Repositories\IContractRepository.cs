using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface IContractRepository : ITrackingRepository<Contract>
{
    Task<IEnumerable<Contract>> GetAllAsync();
    Task<Contract?> GetContractDetails(int id);
    Task<Contract?> GetContractDetailsForAdminUsers(int id);
    Task<IEnumerable<Contract>> GetAllToExportAsync();
    Task<IEnumerable<Contract>> GetForAdHocImportCreate(int companyId, int projectId = 0);
    Task<Contract?> GetByIdAsync(int id);
    Task<IEnumerable<Contract>> GetContractsForScheduledImport(DateTime importDate);
    Task<Contract?> GetContractWithLatestVersion(int contractId);
    Task<bool> DoesContractBelongToCompany(int companyId, int contractId);
}
