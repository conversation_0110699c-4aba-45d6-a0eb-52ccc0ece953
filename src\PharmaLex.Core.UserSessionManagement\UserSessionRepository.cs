﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.Core.UserSessionManagement.Entities;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserSessionManagement;

public class UserSessionRepository : TrackingGenericRepository<UserSession>, IUserSessionRepository
{
    public UserSessionRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<UserSession?> GetByUserIdAsync(int userId)
    {
        return await context.Set<UserSession>()
                            .Include(s => s.User)
                            .Where(x => x.UserId == userId)
                            .OrderByDescending(x => x.Id)
                            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<UserSession>> GetAllByUserIdAsync(int userId)
    {
        return await context.Set<UserSession>()
                            .Include(s => s.User)
                            .Where(x => x.UserId == userId)
                            .ToListAsync();
    }

    public async Task<IEnumerable<UserSession>> GetSessionsForUsersAsync(List<int> userIds)
    {
        return await context.Set<UserSession>()
                            .Where(x => userIds.Contains(x.UserId))
                            .AsNoTracking()
                            .ToListAsync();
    }
}