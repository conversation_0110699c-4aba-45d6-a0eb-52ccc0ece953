using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public class ImportRepository : TrackingGenericRepository<Import>, IImportRepository
{
    protected readonly IMapper _mapper;

    public ImportRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<Import?> GetById(int id)
    {
        return await context.Set<Import>()
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<Import?> GetByIdForImport(int id)
    {
        return await context.Set<Import>()
            .Include(i => i.ImportContracts)
            .Where(i => i.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<ImportModel>> GetImportLog(int days)
    {
        var query = context.Set<Import>()
            .Include(i => i.ImportContracts)
            .Where(i => i.CreatedDate >= DateTime.UtcNow.AddDays(days * -1))
            .OrderByDescending(i => i.Id)
            .AsNoTracking();

        return await _mapper.ProjectTo<ImportModel>(query).ToListAsync();
    }

    public async Task<IEnumerable<ImportDashboardModel>> GetImportsForDashboard()
    {
        var query = context.Set<Import>()
            .Where(i => DashboardStatusTypeGroups.Active.Contains(i.ImportDashboardStatusType))
            .OrderByDescending(i => i.Id)
            .AsNoTracking();

        var imports = await _mapper.ProjectTo<ImportDashboardModel>(query).ToListAsync();

        var counts = await context.Set<ImportDashboardCountResult>()
            .FromSqlRaw(@"  SELECT Imports.Id As [ImportId], COUNT(DISTINCT(ReferenceClassificationId)) As [TodoCount], 
                                GETUTCDATE() As [CreatedDate], GETUTCDATE() As [LastUpdatedDate], '' As [CreatedBy], '' As [LastUpdatedBy] 
                            FROM Imports 
                            INNER JOIN ImportContracts ON ImportContracts.ImportId = Imports.Id 
                            INNER JOIN ImportContractReferenceClassifications ON ImportContractReferenceClassifications.ImportContractId = ImportContracts.Id 
                            INNER JOIN ReferenceClassifications ON ReferenceClassifications.Id = ImportContractReferenceClassifications.ReferenceClassificationId
                            WHERE ImportDashboardStatusType = 10
                            AND (
	                            (ReferenceState = 0) 
	                            OR
	                            (EXISTS (SELECT * FROM ReferenceUpdates WHERE ReferenceUpdates.ReferenceId = ReferenceClassifications.ReferenceId AND ReferenceUpdates.SubstanceId = ReferenceClassifications.SubstanceId))
                            )
                            GROUP BY Imports.Id 
                            ORDER BY Imports.Id DESC    ")
            .ToDictionaryAsync(r => r.ImportId);

        foreach (var import in imports)
        {
            if (counts.TryGetValue(import.Id, out var count))
            {
                import.TodoCount = count.TodoCount;
            }
        }

        return imports;
    }

    public async Task<ImportDashboardModel?> GetImportForDashboardDetails(int importId)
    {
        var query = context.Set<Import>()
            .Where(i => i.Id == importId)
            .AsNoTracking();

        return await _mapper.ProjectTo<ImportDashboardModel>(query).FirstOrDefaultAsync();
    }

    public async Task Archive(int importId)
    {
        var import = await context.Set<Import>()
            .Where(i => i.Id == importId)
            .FirstAsync();

        import.ImportDashboardStatusType = ImportDashboardStatusType.Archived;

        await context.SaveChangesAsync();
    }
}
