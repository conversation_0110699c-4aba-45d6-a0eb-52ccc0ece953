﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;

internal class ReferenceClassificationRepository : TrackingGenericRepository<ReferenceClassification>,
    IReferenceClassificationRepository
{
    public ReferenceClassificationRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }
}
