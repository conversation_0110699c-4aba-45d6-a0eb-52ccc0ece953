using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.Domain.Models;


namespace PharmaLex.VigiLit.DataExtraction.Service.Repositories;

internal class JournalRepository : TrackingGenericRepository<Journal>, IJournalRepository
{
    public JournalRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<string>> GetNames()
    {
        return await context.Set<Journal>()
            .Where(x=>x.Enabled)
            .Select(x=>x.Name)
            .AsNoTracking()
            .ToListAsync();
    }
}