﻿using System;
using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Application;

public static class DayOfWeekExtensions
{
    public static AutoImportDay ToAutoImportDay(this DayOfWeek dayOfWeek)
    {
        return dayOfWeek switch
        {
            DayOfWeek.Monday => AutoImportDay.Monday,
            DayOfWeek.Tuesday => AutoImportDay.Tuesday,
            DayOfWeek.Wednesday => AutoImportDay.Wednesday,
            DayOfWeek.Thursday => AutoImportDay.Thursday,
            DayOfWeek.Friday => AutoImportDay.Friday,
            _ => throw new NotSupportedException($"{dayOfWeek} not supported by the import")
        };
    }
}
