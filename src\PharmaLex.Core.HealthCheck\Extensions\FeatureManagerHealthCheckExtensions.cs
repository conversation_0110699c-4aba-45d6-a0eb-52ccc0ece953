﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.FeatureManagement;

namespace PharmaLex.Core.HealthCheck.Extensions;

public static class FeatureManagerHealthCheckExtensions
{
    public static IHealthChecksBuilder AddFeatureManagerHealthCheck(
        this IHealthChecksBuilder builder,
        string name,
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default)
    {
        return builder.Add(new HealthCheckRegistration(
            name,
            sp => new FeatureManagerHealthCheck(name, 
                sp.GetRequiredService<ILoggerFactory>(),
                sp.GetRequiredService<IFeatureManager>()),
            failureStatus,
            tags));
    }
}