using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

public class MemberInfoAdapter : IMemberInfo
{
    readonly MemberInfo _memberInfo;

    public MemberInfoAdapter(MemberInfo memberInfo)
    {
        _memberInfo = memberInfo;
    }

    public string Name => _memberInfo.Name;

    public IType DeclaringType => new TypeAdapter(_memberInfo.DeclaringType);

    public object[] GetCustomAttributes(bool inherit)
    {
        return _memberInfo.GetCustomAttributes(inherit);
    }

    public object[] GetCustomAttributes(Type attributeType, bool inherit)
    {
        return _memberInfo.GetCustomAttributes(attributeType, inherit);
    }

    public bool IsDefined(Type attributeType, bool inherit)
    {
        return _memberInfo.IsDefined(attributeType, inherit);
    }
}