﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.267" />
    <PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Ui.ViewModels\PharmaLex.VigiLit.Ui.ViewModels.csproj" />
  </ItemGroup>

</Project>
