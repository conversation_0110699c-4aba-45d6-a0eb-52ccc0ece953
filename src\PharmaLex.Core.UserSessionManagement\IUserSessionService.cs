﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using PharmaLex.Core.UserSessionManagement.Entities;

namespace PharmaLex.Core.UserSessionManagement;

public interface IUserSessionService
{
    Task InitialiseUserSession(int userId, string ipAddress);

    public bool IsValidSession(UserSession userSession, string sessionId, RouteValueDictionary routes);

    /// <summary>
    /// Processes incoming session data from the Http context and determines whether the session is valid
    /// </summary>
    /// <param name="context">Incoming context</param>
    /// <param name="userId">Identifier of user in Users table</param>
    /// <param name="sessionLengthSeconds"></param>
    /// <exception cref="InvalidOperationException">
    /// Thrown when session breaks any of the rules that determine whether the session is valid
    /// </exception>
    /// <returns>UserSession</returns>
    Task<UserSession?> ProcessIncomingSession(ActionExecutingContext context, int userId, int sessionLengthSeconds);

    /// <summary>
    /// Deletes a user session from UserSession table for given user
    /// </summary>
    /// <param name="userId">Identifier of user</param>
    /// <returns>Task</returns>
    Task DeleteUserSessionAsync(int userId);

    /// <summary>
    /// Deletes user session from UserSession table for given list of users
    /// </summary>
    /// <param name="userIds">List of user ids</param>
    /// <returns>Task</returns>
    Task DeleteUserSessionsAsync(List<int> userIds);

    /// <summary>
    /// Reads whether valid user session is active for user
    /// </summary>
    /// <param name="context">Current HttpContext</param>
    /// <param name="userId">Identifier of user</param>
    /// <returns>True/false indicating whether session is valid</returns>
    Task<bool> ProcessTimerCall(HttpContext context, int userId);
}