﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.DataExtraction.Entities;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class MdeQueueItemConfiguration : EntityBaseMap<MdeQueueItem>
{
    public override void Configure(EntityTypeBuilder<MdeQueueItem> builder)
    {
        base.Configure(builder);

        builder.ToTable("MdeQueueItem");

        builder.Property(e => e.Status).IsRequired();
        builder.Property(e => e.BatchId).IsRequired();
        builder.Property(e => e.DocumentLocation).IsRequired();
        builder.Property(e => e.Filename).IsRequired();
    }
}