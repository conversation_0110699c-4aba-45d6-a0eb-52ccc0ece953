﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Ui.ViewModels.Substance;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class AutoImportDayMappingProfile : Profile
{
    public AutoImportDayMappingProfile()
    {
        CreateMap<AutoImportDay, AutoImportDayModel>()
            .ForMember(d => d.Id, s => s.MapFrom(x => (int)x))
            .ForMember(d => d.Name, s => s.MapFrom(x => x.GetDescription()));
    }
}