using System.Collections.Generic;
using System.Threading.Tasks;
using PharmaLex.Caching.Data;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.Services;

public class ClassificationCategoryService : IClassificationCategoryService
{
    private readonly IDistributedCacheServiceFactory _cacheServiceFactory;

    public ClassificationCategoryService(IDistributedCacheServiceFactory cacheServiceFactory)
    {
        _cacheServiceFactory = cacheServiceFactory;
    }

    public async Task<IEnumerable<ClassificationCategoryModel>> GetAllAsync()
    {
        var classificationCategoryCache = _cacheServiceFactory.CreateMappedEntity<ClassificationCategory, ClassificationCategoryModel>();
        var classificationCategory = await classificationCategoryCache.AllAsync();

        return classificationCategory;
    }

    public async Task<ClassificationCategoryModel> GetByIdAsync(int id)
    {
        var classificationCategoryCache = _cacheServiceFactory.CreateMappedEntity<ClassificationCategory, ClassificationCategoryModel>();
        var classificationCategory = await classificationCategoryCache.FirstOrDefaultAsync(m => m.Id == id);

        return classificationCategory;
    }
}