using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ContractVersionConfiguration : EntityBaseMap<ContractVersion>
{
    public override void Configure(EntityTypeBuilder<ContractVersion> builder)
    {
        base.Configure(builder);

        builder.ToTable("ContractVersions");

        builder.Property(e => e.ReasonForChange).HasColumnType("nvarchar").HasMaxLength(256);

        builder.HasIndex(cv => new { cv.ContractId, cv.ContractVersionStatus, cv.IsActive, cv.ContractType, cv.ContractWeekday });

        builder.Property(e => e.SearchString)
            .IsRequired(false)
            .HasMaxLength(4000);
    }
}