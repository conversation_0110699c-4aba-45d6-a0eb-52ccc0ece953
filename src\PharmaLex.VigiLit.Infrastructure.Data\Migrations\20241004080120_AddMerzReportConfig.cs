﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.VigiLit.Infrastructure.Data.Extensions;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddMerzReportConfig : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.SqlFileExec("spMerzClassificationReport.sql");
            migrationBuilder.Sql(@"
                        DECLARE @companyName1 varchar(20) = 'Merz Therapeutics';
                        DECLARE @companyId1 int;
                        DECLARE @reportDescription1 varchar(25) = 'Classification reports';
                        DECLARE @reportId1 int;
                        DECLARE @superAdminClaimId1 int;
                        DECLARE @internalSupportClaimId1 int;
                        DECLARE @clientResearcherClaimId1 int;

                        -- Company
                        IF NOT EXISTS(SELECT * FROM [dbo].[Companies] WHERE Name = @companyName1)
                        BEGIN
	                        INSERT INTO [dbo].[Companies]
	                        ([Name],[ContactPersonName],[ContactPersonEmail],[IsActive],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	                        VALUES	(@companyName1,'','',1,GetUTCdate(),'Script',GetUTCdate(),'Script')
                        END

                        SELECT @companyId1=Id FROM [dbo].[Companies] WHERE Name = @companyName1

                        -- Reports
                        IF NOT EXISTS(SELECT * FROM [dbo].[Reports] WHERE Name = @companyName1 AND [Description] = @reportDescription1)
                        BEGIN
	                        INSERT INTO [dbo].[Reports]	([Name],[Description],[AllCompanies],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy],[ControllerName])
	                        VALUES (@companyName1,@reportDescription1,0,GetUTCdate(),'Script',GetUTCdate(),'Script','MerzClientReport')
                        END

                        SELECT @reportId1=Id FROM [dbo].[Reports] WHERE Name = @companyName1 AND [Description] = @reportDescription1

                        -- Report claims
                        SELECT @superAdminClaimId1=Id from [dbo].[Claims] WHERE Name = 'SuperAdmin'
                        SELECT @internalSupportClaimId1=Id from [dbo].[Claims] WHERE Name = 'InternalSupport'
                        SELECT @clientResearcherClaimId1=Id from [dbo].[Claims] WHERE Name = 'ClientResearcher'

                        IF NOT EXISTS(SELECT * FROM [dbo].ReportClaims WHERE ReportId = @reportId1 AND ClaimId = @superAdminClaimId1)
                        BEGIN
	                        INSERT INTO [dbo].[ReportClaims]
	                        ([ReportId],[ClaimId],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	                        VALUES
	                        (@reportId1,@superAdminClaimId1,GetUTCdate(),'Script',GetUTCdate(),'Script')
                        END

                        IF NOT EXISTS(SELECT * FROM [dbo].ReportClaims WHERE ReportId = @reportId1 AND ClaimId = @internalSupportClaimId1)
                        BEGIN
	                        INSERT INTO [dbo].[ReportClaims]
	                        ([ReportId],[ClaimId],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	                        VALUES
	                        (@reportId1,@internalSupportClaimId1,GetUTCdate(),'Script',GetUTCdate(),'Script')
                        END

                        IF NOT EXISTS(SELECT * FROM [dbo].ReportClaims WHERE ReportId = @reportId1 AND ClaimId = @clientResearcherClaimId1)
                        BEGIN
	                        INSERT INTO [dbo].[ReportClaims]
	                        ([ReportId],[ClaimId],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	                        VALUES
	                        (@reportId1,@clientResearcherClaimId1,GetUTCdate(),'Script',GetUTCdate(),'Script')
                        END

                        -- Company reports
                        IF NOT EXISTS(SELECT * FROM [CompanyReports] WHERE CompanyId=@companyId1 AND ReportId = @reportId1)
                        BEGIN
	                        INSERT INTO [dbo].[CompanyReports] ([CompanyId],[ReportId],[CreatedDate],[CreatedBy],[LastUpdatedDate],[LastUpdatedBy])
	                        VALUES	(@companyId1,@reportId1,GetUtcdate(),'script',GetUtcdate(),'script')
                        END
                        ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // VigiLit does not go back in versions
        }
    }
}
