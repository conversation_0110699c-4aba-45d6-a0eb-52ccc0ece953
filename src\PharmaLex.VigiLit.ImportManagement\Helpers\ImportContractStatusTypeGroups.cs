﻿using System.Collections.Immutable;
using PharmaLex.VigiLit.ImportManagement.Enums;

namespace PharmaLex.VigiLit.ImportManagement.Helpers;

public static class ImportContractStatusTypeGroups
{
    public static readonly ImmutableList<ImportContractStatusType> Queued = ImmutableList.Create(
        ImportContractStatusType.Queued, 
        ImportContractStatusType.Started
    );

    public static readonly ImmutableList<ImportContractStatusType> Succeeded = ImmutableList.Create(
        ImportContractStatusType.Completed,
        ImportContractStatusType.CompletedCapped
    );

    public static readonly ImmutableList<ImportContractStatusType> Failed = ImmutableList.Create(
        ImportContractStatusType.Failed,
        ImportContractStatusType.SearchFailed,
        ImportContractStatusType.Timeout,
        ImportContractStatusType.InvalidSearchString
    );
}
