﻿namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

/// <summary>
/// PhlexVisionService sends request to and receives responses from the Phlex Vision API
/// </summary>
public interface IPhlexVisionService // Should be internal
{
    /// <summary>
    /// Requests the data extraction.
    /// </summary>
    /// <param name="extractRequest">The extract meta data request.</param>
    Task RequestDataExtraction(ExtractRequest extractRequest);
}

