﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.DataExtraction.Service.Repositories
{
    public class CountryRepository : TrackingGenericRepository<Country>, ICountryRepository
    {
        public CountryRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
        {


        }
        public async Task<IEnumerable<string>> GetNames()
        {
            return await context.Set<Country>()
                .Select(x => x.Name)
                .AsNoTracking()
                .ToListAsync();
        }
    }
}
