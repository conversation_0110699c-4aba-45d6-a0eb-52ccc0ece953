﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ContractVersionJournalConfiguration : EntityBaseMap<ContractVersionJournal>
{
    public override void Configure(EntityTypeBuilder<ContractVersionJournal> builder)
    {
        base.Configure(builder);
        builder.ToTable("ContractVersionJournals");

        builder.HasOne(cvj => cvj.ContractVersion).WithMany(c => c.ContractVersionJournals).HasForeignKey(cj => cj.ContractVersionId);

        builder.HasOne(cvj => cvj.Journal).WithMany(j => j.ContractVersionJournals).HasForeignKey(cj => cj.JournalId);
    }
}