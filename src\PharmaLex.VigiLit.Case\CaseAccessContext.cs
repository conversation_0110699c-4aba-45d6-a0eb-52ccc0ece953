﻿using PharmaLex.VigiLit.AccessControl;
using System.Security.Claims;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

namespace PharmaLex.VigiLit.Case;

public class CaseAccessContext : IAccessControlContext
{
    public CaseAccessContext(ClaimsPrincipal user, CaseModel caseModel)
    {
        User = user;
        CaseModel = caseModel;
    }
    public ClaimsPrincipal User { get; }
    public CaseModel CaseModel { get; }
}
