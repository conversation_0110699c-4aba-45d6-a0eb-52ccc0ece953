﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;
internal class ImportReferenceRepository : TrackingGenericRepository<ImportReference>, IImportReferenceRepository
{
    public ImportReferenceRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<ImportReference?> GetNextQueued()
    {
        return await context.Set<ImportReference>()
            .Where(x => x.StatusType == ImportReferenceStatusType.Queued)
            .FirstOrDefaultAsync();
    }
}
