﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.AiAnalysis.Entities;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;
public class AiSuggestionConfiguration : EntityBaseMap<AiSuggestion>
{
    public override void Configure(EntityTypeBuilder<AiSuggestion> builder)
    {
        base.Configure(builder);

        builder.ToTable("AISuggestions");

        builder.Property(e => e.Category).HasColumnType("nvarchar").HasMaxLength(256);

        builder.Property(e => e.CategoryReason)
            .IsRequired(false)
            .HasMaxLength(4000);

        builder.Property(e => e.DosageForm).HasColumnType("nvarchar").HasMaxLength(100);

        builder.Property(e => e.DosageFormReason)
            .IsRequired(false)
            .HasMaxLength(4000);

        builder.Property(e => e.Substance).HasColumnType("nvarchar").HasMaxLength(256);

        builder.Property(e => e.CorrelationId).HasColumnType("nvarchar").HasMaxLength(256);
    }
}
