﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;

internal class ImportingImportContractReferenceClassificationRepository : TrackingGenericRepository<ImportContractReferenceClassification>, IImportingImportContractReferenceClassificationRepository
{
    public ImportingImportContractReferenceClassificationRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }
}
