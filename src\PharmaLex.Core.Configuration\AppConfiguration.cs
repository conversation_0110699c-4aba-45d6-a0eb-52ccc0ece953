﻿using Azure.Extensions.AspNetCore.Configuration.Secrets;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using System.Reflection;
namespace PharmaLex.Core.Configuration;

public static class AppConfiguration
{
    public static IHostBuilder AddConfiguration(this IHostBuilder host, Assembly assembly, string[]? args = null)
    {
        host.ConfigureAppConfiguration((context, config) =>
        {
            AddAppSettings(context, config);

            if (!IsDevelopment())
            {
                AddAzureKeyVault(config);
            }

            AddUserSecrets(assembly, config);
            AddEnvironmentVariables(config);
            AddCommandLine(args, config);
        });

        return host;
    }
    public static IHostBuilder AddConfigurationIncludingKeyVault(this IHostBuilder host, Assembly assembly, string[]? args = null)
    {
        host.ConfigureAppConfiguration((context, config) =>
        {
            AddAppSettings(context, config);
            AddAzureKeyVault(config);
            AddUserSecrets(assembly, config);
            AddEnvironmentVariables(config);
            AddCommandLine(args, config);
        });

        return host;
    }

    private static void AddEnvironmentVariables(IConfigurationBuilder config)
    {
        config.AddEnvironmentVariables();
    }

    private static void AddCommandLine(string[]? args, IConfigurationBuilder config)
    {
        if (args != null)
        {
            config.AddCommandLine(args);
        }
    }

    private static void AddUserSecrets(Assembly assembly, IConfigurationBuilder config)
    {
        if (IsDevelopment())
        {
            config.AddUserSecrets(assembly, optional: false);
        }
    }

    private static void AddAzureKeyVault(IConfigurationBuilder config)
    {
        var builtConfig = config.Build();
        // VisualStudioTenantId option required for running local sites.
        // Instructs Visual Studio to connect to the correct tenant for the key vault.
        // Necessary for PharmaLex users whose default tenant is not the Phlex tenant.
        var options = new DefaultAzureCredentialOptions { VisualStudioTenantId = builtConfig["VisualStudioTenantId"] };
        var secretClient = new SecretClient(
            new Uri($"https://{builtConfig["KeyVaultName"]}.vault.azure.net/"),
            new DefaultAzureCredential(options));

        config.AddAzureKeyVault(secretClient, new KeyVaultSecretManager());
    }

    private static void AddAppSettings(HostBuilderContext context, IConfigurationBuilder config)
    {
        var env = context.HostingEnvironment;

        config
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{env.EnvironmentName}.json", optional: true, reloadOnChange: true);
    }

    private static bool IsDevelopment()
    {
        var value = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
        return string.Equals("development", value, StringComparison.OrdinalIgnoreCase);
    }
}
