﻿using AutoMapper;
using PharmaLex.Helpers;
using System.Linq;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class CaseMappingProfile : Profile
{
    public CaseMappingProfile()
    {
        CreateMap<Cases, CaseModel>()
            .ForMember(c => c.PlxId, s => s.MapFrom(c => c.ReferenceClassification.Id))
            .ForMember(c => c.NoOfFiles, s => s.MapFrom(c => c.CaseFiles.Count()))
            .ForMember(c => c.SubstanceName, s => s.MapFrom(c => c.ReferenceClassification.Substance.Name))
            .ForMember(c => c.CompanyIds, s => s.MapFrom(c => c.CaseCompanies.Select(x => x.CompanyId)))
            .ForMember(c => c.Company, s => s.MapFrom(c => string.Join(", ", c.CaseCompanies.Select(c => c.Company.Name).OrderBy(name => name))))
            .ForMember(c => c.MLM, s => s.MapFrom(c => c.MLMDuplicate == CaseMLMDuplicate.NA ? string.Empty : c.MLMDuplicate.GetDescription()))
            .ForMember(c => c.PSUR, s => s.MapFrom(c => c.ReferenceClassification.PSURRelevanceAbstract.GetDescription()))
            .ForMember(c => c.PvSafetyDatabaseId, s => s.MapFrom(c => c.ReferenceClassification.PvSafetyDatabaseId))
            .ForMember(d => d.CreatedDate, s => s.MapFrom(x => x.CreatedDate.ToString("dd MMM yyyy HH:mm:ss")))
            .ForMember(d => d.LastUpdatedDate, s => s.MapFrom(x => x.LastUpdatedDate.ToString("dd MMM yyyy HH:mm:ss")));

        CreateMap<CaseCompanies, CaseCompanyModel>();

        CreateMap<CaseFiles, CaseFileModel>();

        CreateMap<CaseFileDocumentTypes, CaseFileDocumentTypeModel>();
    }
}