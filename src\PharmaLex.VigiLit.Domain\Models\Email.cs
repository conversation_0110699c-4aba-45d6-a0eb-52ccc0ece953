﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class Email : VigiLitEntityBase
{
    public EmailType EmailType { get; set; } = EmailType.None;
    public EmailTriggerType EmailTriggerType { get; set; } = EmailTriggerType.None;
    public DateTime StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public EmailStatusType EmailStatusType { get; set; } = EmailStatusType.None;
    public int SentEmailsCount { get; set; }
    public int FailedEmailsCount { get; set; }

    public ICollection<EmailMessage> EmailMessages { get; set; } = new List<EmailMessage>();
}
