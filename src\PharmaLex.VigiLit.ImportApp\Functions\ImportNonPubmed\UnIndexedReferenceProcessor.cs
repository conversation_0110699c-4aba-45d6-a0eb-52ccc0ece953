﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.ImportManagement.Service.Services;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.ImportApp.Functions.ImportNonPubmed;

public class UnIndexedReferenceProcessor(IUnIndexedReferenceImporter unIndexedReferenceImporter)
{
    [Transaction]
    [Function("ImportNonPubmedProcessor_ProcessEnqueuedImports")]
    public async Task Run([TimerTrigger("*/2 * * * *")] TimerInfo timer, ILogger log)
    {
        await unIndexedReferenceImporter.ProcessEnqueuedImports();
    }
}