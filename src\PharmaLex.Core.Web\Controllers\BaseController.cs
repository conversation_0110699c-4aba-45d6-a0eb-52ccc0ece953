﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Configuration;
using PharmaLex.Authentication.B2C;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.Core.Web.Enums;
using PharmaLex.Core.Web.Extensions;
using PharmaLex.Core.Web.Models.UserNotification;

namespace PharmaLex.Core.Web.Controllers;

/// <summary>
/// A base class for an MVC controller with support to add notifications.
/// </summary>
/// <seealso cref="Controller" />
#pragma warning disable S6934 // A Route attribute should be added to the controller when a route template is specified at the action level
public abstract class BaseController : Controller
#pragma warning restore S6934 // A Route attribute should be added to the controller when a route template is specified at the action level
{
    private readonly IUserSessionService _userSessionService;
    private readonly IConfiguration _configuration;

    int UserSessionInactivityLimitSeconds => _configuration.GetValue<int>("UserSession:InactivityLimitSeconds");

    protected BaseController(IUserSessionService userSessionService, IConfiguration configuration)
    {
        _userSessionService = userSessionService;
        _configuration = configuration;
    }

    protected int CurrentUserId => User.GetClaimValue<int>("plx:userid");

    protected bool IsSystemAdmin => User.HasClaim(x => x.Type == "admin:SystemAdmin" || x.Type == "admin:SuperAdmin");

    protected void AddConfirmationNotification(string html)
    {
        TempData.Set("UserNotification", new UserNotificationModel(html, type: UserNotificationType.Confirm));
    }

    protected void AddNotification(string html, UserNotificationType type, int duration = 2500, UserNotificationPosition position = UserNotificationPosition.TopCenter)
    {
        TempData.Set("UserNotification", new UserNotificationModel(html, type: type, duration: duration, position: position));
    }

    [HttpGet("[action]")]
    public async Task<IActionResult> SessionIsValid()
    {
        var result = await _userSessionService.ProcessTimerCall(HttpContext, CurrentUserId);
        return Ok(result);
    }

    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        try
        {
            if (context.RouteData.Values.Values.All(x => x?.ToString() != "SessionIsValid"))
            {
                await _userSessionService.ProcessIncomingSession(context, CurrentUserId, UserSessionInactivityLimitSeconds);
            }

            await base.OnActionExecutionAsync(context, next);
        }
        catch
        {
            // No valid session data
            context.Result = new RedirectResult("/logoutex");
        }
    }
}