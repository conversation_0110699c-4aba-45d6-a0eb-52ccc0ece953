﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Journals;

public interface IJournalRepository : ITrackingRepository<Journal>
{
    Task<IEnumerable<Journal>> GetAll();
    Task<IEnumerable<Journal>> GetForCountry(int countryId);
    Task<Journal?> GetById(int id);

    /// <summary>
    /// Gets the countries for which there are subscriptions to journals.
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<Country>> GetSubscribedCountries();

    Task<int> GetContractsWithEnabledJournalById(int journalId);
}