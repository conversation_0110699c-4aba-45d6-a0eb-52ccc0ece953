﻿namespace PharmaLex.VigiLit.DataExtraction.Service;

public class ContextInfo // public because ICallbackHandler is and that is because the PhlexVisionApiController is public - needs looking at
{
    /// <summary>
    /// Gets or sets the source language for the extraction
    /// </summary>
    /// <value>
    /// The source language.
    /// </value>
    public required string Language { get; set; }

    /// <summary>
    /// Gets or sets the raw translated text.
    /// </summary>
    /// <value>
    /// The raw translated text.
    /// </value>
    public required string RawTranslatedText { get; set; }
}

