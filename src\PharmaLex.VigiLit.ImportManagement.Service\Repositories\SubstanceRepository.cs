﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Repositories;

internal class SubstanceRepository : CachedTrackingGenericRepository<Substance>, ISubstanceRepository
{
    public SubstanceRepository(PlxDbContext context, IDistributedCache? distributedCache, IUserContext userContext)
        : base(context, distributedCache, userContext)
    {
    }

    public Task<Substance> Get(int id)
    {
        return context.Set<Substance>()
            .Include(s => s.SubstanceSynonyms)
            .Where(u => u.Id == id)
            .SingleAsync();
    }
}
