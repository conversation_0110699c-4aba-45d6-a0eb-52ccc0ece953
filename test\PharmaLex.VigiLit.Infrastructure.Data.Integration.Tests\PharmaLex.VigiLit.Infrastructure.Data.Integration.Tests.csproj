﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<IsPackable>false</IsPackable>
		<IsTestProject>true</IsTestProject>
	</PropertyGroup>
	<PropertyGroup>
		<NoWarn>$(NoWarn);CA1859</NoWarn>
	</PropertyGroup>
	<ItemGroup>
		<None Remove="appsettings.Development.json" />
		<None Remove="appsettings.json" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="appsettings.Development.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
		<Content Include="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.4" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
		<PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.267" />
		<PackageReference Include="xunit" Version="2.9.3" />
		<PackageReference Include="xunit.runner.visualstudio" Version="3.1.3">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="6.0.4">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="XunitXml.TestLogger" Version="6.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\src\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Data.Repositories\PharmaLex.VigiLit.Data.Repositories.csproj" />
		<ProjectReference Include="..\..\src\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Test.DomainHelpers\PharmaLex.VigiLit.Test.DomainHelpers.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Test.Fakes\PharmaLex.VigiLit.Test.Fakes.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore\PharmaLex.VigiLit.Test.Framework.EntityFrameworkCore.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Test.Framework\PharmaLex.VigiLit.Test.Framework.csproj" />
	</ItemGroup>

</Project>
