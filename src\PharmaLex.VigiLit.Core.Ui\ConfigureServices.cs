﻿using Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.FileProviders;
namespace PharmaLex.VigiLit.Core.Ui;
public static class ConfigureServices
{
    public static void RegisterCoreUi(this IServiceCollection services, IConfiguration configuration)
    {
        var assembly = typeof(ConfigureServices).Assembly;
        services.AddControllersWithViews()
            .AddRazorRuntimeCompilation()
            .AddApplicationPart(assembly);
        services.Configure<MvcRazorRuntimeCompilationOptions>(options =>
            { options.FileProviders.Add(new EmbeddedFileProvider(assembly)); });
    }
}