using AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class ClassificationCategoryMappingProfile : Profile
{
    public ClassificationCategoryMappingProfile()
    {
        CreateMap<ClassificationCategory, ClassificationCategoryModel>();
        CreateMap<ClassificationCategory, ClassificationCategoryHistoryModel>();
        CreateMap<ClassificationCategoryModel, ClassificationCategory>();
    }
}