using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ReferenceConfiguration : EntityBaseMap<Reference>
{
    public override void Configure(EntityTypeBuilder<Reference> builder)
    {
        base.Configure(builder);

        builder.ToTable("References", t =>
        {
            t.IsTemporal();
        });

        builder.Property(ra => ra.SourceSystem)
            .HasDefaultValue(1);

        builder.Property(e => e.Doi)
            .HasMaxLength(250);

        builder.HasIndex(e => e.SourceId).IsUnique();
        builder.HasIndex(e => new { e.Doi, e.Id });

        builder.HasMany(r => r.ReferenceClassificationsInternal)
            .WithOne(c => c.Reference)
            .HasForeignKey(r => r.ReferenceId);

        builder.Ignore(r => r.ReferenceClassifications);
    }
}
