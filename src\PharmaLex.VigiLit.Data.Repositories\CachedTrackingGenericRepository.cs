﻿using Microsoft.Extensions.Caching.Distributed;
using PharmaLex.DataAccess;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace PharmaLex.VigiLit.Data.Repositories;

public class CachedTrackingGenericRepository<TEntity> : TrackingGenericRepository<TEntity> where TEntity : class, IEntity
{
    private readonly IDistributedCache? _distributedCache;

#pragma warning disable S2743 // Bug raised
    private static List<string> _cacheKeys = new List<string>();
#pragma warning restore S2743

    public CachedTrackingGenericRepository(PlxDbContext context, IDistributedCache? distributedCache, IUserContext userContext) : base(context, userContext.User)
    {
        _distributedCache = distributedCache;
    }

    public async Task<TItem> GetFromCacheOrDatabaseAsync<TItem>(Func<Task<TItem>> query, object? queryParams = null, [CallerMemberName] string callerName = "")
    {
        var key = GetComputedCacheKey<TItem>(callerName, queryParams!);

        var item = await GetFromCacheAsync<TItem>(key);
        if (item != null)
            return item;

        item = await query();
        await SetInCache<TItem>(item, key);

        return item;
    }

    private async Task<TItem?> GetFromCacheAsync<TItem>(string key)
    {
        var item = await _distributedCache.GetStringAsync(key);
        if (!string.IsNullOrEmpty(item))
        {
            return JsonSerializer.Deserialize<TItem>(item);
        }
        return default(TItem);
    }

    private async Task SetInCache<TItem>(TItem item, string key)
    {
        if (!_cacheKeys.Contains(key))
        {
            _cacheKeys.Add(key);
        }

        var itemString = JsonSerializer.Serialize<TItem>(item);
        await _distributedCache.SetStringAsync(key, itemString);
    }

    public override int SaveChanges()
    {
        foreach (var key in _cacheKeys)
        {
            _distributedCache?.Remove(key);
        }
#pragma warning disable S2696 // Bug Raised
        _cacheKeys = new List<string>();
#pragma warning restore S2696



        return base.SaveChanges();
    }

    public override async Task<int> SaveChangesAsync()
    {
        foreach (var key in _cacheKeys)
        {
            await _distributedCache!.RemoveAsync(key);
        }
#pragma warning disable S2696 // Bug Raised
        _cacheKeys = new List<string>();
#pragma warning restore S2696

        return await base.SaveChangesAsync();
    }

    protected string GetComputedCacheKey<TModel>(string callerName, object cacheKeyParam)
    {
        var modelTypeName = typeof(TModel).Name;
        var list = new List<string> { GetMainCacheKey()!, modelTypeName, callerName };

        if (cacheKeyParam != null)
        {
            var propes = cacheKeyParam.GetType().GetProperties();

            foreach (var property in propes)
            {
                var value = property.GetValue(cacheKeyParam);
                if (value != null)
                {
                    list.Add(value.ToString()!);
                }
            }
        }

        var result = string.Join("|", list);
        return result;
    }

    protected string? GetMainCacheKey()
    {
        return typeof(TEntity).FullName;
    }
}
