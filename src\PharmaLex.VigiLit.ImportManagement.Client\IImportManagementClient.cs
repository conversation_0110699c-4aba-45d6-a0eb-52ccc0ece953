﻿namespace PharmaLex.VigiLit.ImportManagement.Client;

public interface IImportManagementClient
{
    /// <summary>
    /// Sends the specified command to enqueue a reference for importing.
    /// </summary>
    /// <param name="command">The command.</param>
    /// <returns></returns>
    Task Send(EnqueueReferenceCommand command);

    /// <summary>
    /// Sends the specified command when an extraction has failed required manual correction.
    /// </summary>
    /// <param name="command">The command.</param>
    /// <returns></returns>
    Task Send(ManualCorrectionCommand command);
}
