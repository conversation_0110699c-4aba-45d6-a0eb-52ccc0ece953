﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.ImportManagement.Client;
using PharmaLex.VigiLit.ImportManagement.Client.Dto;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Service.Repositories;
using PharmaLex.VigiLit.Logging;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;
internal class EnqueueReferenceCommandHandler : IEnqueueReferenceCommandHandler
{
    private readonly IMapper _mapper;
    private readonly ILogger<EnqueueReferenceCommandHandler> _logger;
    private readonly IImportReferenceRepository _importReferenceRepository;

    public EnqueueReferenceCommandHandler(ILogger<EnqueueReferenceCommandHandler> logger, IMapper mapper, IImportReferenceRepository importReferenceRepository)
    {
        _logger = logger;
        _mapper = mapper;
        _importReferenceRepository = importReferenceRepository;
    }

    public async Task Consume(EnqueueReferenceCommand command)
    {
        _logger.LogInformation("EnqueueReferenceCommandHandler:Consume:{ImportReferenceTitle}", LogSanitizer.Sanitize(command.Reference.Title));

        var importReference = MapImportReferenceStatusQueued(command.Reference);
        importReference.CreatedBy = "File Import";
        importReference.LastUpdatedBy = "File Import";
        importReference.CorrelationId = command.Reference.CorrelationId;
        await SaveImportReference(importReference);

        _logger.LogInformation("EnqueueReferenceCommandHandler:Consume: Completed.");
    }

    private async Task SaveImportReference(ImportReference importReference)
    {
        _importReferenceRepository.Add(importReference);
        await _importReferenceRepository.SaveChangesAsync();
    }

    private ImportReference MapImportReferenceStatusQueued(ReferenceDto reference)
    {
        var importReference = _mapper.Map<ImportReference>(reference);
        importReference.StatusType = ImportReferenceStatusType.Queued;
        return importReference;
    }
}
