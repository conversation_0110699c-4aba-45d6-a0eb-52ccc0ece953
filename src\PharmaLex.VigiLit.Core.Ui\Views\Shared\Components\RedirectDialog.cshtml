﻿<script type="text/x-template" id="redirect-dialog-template">
    <div id="esignature-dialog" class="vue-dialog-surface" v-if="show" v-on:click.stop="$emit('close')">
        <div class="vue-dialog-container esignature-dialog" v-on:click.stop="$emit('close')">
            <i class="icon-cancel-circled dialog-closer" v-on:click.stop="$emit('close')"></i>
            <div class="dialog-content">
                <h5>{{title}}</h5>
                <p>{{message}}</p>
                <p class="hint">{{hint}}</p>
                <p v-if="redirecting" class="generate-button">Redirecting...</p>
                <a v-if="!redirecting" class="button buttonIcon mr-2" v-on:click.stop="redirecting=true" :href="redirectUrl" >{{buttonText}}</a>
                <button v-if="!redirecting" class="secondary icon-button-cancel" v-on:click.prevent="$emit('close')">Cancel</button>
                <footer></footer>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('redirect-dialog', {
        template: '#redirect-dialog-template',
        data() {
            return {
                redirecting: false
            };
        },
        props: {
            show: {
                type: Boolean,
                default: false
            },
            message: String,
            hint: String,
            redirectUrl: String,
            buttonText: String,
            title: String
        }
    });
</script>