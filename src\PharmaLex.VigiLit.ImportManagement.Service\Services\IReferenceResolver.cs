﻿using PharmaLex.VigiLit.Aggregators.PubMed;
using PharmaLex.VigiLit.Core.Aggregator.Models;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;

// This is redundant as we already have the IReferenceProvider and could use the SubstanceRepository instead of middle-man here
internal interface IReferenceResolver
{
    Task<ReferenceResults> GetReferences(ESearchCriteria criteria, DateTime timeout);

    // Shouldn't be here
    Task<Substance> GetSubstance(int substanceId);
}