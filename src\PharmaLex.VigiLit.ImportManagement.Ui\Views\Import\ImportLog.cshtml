@using PharmaLex.Core.Web.Helpers
@model IEnumerable<PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule.ImportModel>
@{
    ViewData["Title"] = "Import Log";
}

<div id="import-log" v-cloak>
    <div class="sub-header">
        <h2>Import Log</h2>
        <div class="controls">
        </div>
    </div>
    <section>
        <filtered-table :items="imports" :columns="columns" :filters="filters" :link="link"></filtered-table>
    </section>
</div>

@section Scripts {

    <script type="text/javascript">

        var pageConfig = {
            appElement: "#import-log",
            data: function () {
                return {
                    link: '/Import/ImportLogDetails/',
                    imports: @Html.Raw(AntiXss.ToJson(Model)),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'importType',
                                sortKey: 'importType',
                                header: 'Type',
                                type: 'text',
                                style: 'width: 8%;'
                            },
                            {
                                dataKey: 'importDate',
                                sortKey: 'importDate',
                                header: 'Import Date',
                                type: 'text',
                                style: 'width: 8%;'
                            },
                            {
                                dataKey: 'importTriggerType',
                                sortKey: 'importTriggerType',
                                header: 'Trigger',
                                type: 'text',
                                style: 'width: 9%;'
                            },
                            {
                                dataKey: 'startDate',
                                sortKey: 'startDate',
                                header: 'Start Date',
                                type: 'text',
                                style: 'width: 11%;'
                            },
                            {
                                dataKey: 'endDate',
                                sortKey: 'endDate',
                                header: 'End Date',
                                type: 'text',
                                style: 'width: 11%;'
                            },
                            {
                                dataKey: 'duration',
                                sortKey: 'duration',
                                header: 'Duration',
                                type: 'text',
                                style: 'width: 10%;'
                            },
                            {
                                dataKey: 'importStatusType',
                                sortKey: 'importStatusType',
                                header: 'Status',
                                type: 'text',
                                style: 'width: 10%;'
                            },
                            {
                                dataKey: 'contractsCount',
                                sortKey: 'contractsCount',
                                header: 'Contracts',
                                type: 'number',
                                style: 'width: 6%;'
                            },
                            {
                                dataKey: 'referencesCount',
                                sortKey: 'referencesCount',
                                header: 'References',
                                type: 'number',
                                style: 'width: 6%;'
                            },
                            {
                                dataKey: 'newReferencesCount',
                                sortKey: 'newReferencesCount',
                                header: 'New Refs',
                                type: 'number',
                                style: 'width: 6%;'
                            },
                            {
                                dataKey: 'updatesCount',
                                sortKey: 'updatesCount',
                                header: 'Updates',
                                type: 'number',
                                style: 'width: 6%;'
                            },
                            {
                                dataKey: 'silentUpdatesCount',
                                sortKey: 'silentUpdatesCount',
                                header: 'Silent Upd.',
                                type: 'number',
                                style: 'width: 6%;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'importType',
                            options: [],
                            type: 'importType',
                            header: 'Search Type',
                            fn: v => p => p.importType.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'importTriggerType',
                            options: [],
                            type: 'search',
                            header: 'Search Trigger',
                            fn: v => p => p.importTriggerType.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'importStatusType',
                            options: [],
                            type: 'search',
                            header: 'Search Status',
                            fn: v => p => p.importStatusType.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            }
        };
    </script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/FilteredTable" />
}