﻿@model PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.ContractDetailsModel

@{
    Layout = null;
}
<!DOCTYPE html>
<html lang="en">

<head>
    <title>VigiLit Search Strings Print Preview</title>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <style>
        .instructions {
            background: #F0F8FF;
            border: 1px solid #1E90FF;
            border-radius: 5px;
            padding: 0 15px;
            margin-top: 20px;
        }

        @@media print {
            .instructions {
                display: none;
            }
        }

        h1, h2, h3, p, .filters div {
            font-family: sans-serif;
            margin: 0;
            padding: 0;
        }

        h1 {
            font-size: 21px;
        }

        h2 {
            font-size: 18px;
            margin-top: 30px;
        }

        h3 {
            font-size: 17px;
            color: #000;
        }

        p {
            font-size: 14px;
            margin: 14px 0;
        }

        .container {
            margin: 0 auto;
            width: 1050px;
        }

        .header {
            margin-top: 30px;
        }


        .version-label {
            background: #572F8C;
            color: #EEE;
            padding: .5rem 1rem;
            border-radius: 4px;
        }

        .contract-details > p {
            padding-top: 10px;
            border-top: 0.5px solid #000;
        }

        .pad-left-85 {
            padding-left: 85px;
        }

        .pad-left-200 {
            padding-left: 200px;
        }

        .pad-left-125 {
            padding-left: 125px;
        }

        .pad-left-145 {
            padding-left: 145px;
        }

        .contract-versions {
            margin-top: 30px;
        }

            .contract-versions > div {
                border-top: 2px solid #DDD;
                margin-bottom: 15px;
            }

                .contract-versions > div > div:first-child {
                    display: flex;
                    flex-direction: row;
                }

                    .contract-versions > div > div:first-child > div:first-child {
                        width: 76%;
                        padding: 18px 18px 5px 0;
                    }

                    .contract-versions > div > div:first-child > div:last-child {
                        width: 24%;
                        padding: 18px 0 5px 18px;
                        border-left: 1px solid #EEE;
                    }

                .contract-versions > div > div:last-child {
                    border-top: 1px solid #EEE;
                    border-bottom: 2px solid #DDD;
                }

                    .contract-versions > div > div:last-child span {
                        display: inline-block;
                        margin-right: 30px;
                    }
    </style>
</head>

<body>
    <div class="container">
        <div class="instructions">
            <p>press ctrl+p or <a href="#" onclick="window.print();return false;">click here</a> to print, then select "save as pdf".</p>
        </div>

        <div class="header">
            <h1>VigiLit Search Strings Print Preview</h1>
            <p>Generated on @DateTime.UtcNow.ToString("d MMM yyyy") at @DateTime.UtcNow.ToString("HH:mm") UTC by @User.Identity.Name</p>
        </div>

        @if (Model.ContractsVersions.Any())
        {
            <p><b>Contract Overview</b></p>
            <div class="contract-details">
                <p><b>ID</b><span class="pad-left-200"> @Html.Raw(Model.Id)</span></p>
                <p><b>Project Name</b><span class="pad-left-125">@Html.Raw(Model.ProjectName)</span></p>
                <p><b>Substance</b><span class="pad-left-145">@Html.Raw(Model.SubstanceName)</span></p>
                <p><b>Contract Start Date</b>
                    <span class="pad-left-85">
                        @Html.Raw(Model.ContractStartDate.Year < 1901 ? "Not Started" : Model.ContractStartDate.ToString("d MMM yyyy"))    
                    </span></p>
            </div>
            <div class="contract-versions">
                @foreach (var contractVersion in Model.ContractsVersions)
                {
                    <div>
                        <div>
                            <div>
                                <p> <span class="version-label">V @Html.Raw(contractVersion.Version)</span> Created on @Html.Raw(contractVersion.TimeStamp.ToString("d MMM yyyy")) @(!string.IsNullOrWhiteSpace(contractVersion.LastModifiedByUserName) ? "| Created By " + @Html.Raw(contractVersion.LastModifiedByUserName) : "") </p>
                                <p><b>Search String</b><br />@Html.Raw(contractVersion.SearchString)</p>
                                <p><b>Reason for Change</b><br />@Html.Raw(contractVersion.ReasonForChange)</p>

                            </div>
                            <div>
                                @if (contractVersion.JournalTitles.Count == 0)
                                {
                                    <p><b>Contract Type</b><br />@Html.Raw(contractVersion.ContractType)</p>
                                    @if (!string.IsNullOrWhiteSpace(contractVersion.ContractWeekday))
                                    {
                                        <p><b>Contract Weekday</b><br />@Html.Raw(contractVersion.ContractWeekday)</p>
                                    }
                                    <p><b>Search Period</b><br />@Html.Raw(contractVersion.SearchPeriodDays)</p>
                                }
                                <p><b>Status</b><br />@Html.Raw(contractVersion.IsActive ? "Active" : "Inactive")</p>
                            </div>
                        </div>
                    </div>

                }
            </div>
        }
        else
        {
            <div class="results">
                <p>No contract version found.</p>
            </div>
        }
    </div>
</body>
</html>