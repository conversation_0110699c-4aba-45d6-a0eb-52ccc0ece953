using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Data.Repositories;

public class EmailMessageRelevantEventRepository : TrackingGenericRepository<EmailMessageRelevantEvent>, IEmailMessageRelevantEventRepository
{
    private readonly IMapper _mapper;

    public EmailMessageRelevantEventRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<EmailMessageClassificationModel>> GetEmailMessageClassifications(int emailMessageId)
    {
        var query = context.Set<EmailMessageRelevantEvent>()
            .Include(e => e.EmailRelevantEvent).ThenInclude(e => e.ClassificationCategory)
            .Include(e => e.EmailRelevantEvent).ThenInclude(e => e.CompanyInterest).ThenInclude(e => e.Reference)
            .Include(e => e.EmailRelevantEvent).ThenInclude(e => e.CompanyInterest).ThenInclude(e => e.ReferenceClassification).ThenInclude(e => e.Substance)
            .Where(e => e.EmailMessageId == emailMessageId)
            .OrderBy(e => e.Id)
            .AsNoTracking();

        return await _mapper.ProjectTo<EmailMessageClassificationModel>(query).ToListAsync();
    }
}
