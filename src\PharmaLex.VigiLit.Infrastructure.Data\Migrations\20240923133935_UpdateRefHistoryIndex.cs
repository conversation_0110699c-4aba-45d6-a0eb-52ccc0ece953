﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class UpdateRefHistoryIndex : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("IF EXISTS (SELECT * FROM sysindexes where name = 'IX_ReferencesHistory_Id_PeriodEnd_PeriodStart') DROP INDEX [IX_ReferencesHistory_Id_PeriodEnd_PeriodStart] ON [dbo].[ReferencesHistory] WITH ( ONLINE = OFF )");
            migrationBuilder.Sql("CREATE NONCLUSTERED INDEX [IX_ReferencesHistory_Id_PeriodEnd_PeriodStart] ON [dbo].[ReferencesHistory] ([Id],[PeriodEnd],[PeriodStart]) INCLUDE(Abstract, AffiliationTextFirstAuthor, Authors, CountryOfOccurrence, DateRevised, Do<PERSON>, [Language], PMID, Title, Keywords, MeshHeadings)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP INDEX [IX_ReferencesHistory_Id_PeriodEnd_PeriodStart] ON [dbo].[ReferencesHistory] WITH ( ONLINE = OFF )");
        }
    }
}
