﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddFeatureFlagEnableJournals : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
                "INSERT INTO [dbo].[FeatureFlags] ( [Name], [Enabled], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                "SELECT 'EnableJournals', 1, GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                "WHERE NOT EXISTS (SELECT [Name] FROM [dbo].[FeatureFlags] WHERE [Name] = 'EnableJournals'); ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM [FeatureFlags] WHERE [Name] = 'EnableJournals';");
        }
    }
}
