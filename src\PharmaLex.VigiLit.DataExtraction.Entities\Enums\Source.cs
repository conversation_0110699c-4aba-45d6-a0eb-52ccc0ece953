﻿namespace PharmaLex.VigiLit.DataExtraction.Entities.Enums;

/// <summary>
/// Source of the data that needs to be extracted.
/// </summary>
/// <remarks>
/// This notes the difference between a file being imported and a file that has been pulled down from the web. In the future we will extend this to include email.
/// This is needed so that the source is maintained throughout the entire workflow, and the UI will be able to render differently depending on where the extracted data came from.
/// </remarks>
public enum Source
{
    None = 0,
    File = 1,
    Web = 2,
}