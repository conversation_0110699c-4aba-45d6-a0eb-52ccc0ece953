﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class UserEmailPreferenceConfigurartion : EntityBaseMap<UserEmailPreference>
{
    public override void Configure(EntityTypeBuilder<UserEmailPreference> builder)
    {
        base.Configure(builder);

        builder.ToTable("UserEmailPreferences");
        builder.<PERSON><PERSON><PERSON>(up => new { up.UserId, up.EmailPreferenceId });
        builder.Ignore(up => up.Id);

        builder.HasOne(up => up.User)
            .WithMany(u => u.UserEmailPreferences)
            .HasForeignKey(up => up.UserId);

        builder.HasOne(up => up.EmailPreference)
            .WithMany(ep => ep.UserEmailPreferences)
            .HasForeignKey(sc => sc.EmailPreferenceId);
    }
}