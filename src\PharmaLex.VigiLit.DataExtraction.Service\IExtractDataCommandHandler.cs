﻿using PharmaLex.VigiLit.DataExtraction.Client;

namespace PharmaLex.VigiLit.DataExtraction.Service;

public interface IExtractDataCommandHandler
{
    /// <summary>
    /// Consumes the specified command which causes the given file to be enqueued for data extraction.
    /// 
    /// Internally:
    /// - Calls the MoveImportFiles method to move the blob from the 
    ///   'import-file-document-upload' container to the 'import-file-document' container,
    ///   and retrieves the URL of the destination blob.
    /// - If a failure occurs during repository operations, the blob move is rolled back by
    ///   copying the file back to the original container and deleting it from the destination.
    /// </summary>
    /// <param name="command">The command.</param>
    Task Consume(ExtractDataCommand command);
}
