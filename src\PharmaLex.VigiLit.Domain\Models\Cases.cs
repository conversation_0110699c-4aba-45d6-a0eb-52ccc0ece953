﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.VigiLit.Domain.Models;

public class Cases : VigiLitEntityBase
{
    // PLXID
    [ForeignKey("ReferenceClassification")]
    public int ReferenceClassificationId { get; set; }
    public ReferenceClassification ReferenceClassification { get; set; }

    [Required]
    public CaseMLMDuplicate MLMDuplicate { get; set; }

    [Required]
    public CaseStatus Status { get; set; }

    [MaxLength(2500)]
    public string Comment { get; set; }

    public IEnumerable<CaseFiles> CaseFiles { get; set; } = new List<CaseFiles>();

    public IEnumerable<CaseCompanies> CaseCompanies { get; set; } = new List<CaseCompanies>();

    public Cases() { }

    public Cases(int referenceClassificationId, CaseMLMDuplicate mlmDuplicate, string comment, CaseStatus caseStatus = CaseStatus.Pending)
    {
        ReferenceClassificationId = referenceClassificationId;
        MLMDuplicate = mlmDuplicate;
        Status = caseStatus;
        Comment = comment;
    }
}