﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Infrastructure.Utilities;

public class UrlRenderHelper : IUrlRenderHelper
{
    private readonly IPubMedUrlRenderHelper _pubMedUrlRenderHelper;
    private readonly ILogger<UrlRenderHelper> _logger;
    private readonly IWebsiteUriProvider _websiteUriProvider;

    public UrlRenderHelper(ILoggerFactory loggerFactory, IPubMedUrlRenderHelper pubMedUrlRenderHelper, IWebsiteUriProvider websiteUriProvider)
    {
        _logger = loggerFactory.CreateLogger<UrlRenderHelper>();
        _pubMedUrlRenderHelper = pubMedUrlRenderHelper;
        _websiteUriProvider = websiteUriProvider;
    }

    public string GetUrlLinkBasedOnSourceSystem(DailyClassificationEmailSendGridTemplateModelRow referenceModel)
    {
        var reference = referenceModel.Reference;
        var referenceClassificationId = referenceModel.ReferenceClassification.Id;
        _logger.LogInformation(
            "Details of SourceSystem: {SourceSystem} and ReferenceClassificationId: {ReferenceClassificationId}",
            reference.SourceSystem, referenceClassificationId);
        if (reference.SourceSystem != SourceSystem.PubMed)
        {
            return GetUrl(referenceClassificationId);
        }

        var referenceModelMapped = MapToReferenceModel(reference);
        return _pubMedUrlRenderHelper.GetAbstractAndUrlConcatenated(referenceModelMapped);
    }

    private static ReferenceModel MapToReferenceModel(ReferenceSendGridTemplateModel reference)
    {
        return new ReferenceModel
        {
            Abstract = reference.Abstract,
            SourceId = reference.SourceId
        };
    }

    private string GetUrl(int referenceClassificationId)
    {
        var baseUrl = new Uri(_websiteUriProvider.Provide(), "/References/Classifications/");
        _logger.LogInformation("Base Url from UriProvider : {BaseUrl}", baseUrl);
        return $"{baseUrl}{referenceClassificationId}";
    }
}