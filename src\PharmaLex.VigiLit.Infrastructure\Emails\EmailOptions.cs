﻿namespace PharmaLex.VigiLit.Infrastructure.Emails;

public class EmailOptions
{
    public const string EmailSettings = "EmailSettings";

    public string NoReplyEmail { get; set; } = string.Empty;
    public string NoReplyEmailName { get; set; } = string.Empty;
    public string InfoEmail { get; set; } = string.Empty;
    public string OrderingEmail { get; set; } = string.Empty;
    public string DailyReferenceClassificationEmailTemplateId { get; set; } = string.Empty;
    public string InvitationEmailTemplateId { get; set; } = string.Empty;
    public string CaseEmailTemplateId { get; set; } = string.Empty;
    public long? CaseEmailAttachmentMaxBytes { get; set; }
    public Uri? WebsiteUri { get; set; }
}
