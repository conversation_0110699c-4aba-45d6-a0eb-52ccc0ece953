﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;
public class ImportFileConfiguration : EntityBaseMap<ImportFile>
{
    public override void Configure(EntityTypeBuilder<ImportFile> builder)
    {
        base.Configure(builder);
        builder.ToTable("ImportFiles");
        builder.HasIndex(e => e.FileHash).IsUnique();
        builder.Property(e => e.FileName)
        .HasMaxLength(256);
    }
}
