﻿using Microsoft.Extensions.DependencyInjection;
using PharmaLex.Core.UserSessionManagement;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.DataAccessLayer;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;

namespace PharmaLex.VigiLit.Data.Repositories;

public static class ConfigureServices
{
    public static void RegisterRepositories(this IServiceCollection services)
    {
        services.AddScoped<PlxDbContext, VigiLitDbContext>();
        services.RegisterDbContext<VigiLitDbContext>();

        services.AddScoped<IClaimRepository, ClaimRepository>();
        services.AddScoped<ICompanyInterestRepository, CompanyInterestRepository>();
        services.AddScoped<ICompanyRepository, CompanyRepository>();
        services.AddScoped<ICompanyUserRepository, CompanyUserRepository>();
        services.AddScoped<IContractRepository, ContractRepository>();
        services.AddScoped<IEmailRelevantEventRepository, EmailRelevantEventRepository>();
        services.AddScoped<IEmailRepository, EmailRepository>();
        services.AddScoped<IEmailMessageRepository, EmailMessageRepository>();
        services.AddScoped<IEmailMessageRelevantEventRepository, EmailMessageRelevantEventRepository>();
        services.AddScoped<IEmailPreferenceRepository, EmailPreferenceRepository>();
        services.AddScoped<IEmailSuppressionRepository, EmailSuppressionRepository>();
        services.AddScoped<ICaseRepository, CaseRepository>();
        services.AddScoped<ICaseCompanyRepository, CaseCompanyRepository>();
        services.AddScoped<ICaseFileRepository, CaseFileRepository>();
        services.AddScoped<ICaseFileDocumentTypeRepository, CaseFileDocumentTypeRepository>();
        services.AddScoped<IProjectRepository, ProjectRepository>();
        services.AddScoped<IReferenceHistoryActionRepository, ReferenceHistoryActionRepository>();
        services.AddScoped<ISubstanceRepository, SubstanceRepository>();
        services.AddScoped<ISubstanceSynonymRepository, SubstanceSynonymRepository>();
        services.AddScoped<IUserRepository, UserRepository>();
        services.AddScoped<IUserSessionRepository, UserSessionRepository>();

        // Add here Interfaces used purely for importing
    }
}
