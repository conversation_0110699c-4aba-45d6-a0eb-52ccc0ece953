﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Blobs" Version="12.25.0" />
    <PackageReference Include="CsvHelper" Version="33.1.0" />
    <PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.126" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.UserSessionManagement.Entities\PharmaLex.Core.UserSessionManagement.Entities.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Aggregators.PubMed\PharmaLex.VigiLit.Aggregators.PubMed.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.MessageBroker.Contracts\PharmaLex.VigiLit.MessageBroker.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Contracts\PharmaLex.VigiLit.Reporting.Contracts.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Ui.ViewModels\PharmaLex.VigiLit.Ui.ViewModels.csproj" />
  </ItemGroup>

</Project>
