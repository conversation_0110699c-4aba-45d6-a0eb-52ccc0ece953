﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface ICompanyUserRepository : ITrackingRepository<CompanyUser>
{
    Task<CompanyUser?> GetCompanyUserByIdAsync(int companyId, int userId);
    Task<List<CompanyUser>> GetAllCompanyUsersWithSuppressions();
    Task<CompanyUser?> GetCompanyUserByEmail(string email);
}