using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Data.Repositories;

public class EmailRelevantEventRepository : TrackingGenericRepository<EmailRelevantEvent>, IEmailRelevantEventRepository
{
    public EmailRelevantEventRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }
}
