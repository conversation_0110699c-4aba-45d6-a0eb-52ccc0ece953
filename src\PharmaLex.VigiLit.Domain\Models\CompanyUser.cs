﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Domain.Models;

public class CompanyUser : VigiLitEntityBase
{
    public int CompanyId { get; set; }
    public Company Company { get; set; }
    public int UserId { get; set; }
    public User User { get; set; }
    public bool Active { get; set; }
    public CompanyUserType CompanyUserType { get; set; }
}