﻿using PharmaLex.VigiLit.ImportManagement.Ui.Services;
using DownloadFile = PharmaLex.VigiLit.ImportManagement.Ui.Models.DownloadFile;

namespace PharmaLex.VigiLit.ImportManagement.Ui.FileImport;

public interface IImportFileService : ICardRetriever
{
    /// <summary>
    /// Creates import file details that will be used for storing in blob storage.
    /// </summary>
    /// <param name="model">The import file model containing file details and metadata.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    Task Add(ImportFileUploadModel model);

    /// <summary>
    /// Asynchronously retrieves a list of file import records associated with the specified batch identifier.
    /// </summary>
    /// <param name="batchId">The unique identifier of the batch for which file import records are to be retrieved.</param>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains a list of <see cref="ImportFileModel"/> objects corresponding to the file imports of the specified batch.
    /// </returns>
    Task<List<ImportFileModel>> GetImportFile(Guid batchId);

    Task Enqueue(Guid batchId);

    /// <summary>
    /// Deletes an import file associated with the specified batch Id.
    /// </summary>
    /// <param name="batchId">The unique identifier of the batch to delete.</param>
    /// <returns>A task representing the asynchronous delete operation.</returns>
    Task Abandon(Guid batchId);

    /// <summary>
    /// Deletes the specified import file.
    /// </summary>
    /// <param name="model">The import file to be deleted.</param>
    /// <returns>A task representing the asynchronous delete operation.</returns>
    Task DeleteImportFile(ImportFileModel model);

    /// <summary>
    /// Deletes a list of import files asynchronously.
    /// </summary>
    /// <param name="files">A list of <see cref="ImportFileModel"/> representing the files to be deleted.</param>
    /// <exception cref="InvalidOperationException">
    /// Thrown when one or more files could not be deleted.
    /// </exception>
    /// <remarks>
    /// This method iterates through the provided list and attempts to delete each file individually.
    /// If any deletion fails, the errors are collected, and an <see cref="InvalidOperationException"/> is thrown
    /// with details of the failed deletions.
    /// </remarks>
    Task DeleteImportFiles(List<ImportFileModel> files);

    /// <summary>
    /// Download imported file.
    /// </summary>
    /// <param name="batchId"></param>
    /// <param name="fileName"></param>
    /// <returns></returns>
    Task<DownloadFile> DownloadImportedFile(Guid batchId, string fileName);

    /// <summary>
    /// Downloads given file (uses ImportFileDescriptor)
    /// </summary>
    /// <param name="batchId">Batch id</param>
    /// <param name="fileName">File name</param>
    /// <returns>DownloadFile object</returns>
    Task<DownloadFile> DownloadFile(Guid batchId, string fileName);
}
