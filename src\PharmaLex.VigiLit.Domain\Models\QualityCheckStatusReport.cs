﻿namespace PharmaLex.VigiLit.Domain.Models;

public class QualityCheckStatusReport
{
    public int AssessorId { get; set; }
    public int PreClassifiedCount { get; set; }
    public int ClassifiedCount { get; set; }
    public int QCPercentage { get; set; }

    public int ItemsToTake
    { 
        get
        {
            int total = PreClassifiedCount + ClassifiedCount;

            decimal percentage = (decimal)QCPercentage / 100;

            decimal sample = total * percentage;

            int sampleRounded = (int)Math.Ceiling(sample);

            int remaining = sampleRounded - ClassifiedCount;

            return Math.Max(0, remaining);
        }
    }
}