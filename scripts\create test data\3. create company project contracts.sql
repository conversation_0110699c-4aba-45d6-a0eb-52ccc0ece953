-- =============================================
-- Author:      <PERSON>
-- Create date: 2023-08-22
-- Description: Creates a company, a project, and a contract configured to run Mon-Fri.
-- =============================================

DECLARE @CompanyName nvarchar(250) = 'My Company' 	-- name your company
DECLARE @ProjectName nvarchar(250) = 'My Project' 	-- name your project
DECLARE @SubstanceName nvarchar(250) = 'Zinc' 		-- name of one of the existing substances


-- DO NOT EDIT BELOW THIS LINE --

BEGIN TRAN
	
	DECLARE @SearchString nvarchar(4000) = '("' + @SubstanceName + '") AND (abuse OR abuse* OR addiction OR addict* OR adverse OR adverse drug reaction OR breast feed* OR breastfeed* OR cancerogen* OR carcinogen* OR case OR cases OR congenital disorder OR contraindication OR deaths OR dependan* OR dependen* OR dependency OR embryopath* OR embryopathy OR fatal OR fertile OR interact* OR drug interaction OR intoxica* OR lack of effect OR lack of effect* OR lack of efficacy OR lactation OR lactating OR lactations OR lethal OR malformation OR malformed OR medication error OR misus* OR mortality OR mortalities OR mutagen* OR occupational exposure OR occupational exposures OR off-label OR overdos* OR overdose OR pharmacovigilance OR poison* OR pregnancies OR pregnant OR drug resistance OR safety OR side effect* OR teratogen* OR drug tolerance OR toxic* OR treatment failure OR tumorigen* OR tumourigen* OR untoward OR withdraw* OR drug withdrawal)'
	
	DECLARE @CompanyId int
	DECLARE @ProjectId int
	DECLARE @SubstanceId int
	DECLARE @ContractId int
	DECLARE @UserId int
	DECLARE @ContractVersionId int
	
	SELECT @SubstanceId = [Id] FROM [Substances] WHERE [Name] = @SubstanceName
	
	SELECT TOP 1 @UserId = [Id] FROM [Users] ORDER BY [Id]
	
	-- create company
	INSERT INTO [Companies] ([Name], [IsActive], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
		VALUES (@CompanyName, 1, GETUTCDATE(), 'script', GETUTCDATE(), 'script')

	SET @CompanyId = SCOPE_IDENTITY()
		
	-- create project
	INSERT INTO [Projects] ([Name], [CompanyId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
		VALUES (@ProjectName, @CompanyId, GETUTCDATE(), 'script', GETUTCDATE(), 'script')
		
	SET @ProjectId = SCOPE_IDENTITY()

	-- create contracts mon-fri
	DECLARE @ContractWeekday int = 1
	WHILE @ContractWeekday <= 5
	BEGIN
		INSERT INTO [Contracts] ([SubstanceId], [ProjectId], [CurrentContractVersionId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
			VALUES (@SubstanceId, @ProjectId, -1, GETUTCDATE(), 'script', GETUTCDATE(), 'script')
			
		SET @ContractId = SCOPE_IDENTITY()
			
		INSERT INTO [ContractVersions] ([ContractId], [ContractVersionStatus], [IsActive], [ContractType], [ContractWeekday], [SearchPeriodDays], [SearchString], [Version], [ReasonForChange], [UserId], [TimeStamp], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
			VALUES (@ContractId, 3, 1, 10, @ContractWeekday, 56, @SearchString, 1, 'reason', @UserId, GETUTCDATE(), GETUTCDATE(), 'script', GETUTCDATE(), 'script')
			
		SET @ContractVersionId = SCOPE_IDENTITY()
		
		UPDATE [Contracts] SET [CurrentContractVersionId] = @ContractVersionId WHERE [Id] = @ContractId
		
		SET @ContractWeekday = @ContractWeekday + 1
	END

COMMIT TRAN
