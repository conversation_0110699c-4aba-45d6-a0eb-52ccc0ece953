image:
  repository: phlexglobal.azurecr.io/vigilit-ai
  pullPolicy: Always

replicas: 1
minAvailability: 1

keyVaultName: "vgt-dev-kv-eun"

azureWorkload:
  clientId: f4fc3690-02e3-47dc-878e-cf67c2191872 #aksshared-sharednonprod-aks-eun-sp-vigilit-ai-dev

serviceBus: "vgt-dev-servicebus-eun"
newRelicAppName: "vgt-dev-ai-eun"

AiEndpointSettingsUri: "https://vigilit-ai-dev.smartphlex.com/latest/process"
