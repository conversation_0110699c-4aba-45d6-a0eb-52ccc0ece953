using System.Diagnostics.CodeAnalysis;
using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

public class AssemblyFactory : IAssemblyFactory
{
    public IAssembly GetAssembly(Type type)
    {
        return new AssemblyAdapter(Assembly.GetAssembly(type));
    }

    public IAssembly GetEntryAssembly
    {
        get
        {
            var assembly = Assembly.GetEntryAssembly();

            if (assembly != null)
                return new AssemblyAdapter(assembly);
            else
                return null;
        }
    }

    public IAssembly GetExecutingAssembly
    {
        get
        {
            // NOTE: The following line looks wrong as it isn't 'GetExecutingAssembly'
            // however if GetExecutingAssembly was called then the assembly for the adapters
            // would be returned.
            var assembly = Assembly.GetCallingAssembly();

            if (assembly != null)
            {
                return new AssemblyAdapter(assembly);
            }

            return null;
        }
    }

    public IAssembly LoadFile(string fileName)
    {
        return new AssemblyAdapter(Assembly.LoadFile(fileName));
    }
}