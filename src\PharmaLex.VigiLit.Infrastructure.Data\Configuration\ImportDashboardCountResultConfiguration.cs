using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ImportDashboardCountResultConfiguration : EntityBaseMap<ImportDashboardCountResult>
{
    public override void Configure(EntityTypeBuilder<ImportDashboardCountResult> builder)
    {
        builder.HasNoKey().ToView(null);
    }
}