﻿using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

#pragma warning disable S1135 // Work in progress - remove once all TODO: have been done
public interface IPreClassificationService
{
    Task PreClassifyAsync(IEnumerable<PreclassifyReferenceModel> preClassifyReferenceModels);
    Task<IEnumerable<PreclassifyReferenceModel>> PickNextToPreClassify(int importId);

    Task<PreclassifyModel> GetFirst(); // Pick_InitialPageLoad

    Task<PreclassifyModel> GetNext(); // Pick_Subsequent

    // TODO: should be a locking service and this should be on that. Also needs to take Ids doesn't need the entire object
    Task<bool> UserHasAllLocks(IEnumerable<PreclassifyReferenceModel> preClassifyReferenceModels);

    // TODO: Rename
    Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetPreClassifiedByCurrentUser(int importId);

    Task RePreClassifyAsync(ReferenceClassificationModel model);

    // TODO: should be a locking service and this should be on that if we're locking this way
    Task Release();
    Task Release(int userId);

    Task<IEnumerable<PreclassifyReferenceModel>?> PickLockedClassifications();

    Task RetryAiSuggestions(ReferenceClassificationModel referenceModel);

    Task<AiSuggestedClassificationModel> GetExistingAiSuggestion(string sourceId, string substance);
}
#pragma warning restore S1135