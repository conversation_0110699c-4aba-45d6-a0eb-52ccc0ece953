@using PharmaLex.Core.Web.Helpers
@model PharmaLex.VigiLit.ImportManagement.Ui.Models.AdHocListDetailsPageModel
@{
    ViewData["Title"] = "Ad-Hoc Import Contracts";
}

<div id="ad-hoc-list-details" v-cloak>
    <div class="sub-header">
        <h2>Ad-Hoc Import Contracts</h2>
        <div class="controls">
            <a class="button" href="../AdHocList">Back</a>
        </div>
    </div>
    <section>
        <filtered-table :items="pageModel.adHocImportContractModels" :columns="columns" :filters="filters"></filtered-table>
    </section>
</div>

@section Scripts {

    <script type="text/javascript">

        var pageConfig = {
            appElement: "#ad-hoc-list-details",
            data: function () {
                return {
                    pageModel: @Html.Raw(AntiXss.ToJson(Model)),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'companyName',
                                sortKey: 'companyName',
                                header: 'Company',
                                type: 'text',
                                style: 'width: 20%;'
                            },
                            {
                                dataKey: 'projectName',
                                sortKey: 'projectName',
                                header: 'Project',
                                type: 'text',
                                style: 'width: 20%;'
                            },
                            {
                                dataKey: 'substanceName',
                                sortKey: 'substanceName',
                                header: 'Substance',
                                type: 'text',
                                style: 'width: 20%;'
                            },
                            {
                                dataKey: 'contractType',
                                sortKey: 'contractType',
                                header: 'Type',
                                type: 'text',
                                style: 'width: 20%;'
                            },
                            {
                                dataKey: 'searchPeriod',
                                sortKey: 'searchPeriod',
                                header: 'Search Period',
                                edit: {},
                                type: 'text',
                                style: 'width: 20%;'
                            }
                        ]
                    },
                    filters: [{
                        key: 'companyName',
                        options: [],
                        type: 'search',
                        header: 'Search Company',
                        fn: v => p => p.companyName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'projectName',
                        options: [],
                        type: 'search',
                        header: 'Search Project',
                        fn: v => p => p.projectName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'substanceName',
                        options: [],
                        type: 'search',
                        header: 'Search Substance',
                        fn: v => p => p.substanceName.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'contractType',
                        options: [],
                        type: 'search',
                        header: 'Search Type',
                        fn: v => p => p.contractType.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    },
                    {
                        key: 'searchPeriod',
                        options: [],
                        type: 'search',
                        header: 'Search Search Period',
                        fn: v => p => p.searchPeriod.toLowerCase().includes(v.toLowerCase()),
                        convert: v => v
                    }
                    ]
                };
            }
        };
    </script>
}

@section VueComponentScripts{
    <partial name="Components/Vue3/FilteredTable" />
}