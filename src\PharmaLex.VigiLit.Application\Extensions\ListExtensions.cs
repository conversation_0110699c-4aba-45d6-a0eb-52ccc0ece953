﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.VigiLit.Application.Extensions;

public static class ListExtensions
{
    public static bool ContentsEqual<T>(this List<T> list1, List<T> list2) where T : IComparable<T>
    {
        var nullOrEmptyListsSame = NullOrEmptyListsSame(list1, list2);

        if (nullOrEmptyListsSame.HasValue)
        {
            return nullOrEmptyListsSame.Value;
        }

        return list1!.SequenceEqual(list2);
    }

    public static bool OrderedContentsEqual<T>(this List<T> list1, List<T> list2) where T : IComparable<T>
    {
        var nullOrEmptyListsSame = NullOrEmptyListsSame(list1, list2);

        if (nullOrEmptyListsSame.HasValue)
        {
            return nullOrEmptyListsSame.Value;
        }

        list1 = list1!.OrderBy(x => x).ToList();
        list2 = list2.OrderBy(x => x).ToList();

        return list1!.SequenceEqual(list2);
    }

    public static bool? NullOrEmptyListsSame<T>(List<T> list1, List<T> list2)
    {
        bool? nullOrEmptyListsSame = null;

        if (list1 == null && list2 == null)
        {
            nullOrEmptyListsSame = true;
        }
        else if ((list1 == null && list2.Count > 0) || (list1 != null && list2 == null))
        {
            nullOrEmptyListsSame = false;
        }
        return nullOrEmptyListsSame;
    }
}