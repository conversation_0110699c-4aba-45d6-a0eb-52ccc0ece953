﻿using PharmaLex.FeatureManagement.Entities;
using PharmaLex.FeatureManagement.Repositories;

namespace PharmaLex.Core.Configuration.Services;
public class FeatureFlagServices : IFeatureFlagServices
{
    private readonly IFeatureFlagRepository _featureFlagRepository;


    public FeatureFlagServices(IFeatureFlagRepository featureFlagRepository)
    {
        _featureFlagRepository = featureFlagRepository;
    }

    public async Task<FeatureFlag?> GetFeatureFlag(string name)
    {
        return await _featureFlagRepository.GetFeatureFlag(name);
    }

    public async Task UpdateAsync(FeatureFlag model)
    {
        var existingFeatureFlag = await _featureFlagRepository.GetFeatureFlag(model.Name);

        if (existingFeatureFlag != null)
        {
            existingFeatureFlag.Update(model.Enabled);
        }
        await _featureFlagRepository.SaveChangesAsync();
    }

}
