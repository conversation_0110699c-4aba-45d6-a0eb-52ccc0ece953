﻿using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

public class EventInfoAdapter : MemberInfoAdapter, IEventInfo
{
    private readonly EventInfo _eventInfo;
    private IType _eventHandlerType;

    public EventInfoAdapter(EventInfo eventInfo)
        : base(eventInfo)
    {
        _eventInfo = eventInfo;
    }

    public IType EventHandlerType
    {
        get
        {
            if (_eventHandlerType == null)
            {
                _eventHandlerType = new TypeAdapter(_eventInfo.EventHandlerType);
            }

            return _eventHandlerType;
        }
    }
}