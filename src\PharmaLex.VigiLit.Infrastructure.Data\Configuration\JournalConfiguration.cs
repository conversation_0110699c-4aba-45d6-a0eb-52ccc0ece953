﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class JournalConfiguration : EntityBaseMap<Journal>
{
    public override void Configure(EntityTypeBuilder<Journal> builder)
    {
        base.Configure(builder);

        builder.ToTable("Journals");

        builder.Property(e => e.Name).IsRequired().HasMaxLength(1000);
        builder.Property(e => e.CountryId).IsRequired();
        builder.Property(e => e.Url).HasMaxLength(2048);
        builder.Property(e => e.Enabled).HasDefaultValue(true);
        builder.HasOne(j => j.Country).WithMany(c => c.Journals).HasForeignKey(j => j.CountryId);
    }
}