﻿using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

public interface IDashboardService
{
    /// <summary>
    /// Gets the imports for dashboard.
    /// </summary>
    /// <returns>Imports for the dashboard.</returns>
    Task<IEnumerable<ImportDashboardModel>> GetImportsForDashboard();

    /// <summary>
    /// Gets the import dashboard details.
    /// </summary>
    /// <param name="importId">The import id.</param>
    /// <param name="page">The page.</param>
    /// <param name="pageSize">Size of the page.</param>
    /// <returns>Import dashboard details.</returns>
    /// <exception cref="ArgumentOutOfRangeException">Thrown if any of the parameters are out of range.</exception>
    Task<DashboardDetailsModel> GetImportDashboardDetails(int importId, int page, int pageSize);

    /// <summary>
    /// Gets the import dashboard details export.
    /// </summary>
    /// <param name="importId">The import id.</param>
    /// <returns>Import dashboard details for export.</returns>
    Task<IEnumerable<DashboardDetailsRowModel>> GetImportDashboardDetailsExport(int importId);

    /// <summary>
    /// Archives the import.
    /// </summary>
    /// <param name="importId">The import id.</param>
    Task Archive(int importId);

    /// <summary>
    /// Selects the import.
    /// </summary>
    /// <param name="importId">The import id.</param>
    Task Select(int importId);

    /// <summary>
    /// Deselects the import.
    /// </summary>
    /// <param name="importId">The import id.</param>
    Task Deselect(int importId);

    /// <summary>
    /// Gets the selected import for the current user.
    /// </summary>
    /// <returns>The selected import.</returns>
    Task<ImportSelectionModel?> GetSelectedImport();

    Task<DashboardModel> GetCompanyDashboardStats();
}
