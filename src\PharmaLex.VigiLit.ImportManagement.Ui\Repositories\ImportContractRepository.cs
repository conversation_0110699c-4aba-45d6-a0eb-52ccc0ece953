using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public class ImportContractRepository : TrackingGenericRepository<ImportContract>, IImportContractRepository
{
    protected readonly IMapper _mapper;

    public ImportContractRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<ImportContractModel>> GetImportContractsLog(int importId)
    {
        var query = context.Set<ImportContract>()
            .Include(ic => ic.Contract)
                .ThenInclude(c => c.Substance)
            .Include(ic => ic.Contract)
                .ThenInclude(c => c.Project)
            .Include(ic => ic.Contract)
                .ThenInclude(c => c.Project)
                    .ThenInclude(p => p.Company)
            .Where(ic => ic.ImportId == importId)
            .OrderBy(x => x.Contract.Project.Company.Name)
                .ThenBy(x => x.Contract.Project.Name)
                .ThenBy(x => x.Contract.Substance.Name)
            .AsSplitQuery()
            .AsNoTracking();

        return await _mapper.ProjectTo<ImportContractModel>(query).ToListAsync();
    }

    public async Task<ImportContract?> GetLastScheduledLogEntryForContract(int contractId, int days)
    {
        var query = context.Set<ImportContract>()
            .Include(ic => ic.Import)
            .Where(ic =>
                // same contract
                ic.ContractId == contractId
                // scheduled type only, ignore ad-hoc imports
                && ic.Import.ImportType == ImportType.Scheduled
                // only look back x days
                && ic.PubMedModificationDate >= DateTime.UtcNow.AddDays(days * -1))
            .OrderByDescending(ic => ic.Id)
            .AsNoTracking();

        return await query.FirstOrDefaultAsync();
    }
}
