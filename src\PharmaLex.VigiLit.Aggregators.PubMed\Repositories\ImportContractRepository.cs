﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Repositories;

internal class ImportContractRepository : TrackingGenericRepository<ImportContract>, IImportContractRepository
{
    protected readonly IMapper _mapper;

    public ImportContractRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<ImportContract?> GetLastScheduledLogEntryForContract(int contractId, int days)
    {
        var query = context.Set<ImportContract>()
            .Include(ic => ic.Import)
            .Where(ic =>
                // same contract
                ic.ContractId == contractId
                // scheduled type only, ignore ad-hoc imports
                && ic.Import.ImportType == ImportType.Scheduled
                // only look back x days
                && ic.PubMedModificationDate >= DateTime.UtcNow.AddDays(days * -1))
            .OrderByDescending(ic => ic.Id)
            .AsNoTracking();

        return await query.FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<Contract>> GetContractsForScheduledImport(DateTime importDate)
    {
        var query = context.Set<Contract>()
            .Include(c => c.Substance)
            .Include(c => c.ContractVersionsInternal)
            .Include(c => c.Project)
            .ThenInclude(p => p.Company)
            .Where(c =>
                // active 
                c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.IsActive
                // current contract version type is scheduled 
                && c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.ContractType == ContractType.Scheduled
                // contract is configured for this weekday
                && c.ContractVersionsInternal.FirstOrDefault(cs => cs.Id == c.CurrentContractVersionId)!.ContractWeekday == (ContractWeekday)importDate.DayOfWeek
                // company must be active
                && c.Project.Company.IsActive
                // exclude contracts without a search string
                && c.ContractVersionsInternal.Any())
            .OrderBy(c => c.Project.Company.Name)
            .ThenBy(c => c.Project.Name)
            .ThenBy(c => c.Substance.Name)
            .AsNoTracking();

        return await query.ToListAsync();
    }
}
