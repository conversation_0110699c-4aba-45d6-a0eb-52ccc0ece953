﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Contracts\**" />
    <EmbeddedResource Remove="Contracts\**" />
    <None Remove="Contracts\**" />
  </ItemGroup>

  <ItemGroup>
	  <ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
  </ItemGroup>

</Project>
