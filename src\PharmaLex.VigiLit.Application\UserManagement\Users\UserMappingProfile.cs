﻿using System.Linq;
using AutoMapper;
using PharmaLex.Core.UserManagement.Users;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Application.UserManagement.Users;

public class UserMappingProfile : Profile
{
    public UserMappingProfile()
    {
        CreateMap<User, UserModel>().ReverseMap();

        CreateMap<User, UserFullModel>()
            .ForMember(um => um.Name, u => u.MapFrom(x => $"{x.GivenName} {x.FamilyName} ({x.Email})"))
            .ForMember(um => um.Value, u => u.MapFrom(x => x.Email))
            .ForMember(c => c.Claims, m => m.MapFrom(t => t.ClaimsInternal))
            .ForMember(c => c.ClaimIds, m => m.MapFrom(t => t.ClaimsInternal.Select(c => c.Id)));

        CreateMap<Microsoft.Graph.Models.User, UserFullModel>()
            .ForMember(um => um.Name, u => u.MapFrom(x => $"{x.GivenName} {x.Surname} ({x.UserPrincipalName})"))
            .ForMember(um => um.GivenName, u => u.MapFrom(x => x.GivenName))
            .ForMember(um => um.Email, u => u.MapFrom(x => x.UserPrincipalName))
            .ForMember(um => um.FamilyName, u => u.MapFrom(x => x.Surname))
            .ForMember(um => um.Id, u => u.MapFrom(x => 0));

        CreateMap<User, AssessorModel>()
            .ForMember(s => s.UserSubstances, m => m.MapFrom(t => t.UserSubstances));

        CreateMap<User, UserLocksModel>()
            .ForMember(s => s.LocksCount, m => m.MapFrom(t => t.ReferenceClassificationLocks.Count));
    }
}