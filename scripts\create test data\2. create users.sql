-- =============================================
-- Author:      <PERSON>
-- Create date: 2023-08-22
-- Description: Creates users not created by the initial migration.
-- =============================================

DROP PROCEDURE IF EXISTS [CreateUser]
GO

CREATE PROCEDURE [CreateUser] @Email nvarchar(256), @FirstName nvarchar(512), @LastName nvarchar(512), @ClaimName nvarchar(1024)
AS
	DECLARE @ClaimId int
	DECLARE @UserId int

	SELECT @ClaimId = [Id] from [Claims] WHERE [Name] = @ClaimName
	
	BEGIN TRAN

		INSERT INTO [Users] ([Email], [GivenName], [FamilyName], [LastLoginDate], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
			VALUES (@Email, @FirstName, @LastName, NULL, GETUTCDATE(), 'script', GETUTCDATE(), 'script')

		SET @UserId = SCOPE_IDENTITY()

		INSERT INTO [UserClaims] ([ClaimsInternalId], [UsersInternalId])
			VALUES (@ClaimId, @UserId)  
			
	COMMIT TRAN
GO

-- devs
EXEC [CreateUser]	@Email = '<EMAIL>',		@FirstName = 'Sagar',		@LastName = 'Thapa',	@ClaimName = 'SuperAdmin';

-- testers
EXEC [CreateUser]	@Email = '<EMAIL>',	@FirstName = 'Dimitar',		@LastName = 'Angelov',	@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',	@FirstName = 'Gergana',		@LastName = 'Peneva',	@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',		@FirstName = 'David',		@LastName = 'Jones',	@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',		@FirstName = 'Olly',		@LastName = 'Ray',		@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',			@FirstName = 'Marcin',		@LastName = 'Wandel',	@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',	@FirstName = 'Anthony',		@LastName = 'Spencer',	@ClaimName = 'SuperAdmin';

-- designers
EXEC [CreateUser]	@Email = '<EMAIL>',		@FirstName = 'Martin',		@LastName = 'Law',		@ClaimName = 'SuperAdmin';

-- devops
EXEC [CreateUser]	@Email = '<EMAIL>',	@FirstName = 'Veselin',		@LastName = 'Todorov',	@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',	@FirstName = 'Borislav',	@LastName = 'Ivchev',	@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',	@FirstName = 'Yuliyan',		@LastName = 'Serbezki',	@ClaimName = 'SuperAdmin';
EXEC [CreateUser]	@Email = '<EMAIL>',	@FirstName = 'Svetoslav',	@LastName = 'Petkov',	@ClaimName = 'SuperAdmin';
GO

DROP PROCEDURE IF EXISTS [CreateUser]
GO
