﻿using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

public class MethodInfoAdapter : MemberInfoAdapter, IMethodInfo
{
    private readonly MethodInfo _methodInfo;
    private IType _returnType;

    public MethodInfoAdapter(MethodInfo methodInfo) : base((MemberInfo)methodInfo)
    {
        _methodInfo = methodInfo;
    }

    public IParameterInfo[] GetParameters()
    {
        var unadaptedParameters = _methodInfo.GetParameters();
        var totalParameters = unadaptedParameters.Length;
        IParameterInfo[] parameters = new IParameterInfo[totalParameters];

        for (int i = 0; i < totalParameters; i++)
        {
            parameters[i] = new ParameterInfoAdapter(unadaptedParameters[i]);
        }

        return parameters;
    }

    public object Invoke(object instance, object[] parameters)
    {
        return _methodInfo.Invoke(instance, parameters);
    }

    public IType ReturnType
    {
        get
        {
            if (_returnType == null)
            {
                _returnType = new TypeAdapter(_methodInfo.ReturnType);
            }

            return _returnType;
        }
    }

    public bool IsSpecialName => _methodInfo.IsSpecialName;
}