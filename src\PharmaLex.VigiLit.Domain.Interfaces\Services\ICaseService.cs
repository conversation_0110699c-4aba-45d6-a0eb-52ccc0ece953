﻿using PharmaLex.VigiLit.Domain.Exceptions;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface ICaseService
{
    Task<IEnumerable<CaseModel>> GetAllAsync();
    Task<IEnumerable<CaseModel>> SearchAsync(CaseSearchRequest searchRequest);
    Task<CaseModel> GetByIdAsync(int id);
    CaseValidationResult Validate(SaveCaseRequest saveCaseRequest);
    Task<CaseValidationResult> Validate(string fileName, Stream stream, int plxId, string uploadId);
    Task<int> AddAsync(SaveCaseRequest request);
    Task UpdateAsync(SaveCaseRequest request);
    Task DeleteAsync(int id);
    Task PublishPendingCasesAsync();

    /// <summary>
    /// Retrieves the document stream and filename associated with the specified case model and case file ID.
    /// </summary>
    /// <param name="caseModel">The case model.</param>
    /// <param name="caseFileId">The ID of the case file.</param>
    /// <returns>
    /// A <see cref="Task{TResult}"/> representing the asynchronous operation. The task result contains a tuple
    /// containing the document stream and its filename.
    /// </returns>
    /// <exception cref="NotFoundException">
    /// Thrown when the document corresponding to the specified case model and case file ID is not found.
    /// </exception>
    Task<(Stream stream, string filename)> GetDocumentStream(CaseModel caseModel, int caseFileId);
}