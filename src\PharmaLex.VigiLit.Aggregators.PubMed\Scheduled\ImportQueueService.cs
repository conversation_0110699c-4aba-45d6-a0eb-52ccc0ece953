﻿using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Aggregators.PubMed.Repositories;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Helpers;
using System.Diagnostics.CodeAnalysis;
using TimeZoneConverter;

namespace PharmaLex.VigiLit.Aggregators.PubMed.Scheduled;

internal class ImportQueueService : IImportQueueService
{
    private readonly ILogger<ImportQueueService> _logger;
    private readonly IImportRepository _importRepository;
    private readonly IImportContractRepository _importContractRepository;
    private readonly IContractRepository _contractRepository;
    private readonly IAdHocImportRepository _adHocImportRepository;

    public ImportQueueService(
        ILoggerFactory loggerFactory,
        IImportRepository importRepository,
        IImportContractRepository importContractRepository,
        IContractRepository contractRepository,
        IAdHocImportRepository adHocImportRepository)
    {
        _logger = loggerFactory.CreateLogger<ImportQueueService>();

        _importRepository = importRepository;
        _importContractRepository = importContractRepository;
        _contractRepository = contractRepository;
        _adHocImportRepository = adHocImportRepository;
    }

    public async Task EnqueueAdHocImport(int adHocImportId)
    {
        var adHocImport = await _adHocImportRepository.GetForEnqueue(adHocImportId);
        var import = new Import(ImportType.AdHoc, ImportTriggerType.Manual, DateTime.UtcNow.Date);
        AddAdhocImportContractsToImport(adHocImport, import);

        // save changes - in one batch so the import can't start being processed before it is fully populated
        _importRepository.Add(import);
        await _importRepository.SaveChangesAsync();

        adHocImport.AdHocImportStatusType = AdHocImportStatusType.Enqueued;
        await _adHocImportRepository.SaveChangesAsync();
    }

    [SuppressMessage("Minor Code Smell", "S3267:Loops should be simplified with \"LINQ\" expressions", Justification = "This would not be simpler.")]
    private static void AddAdhocImportContractsToImport(AdHocImport adHocImport, Import import)
    {
        foreach (var adHocImportContract in adHocImport.AdHocImportContracts)
        {
            // all the import contracts will get the same pubmed create date filter
            var pubMedCreateDateFrom = CalculatePubMedCreateDateFrom(
                adHocImport.SourceSearchStartDate.Value,
                  adHocImportContract.Contract.GetCurrentContractVersion().SearchPeriodDays);

            // one row per day in the time period
            var modDate = adHocImport.SourceSearchStartDate.Value; // clone
            while (modDate <= adHocImport.SourceSearchEndDate.Value)
            {
                import.ImportContracts.Add(new ImportContract(adHocImportContract.Contract, modDate, pubMedCreateDateFrom));
                modDate = modDate.AddDays(1);
            }
        }
    }

    [Trace]
    public async Task EnqueueScheduledImport(ImportTriggerType importTriggerType) // SHOULD NOT HAVE TRIGGER TYPE
    {
        try
        {
            // The last completed american day is the last whole day in the ET timezone.
            // We do this because the modification date in PubMed has no time component.
            DateTime lastCompletedAmericanDay = GetLastCompletedAmericanDay();

            // The import date is the date the UI thinks the import happened on.
            // It isn't when the import actually happens, the scheduled import is typically one day earlier than this, and manual triggers can be whenever.
            var importDate = lastCompletedAmericanDay.AddDays(2);

            // Get contracts which are configured to be imported on the specified weekday.
            var contracts = await _importContractRepository.GetContractsForScheduledImport(importDate);

            var import = new Import(ImportType.Scheduled, importTriggerType, importDate);

            await AddScheduledImportContractsToImport(lastCompletedAmericanDay, contracts, import);

            // save changes - in one batch so the import can't start being processed before it is fully populated
            _importRepository.Add(import);
            await _importRepository.SaveChangesAsync();

            _logger.LogInformation("Enqueued scheduled import for lastCompletedAmericanDay: {lastCompletedAmericanDay}; importDate: {importDate}; contracts: {contracts}.",
                lastCompletedAmericanDay.ToString("yyyy-MM-dd"), importDate.ToString("yyyy-MM-dd"), contracts.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception during EnqueueScheduledImport. \nError: {error}", ex.ToString());
            throw;
        }
    }

    private async Task AddScheduledImportContractsToImport(DateTime lastCompletedAmericanDay, IEnumerable<Contract> contracts, Import import)
    {
        foreach (var contract in contracts)
        {
            // need to know the day of last import contract - usually it'll just be the previous day
            // checking the last log entry means we can:
            // - not run on weekends and monday's run should include the weekend
            // - run a contract only on a configured day of the week
            ImportContract logEntry = await _importContractRepository.GetLastScheduledLogEntryForContract(contract.Id, Constants.ImportContractLastRunDays);

            // we can only make up gap days if we have a valid previous record
            if (logEntry != null && logEntry.PubMedModificationDate.HasValue)
            {
                // all the import contracts will get the same pubmed create date filter
                var pubMedCreateDateFrom = CalculatePubMedCreateDateFrom(
                    logEntry.PubMedModificationDate.Value,
                      contract.GetCurrentContractVersion().SearchPeriodDays);

                // add one row for each day since the last record
                var previousCompletedAmericanDay = logEntry.PubMedModificationDate;
                while (previousCompletedAmericanDay < lastCompletedAmericanDay)
                {
                    // advance first so we're not adding the same day as the log entry
                    previousCompletedAmericanDay = previousCompletedAmericanDay.Value.AddDays(1);

                    import.ImportContracts.Add(new ImportContract(contract, previousCompletedAmericanDay.Value, pubMedCreateDateFrom));
                }
            }
            else
            {
                var pubMedCreateDateFrom = CalculatePubMedCreateDateFrom(
                    lastCompletedAmericanDay,
                      contract.GetCurrentContractVersion().SearchPeriodDays);

                // there's no valid previous log entry, just add today's record
                import.ImportContracts.Add(new ImportContract(contract, lastCompletedAmericanDay, pubMedCreateDateFrom));
            }
        }
    }

    private static DateTime? CalculatePubMedCreateDateFrom(DateTime modDate, int searchPeriodDays)
    {
        // 0 means unlimited, the create date filter will not be applied.
        if (searchPeriodDays == 0)
        {
            return null;
        }

        // for limiting pubmed search by create date, usually 56 days before mod date.
        return modDate.AddDays(searchPeriodDays * -1);
    }


    /// <summary>
    /// <para>PubMed Modification Date is stored in ET (this is not the same thing as EST or EDT)</para>
    /// <para>https://www.timeanddate.com/time/zones/et</para>
    /// <para> We are using the TimeZoneConverter package to get timezones in a platform-independent way as Microsoft's built-in solution isn't reliable.</para>
    /// <para> https://github.com/mattjohnsonpint/TimeZoneConverter </para>
    /// </summary>
    private static DateTime GetLastCompletedAmericanDay()
    {
        TimeZoneInfo tzi = TZConvert.GetTimeZoneInfo("America/New_York");
        var nowInNewYork = TimeZoneInfo.ConvertTime(DateTime.UtcNow, tzi);
        var yesterdayInNewYork = nowInNewYork.AddDays(-1);
        // need to specify kind otherwise when it's saved it'll get converted to utc.
        var lastCompletedAmericanDay = new DateTime(yesterdayInNewYork.Year, yesterdayInNewYork.Month, yesterdayInNewYork.Day, 0, 0, 0, DateTimeKind.Utc);
        return lastCompletedAmericanDay;
    }

    public async Task<bool> CanRetry(int importId)
    {
        var import = await _importRepository.Get(importId);

        return import.ImportStatusType == ImportStatusType.CompletedWithFailedContracts;
    }

    [SuppressMessage("Minor Code Smell", "S3267:Loops should be simplified with \"LINQ\" expressions", Justification = "This would not be simpler.")]
    public async Task Retry(int importId)
    {
        // optimised get used during import, don't edit it
        var import = await _importRepository.GetWithContracts(importId);

        // enqueue failed contracts
        foreach (var importContract in import.ImportContracts)
        {
            if (ImportContractStatusTypeGroups.Failed.Contains(importContract.ImportContractStatusType))
            {
                // make sure the retry uses the latest search string
                var contract = await _contractRepository.Get(importContract.ContractId);
                importContract.ContractVersionId = contract.GetCurrentContractVersion().Id;

                // set state to be picked up for processing
                importContract.ImportContractStatusType = ImportContractStatusType.Queued;
                importContract.StartDate = null;
                importContract.EndDate = null;

                // set overall import state to show processing is outstanding
                import.ImportStatusType = ImportStatusType.Started;
            }
        }

        await _importRepository.SaveChangesAsync();
    }
}