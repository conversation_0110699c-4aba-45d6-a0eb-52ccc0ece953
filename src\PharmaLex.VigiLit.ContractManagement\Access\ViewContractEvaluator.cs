﻿using PharmaLex.Core.UserManagement;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.ContractManagement.Security;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using System.Diagnostics.CodeAnalysis;

namespace PharmaLex.VigiLit.ContractManagement.Access;
internal class ViewContractEvaluator : PermissionEvaluator<ViewContractPermissionContext>
{
    private readonly IUserRepository _userRepository;
    private readonly IContractRepository _contractRepository;

    public ViewContractEvaluator(IUserRepository userRepository, IContractRepository contractRepository)
    {
        _userRepository = userRepository;
        _contractRepository = contractRepository;
    }

    private async Task<IUserEntity?> GetSecurityUser(int userId)
    {
        return await _userRepository.GetForSecurity(userId);
    }

    private async Task<bool> CanUserViewContract(int userId, int contractId)
    {
        var user = await GetSecurityUser(userId);

        if (TryDetermineAccessByUserType(user, out bool accessResult))
        {
            return accessResult;
        }
        return await _contractRepository.DoesContractBelongToCompany(user.GetActiveCompanyId(), contractId);
    }

    public override async Task<bool> HasPermissions(ViewContractPermissionContext context)
    {
        if (await CanUserViewContract(context.UserId, context.ContractId))
        {
            return true;
        }

        throw new UnauthorizedAccessException();
    }

    /// <summary>
    /// Tries to evaluate access based on their user type.
    /// </summary>
    /// <param name="user">The user.</param>
    /// <param name="accessResult">if returns <c>true</c> [access result].</param>
    /// <returns>
    /// Returns True if can evaluate what the access should be and returns that access result. 
    /// Returns False if further processing is necessary for the operation. 
    /// </returns>
    private static bool TryDetermineAccessByUserType([NotNullWhen(false)] IUserEntity? user, out bool accessResult)
    {
        accessResult = false;

        if (user == null)
        {
            accessResult = false;
            return true;
        }

        if (!user.IsCompanyUser())
        {
            accessResult = true;
            return true;
        }

        if (!user.HasActiveCompany())
        {
            accessResult = false;
            return true;
        }

        return false;
    }
}
