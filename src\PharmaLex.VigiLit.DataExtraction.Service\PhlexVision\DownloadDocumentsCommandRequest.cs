﻿using Microsoft.AspNetCore.Mvc;

namespace PharmaLex.VigiLit.DataExtraction.Service.PhlexVision;

// Reference Axon nuget
public class DownloadDocumentsCommandRequest : IHasCorrelationId
{
    [FromRoute(Name = "id")]
    public string? DocumentId { get; init; }

    [FromHeader(Name = "fileName")]
    public string? FileName { get; init; }

    public string FileExtension => FileName != null ? FileName.Split(".")[1] : "";

    [FromHeader(Name = PhlexVisionConstants.CorrelationIdHeader)]
    public string? CorrelationId { get; init; }
}