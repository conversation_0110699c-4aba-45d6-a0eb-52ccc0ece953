using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using System.Threading.Tasks;

namespace PharmaLex.VigiLit.ImportApp.Functions.Email;

public class SendDailyClassificationEmail
{
    private readonly IEmailService _emailService;

    public SendDailyClassificationEmail(IEmailService emailService)
    {
        _emailService = emailService;
    }

    [Transaction]
    [Function("Email_SendDailyClassificationEmail")]
    public async Task Run([TimerTrigger("0 0 21 * * Mon-Fri")] TimerInfo timer, ILogger log)
    {
        await _emailService.SendDailyReferenceClassificationEmails(EmailTriggerType.Auto);
    }
}