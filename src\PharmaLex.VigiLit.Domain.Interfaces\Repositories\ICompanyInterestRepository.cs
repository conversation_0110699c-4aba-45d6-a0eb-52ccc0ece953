using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface ICompanyInterestRepository : ITrackingRepository<CompanyInterest>
{
    Task<bool> CompanyHasInterestInReference(int companyId, int referenceId);
    Task<bool> CompanyHasInterestInClassification(int companyId, int referenceClassificationId);
    Task<IEnumerable<CompanyInterest>> GetForLoggingEmailRelevantEvent(int referenceClassificationId);
    Task<IEnumerable<CompanyInterest>> GetForSendingDailyEmail(int companyId);
    void ClearChangeTracker();
}