﻿using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.VigiLit.Domain.Models.Document.Case;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;

public interface ICaseDocumentUploadService
{
    Task Create(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, Stream stream, CancellationToken cancellationToken = default);
    Task<bool> Exists(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, CancellationToken cancellationToken = default);
    Task<Stream> OpenRead(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, CancellationToken cancellationToken = default);
    Task Delete(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, CancellationToken cancellationToken = default);
    Task<DocumentProperties> Save(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, CaseDocumentDescriptor caseDocumentDescriptor, CancellationToken cancellationToken = default);
}