﻿using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using SendGrid;

namespace PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;

public interface IEmailSender
{
    Task<Response> SendDailyReferenceClassificationEmail(DailyClassificationEmailSendGridTemplateModel templateData);
    Task<Response> SendInvitationEmail(InvitationEmailModel model);
    Task<Response> SendCaseEmail(CaseEmailModel caseEmail, string fileName, byte[] zippedAttachment);
}