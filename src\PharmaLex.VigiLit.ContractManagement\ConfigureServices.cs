﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.ContractManagement.Access;
using PharmaLex.VigiLit.ContractManagement.Security;

namespace PharmaLex.VigiLit.ContractManagement;
public static class ConfigureServices 
{
    public static void RegisterContractManagement(this IServiceCollection services, IConfiguration configuration)
    {
        // Will need to move contract services and repos to here 

        services.AddScoped<IPermissionEvaluator, ViewContractEvaluator>();
        services.AddScoped<IPermissionEvaluator<ViewContractPermissionContext>, ViewContractEvaluator>();
    }
}
