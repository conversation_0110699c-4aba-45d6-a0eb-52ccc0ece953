﻿namespace PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;

public interface IStreamHelper
{
    /// <summary>
    /// Converts a series of files into a single zipped byte array; can be used when sending zipped email attachments
    /// </summary>
    /// <param name="files">Dictionary representing <file name, file data> </param>
    /// <returns>All files zipped into single byte array</returns>
    byte[] GetZipOfBytesFromFileStreams(Dictionary<string, Stream> files);

    Dictionary<string, MemoryStream> UnzipBytesToDictionaryOfStreams(byte[] inputBytes);

    bool AreDictionariesTheSame(Dictionary<string, Stream> compareTo, Dictionary<string, MemoryStream> compare);

    bool AreStreamsIdentical(Stream stream, MemoryStream memoryStream);
}
