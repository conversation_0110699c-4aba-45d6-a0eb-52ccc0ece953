﻿<script type="text/x-template" id="contract-history-display-template">
    <div v-for='(item, index) in contractVersions' :key='item.id' class="p-2">
        <div class="flex flex-wrap">
            <div class="flex-item flex-80-percent p-3 tile">
                    <p> <span class="version-label">V{{item.version}}</span> Created on {{getDateFormat(item.timeStamp)}} <span v-if="item.lastModifiedByUserName"> | Created By {{item.lastModifiedByUserName}}<span> </p>
                <label>Search String</label>
                <p>{{item.searchString}}</p>
                <label>Reason for Change</label>
                <p>{{item.reasonForChange}}</p>
            </div>

                <div class="flex-item flex-20-percent p-3 tile" style="border-left: solid 1px #c4c4c4;">
                    <span v-if="item.journalTitles.length == 0">
                        <label>Contract Type</label>
                        <p>{{item.contractType}}</p>
                        <label v-if="item.contractWeekday">Contract Weekday</label>
                        <p>{{item.contractWeekday}}</p>
                        <label>Search Period</label>
                        <p>{{item.searchPeriodDays}}</p>
                    </span>
                    <label>Status</label>
                    <p>{{parseActiveFlag(item.isActive)}}</p>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('contract-history-display', {
        template: '#contract-history-display-template',
        props: {
            contractVersions: {
                type: Object
            }
        },
        methods: {
            getDateFormat: function (date) {
                return moment.utc(date).format('DD MMM YYYY');
            },
            parseActiveFlag(isActive) {
                return isActive == true ? 'Active' : 'Inactive';
            }
        },
        created() {
        }
    });
</script>
