﻿using System.Collections.Generic;
using System.Linq;

namespace PharmaLex.VigiLit.Domain.UserManagement;

public static class Policies
{
    public const char KEY_SEPARATOR = ':';

    public const string SuperAdmin = Claims.SuperAdmin;
    public const string InternalSupport = Claims.InternalSupport;
    public const string PreAssessor = Claims.PreAssessor;
    public const string MasterAssessor = Claims.MasterAssessor;
    public const string ClientResearcher = Claims.ClientResearcher;
    public const string PharmaLexResearcher = Claims.PharmaLexResearcher;
    public const string PharmaLexClientResearcher = Claims.PharmaLexClientResearcher;
    public const string CaseFileOperator = Claims.CaseFileOperator;

    public const string Admin = "Admin";
    public const string Assessor = "Assessor";
    public const string CaseManagement = "CaseManagement";
    public const string CanAccessContractHistoryVersions = "CanAccessContractHistoryVersions";
    public const string CaseExternal = "CaseExternal";
    public const string InternalUser = "InternalUser";
    public const string ExternalUser = "ExternalUser";

    private static readonly IDictionary<string, string[]> _policyClaimKeys = new Dictionary<string, string[]>();

    static Policies()
    {
        _policyClaimKeys[Assessor] = new string[]
        {
            CreateClaimKey(ClaimTypes.Operator, PreAssessor),
            CreateClaimKey(ClaimTypes.Operator, MasterAssessor),
            CreateClaimKey(ClaimTypes.Admin, SuperAdmin)
        };
        _policyClaimKeys[CaseManagement] = new string[]
        {
            CreateClaimKey(ClaimTypes.Operator, CaseFileOperator),
            CreateClaimKey(ClaimTypes.Admin, SuperAdmin)
        };
        _policyClaimKeys[CaseExternal] = new string[]
        {
            CreateClaimKey(ClaimTypes.Company, ClientResearcher)
        };
        _policyClaimKeys[ExternalUser] = new string[]
        {
            CreateClaimKey(ClaimTypes.Company, ClientResearcher),
        };

        _policyClaimKeys[InternalUser] = new string[]
        {
            CreateClaimKey(ClaimTypes.Operator, CaseFileOperator),
            CreateClaimKey(ClaimTypes.Operator, PreAssessor),
            CreateClaimKey(ClaimTypes.Operator, MasterAssessor),
            CreateClaimKey(ClaimTypes.Operator, PharmaLexResearcher),
            CreateClaimKey(ClaimTypes.Admin, SuperAdmin),
            CreateClaimKey(ClaimTypes.Admin, InternalSupport)
        };
    }

    public static bool ContainsClaimKey(string key, string policy)
    {
        if (policy == Admin)
        {
            return KeyHasClaimType(key, ClaimTypes.Admin);
        }
        if (_policyClaimKeys.ContainsKey(policy))
        {
            return _policyClaimKeys[policy].Contains(key);
        }

        return false;
    }

    public static string CreateClaimKey(string claimType, string claimName)
    {
        return $"{claimType}{KEY_SEPARATOR}{claimName}";
    }

    private static bool KeyHasClaimType(string key, string claimType)
    {
        return key.StartsWith(claimType + KEY_SEPARATOR);
    }
}