﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Domain.Models;

public class ImportSelection : VigiLitEntityBase
{
    public int UserId { get; set; }
    public int ImportId { get; set; }

    public User User { get; set; }
    public Import Import { get; set; }

    private ImportSelection()
    {
    }

    public ImportSelection(int userId, int importId)
    {
        UserId = userId;
        ImportId = importId;
    }
}
