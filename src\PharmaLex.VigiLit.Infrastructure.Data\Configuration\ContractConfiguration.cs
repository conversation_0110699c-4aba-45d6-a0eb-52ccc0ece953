using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ContractConfiguration : EntityBaseMap<Contract>
{
    public override void Configure(EntityTypeBuilder<Contract> builder)
    {        
        base.Configure(builder);

        builder.ToTable("Contracts");

        builder.HasOne(c => c.Substance).WithMany().HasForeignKey(c => c.SubstanceId);
        builder.HasOne(c => c.Project).WithMany(p => p.ContractsInternal).HasForeignKey(c => c.ProjectId);

        builder.HasMany(c => c.ContractVersionsInternal).WithOne(cs => cs.Contract).HasForeignKey(cs => cs.ContractId);
        builder.Ignore(c => c.ContractVersions);

        builder.Property(ra => ra.ContractStartDate)
            .HasDefaultValueSql("GetUTCDate()");
    }
}
