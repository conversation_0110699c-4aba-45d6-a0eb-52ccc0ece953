﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="PharmaLex.DataAccess" Version="8.0.0.267" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.VigiLit.AccessControl\PharmaLex.VigiLit.AccessControl.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ReferenceManagement.Contracts\PharmaLex.VigiLit.ReferenceManagement.Contracts.csproj" />
  </ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.ReferenceManagement.Service.Integration.Tests" />
		<InternalsVisibleTo Include="PharmaLex.VigiLit.Infrastructure.Data.Integration.Tests" />
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="PharmaLex.VigiLit.ReferenceManagement.Service.Unit.Tests"></InternalsVisibleTo>
		<InternalsVisibleTo Include="DynamicProxyGenAssembly2" />
	</ItemGroup>

</Project>
