﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.UserManagement;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class EmailMessage : VigiLitEntityBase
{
    public int CompanyId { get; set; }
    public Company Company { get; set; }

    public int EmailId { get; set; }
    public Email Email { get; set; }

    public int UserId { get; set; }
    public User User { get; set; }

    public string EmailAddress { get; set; }
    public string Subject { get; set; }
    public EmailMessageStatusType EmailMessageStatusType { get; set; }
    public DateTime Timestamp { get; set; }

    public ICollection<EmailMessageRelevantEvent> EmailMessageRelevantEvents { get; set; } = new List<EmailMessageRelevantEvent>();
}
