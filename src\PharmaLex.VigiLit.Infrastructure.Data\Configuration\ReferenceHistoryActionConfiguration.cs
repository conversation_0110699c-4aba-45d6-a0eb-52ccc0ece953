﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ReferenceHistoryActionConfiguration : EntityBaseMap<ReferenceHistoryAction>
{
    public override void Configure(EntityTypeBuilder<ReferenceHistoryAction> builder)
    {
        base.Configure(builder);

        builder.ToTable("ReferenceHistoryActions");
        builder.Property(ra => ra.TimeStamp)
            .HasDefaultValueSql("getutcdate()");

        builder.HasOne(ra => ra.ReferenceClassification)
            .WithMany(rf => rf.ReferenceHistoryActions)
            .HasForeignKey(ra => ra.ReferenceClassificationId)
            .IsRequired(false);

        builder.HasOne(ra => ra.User)
            .WithMany()
            .HasForeignKey(ra => ra.UserId)
            .IsRequired(false);
    }

}