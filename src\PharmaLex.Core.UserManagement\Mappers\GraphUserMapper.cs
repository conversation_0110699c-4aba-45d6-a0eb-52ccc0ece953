﻿using AutoMapper;
using PharmaLex.Core.UserManagement.Users;

namespace PharmaLex.Core.UserManagement.Mappers;

internal class GraphUserMapper : Profile
{
    public GraphUserMapper()
    {
        CreateMap<Microsoft.Graph.Models.User, UserFindResult>()
            .ForMember(um => um.Label, u => u.MapFrom(x => $"{x.GivenName} {x.Surname} ({x.UserPrincipalName})"))
            .ForMember(um => um.GivenName, u => u.MapFrom(x => x.GivenName))
            .ForMember(um => um.Email, u => u.MapFrom(x => x.UserPrincipalName))
            .ForMember(um => um.Value, u => u.MapFrom("UserPrincipalName"))
            .ForMember(um => um.FamilyName, u => u.MapFrom("Surname"))
            .ForMember(um => um.Id, u => u.MapFrom(x => 0));
    }
}
