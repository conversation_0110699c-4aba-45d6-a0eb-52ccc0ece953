﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using PharmaLex.DataAccess.Design;

namespace PharmaLex.VigiLit.DataAccessLayer;

public class DesignTimeContextFactory : IDesignTimeDbContextFactory<VigiLitDbContext>
{
    public VigiLitDbContext CreateDbContext(string[] args)
    {
        var context = new DesignPlxDbContextResolver<VigiLitDbContext>
                (Path.Combine(Directory.GetCurrentDirectory().Replace(".DataAccessLayer", ".Web"), "appSettings.json"))
            .ServiceProvider.GetService(typeof(VigiLitDbContext)) as VigiLitDbContext;

        if (context == null)
        {
            throw new ArgumentException("context cannot be null");
        }

        // Allow migrations to run for 30 mins instead of 30 seconds.
        context.Database.SetCommandTimeout((int)TimeSpan.FromMinutes(30).TotalSeconds);

        return context;
    }
}