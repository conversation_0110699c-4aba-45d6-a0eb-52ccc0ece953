﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;

namespace PharmaLex.VigiLit.Domain.Models;

public class EmailRelevantEvent : VigiLitEntityBase
{
    public int CompanyInterestId { get; set; }
    public EmailRelevantEventActionType EmailRelevantEventActionType { get; set; }
    public int? ClassificationCategoryId { get; set; }
    public EmailRelevantEventEmailStatusType EmailRelevantEventEmailStatusType { get; set; }
    public DateTime? EmailSentDate { get; set; }
    public EmailReason EmailReason { get; set; }

    public CompanyInterest CompanyInterest { get; set; }
    public ClassificationCategory ClassificationCategory { get; set; }
}
