﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface IEmailLogService
{
    Task<Email> CreateAndSaveEmailLog(EmailType emailType, EmailTriggerType emailTriggerType);
    Task LogOutcome(Email? email, EmailStatusType status, int sentEmailsCount, int failedEmailsCount);
    Task<IEnumerable<EmailModel>> GetEmailLog();
    Task<IEnumerable<EmailMessageModel>> GetEmailMessagesLog(int emailId);
    Task<EmailMessage> GetEmailMessage(int emailMessageId);
    Task<IEnumerable<EmailMessageClassificationModel>> GetEmailMessageClassifications(int emailMessageId);
}
