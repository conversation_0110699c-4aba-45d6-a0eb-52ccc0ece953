﻿namespace PharmaLex.VigiLit.ImportManagement.Ui.Models;
public class ImportQueuePageModel
{
    /// <summary>
    /// Gets or sets the import display cards.
    /// </summary>
    /// <value>
    /// The import display cards.
    /// </value>
    public required IEnumerable<ImportDisplayCard> ImportDisplayCards { get; set; }

    /// <summary>
    /// Gets or sets a value indicating whether [to open the manual entry modal] when loading the page.
    /// </summary>
    /// <value>
    ///   <c>true</c> if [to open manual entry modal]; otherwise, <c>false</c>.
    /// </value>
    public bool DoOpenManualEntryModal { get; set; }
}