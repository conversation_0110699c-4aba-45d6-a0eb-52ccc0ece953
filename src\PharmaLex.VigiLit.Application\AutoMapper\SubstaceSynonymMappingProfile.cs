﻿using AutoMapper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class SubstaceSynonymMappingProfile :Profile 
{
    public SubstaceSynonymMappingProfile() 
    {
        CreateMap<SubstanceSynonym, SubstanceSynonymModel>();
        CreateMap<SubstanceSynonym, SubstanceSynonymModel>().ReverseMap();
    }
}