﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CompanyUserConfiguration : EntityBaseMap<CompanyUser>
{
    public override void Configure(EntityTypeBuilder<CompanyUser> builder)
    {
        base.Configure(builder);

        builder.ToTable("CompanyUsers");

        builder.Property(ra => ra.CompanyUserType)
            .HasDefaultValue(CompanyUserType.External)
            .HasConversion<int>();

        builder.<PERSON><PERSON><PERSON>(e => e.Id);

        builder.HasOne(sc => sc.User)
            .WithOne(s => s.CompanyUser);

        builder.HasOne(sc => sc.Company)
            .WithMany(s => s.CompanyUsers)
            .HasForeignKey(s => s.CompanyId);

        builder.HasIndex(e => new { e.CompanyId, e.UserId });

    }

}