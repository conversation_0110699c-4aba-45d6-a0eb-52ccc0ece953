﻿namespace PharmaLex.VigiLit.ImportManagement.Ui.FileImport;

public class ImportFileModel
{
    public int Id { get; set; }
    public required string FileName { get; set; }
    public Guid BatchId { get; set; }
    public int FileSize { get; set; }
    public string? CreatedBy { get; init; }
    public string? LastUpdatedBy { get; init; }
    public DateTime CreatedDate { get; set; }
    public DateTime LastUpdatedDate { get; set; }
    public required string FileHash { get; set; }

    public ImportFileModel(string fileName, Guid batchId, string fileHash)
    {
        FileName = fileName;
        BatchId = batchId;
        FileHash = fileHash;
    }
}