﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Reporting.Contracts.Models;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class Report : VigiLitEntityBase, IReportEntity
{
    public string Name { get; set; }
    public string Description { get; set; }
    public string ControllerName { get; set; }
    public bool AllCompanies { get; set; }
    public string Claims { get; set; }
    public ICollection<CompanyReport> CompanyReports { get; set; } = new List<CompanyReport>();
    public Report()
    {
    }
}
