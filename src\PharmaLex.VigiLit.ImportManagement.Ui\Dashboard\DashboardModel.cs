﻿namespace PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

public class DashboardModel
{
    public bool DisplayEmailDashboard { get; set; }
    public required CompanyEmailStats Total { get; set; }
    public required List<DashboardCompanySummary> CompanySummary { get; set; }

    public bool DisplayImportsDashboard { get; set; }
    public IEnumerable<ImportDashboardModel> Imports { get; set; } = new List<ImportDashboardModel>();
    public int SelectedImportId { get; set; }
}