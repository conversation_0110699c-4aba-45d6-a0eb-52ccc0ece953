﻿namespace PharmaLex.VigiLit.ImportManagement.Contracts;

/// <summary>
/// This is the entry point into PubMed importing and should be moved out into the "PharmaLex.VigiLit.Aggregators.PubMed" project
/// A common interface needs to added to start any aggregator.
/// </summary>
public interface IImportProcessingService
{
    Task<ImportProcessingParams> ImportProcessingActivity(ImportProcessingParams input);
    Task<ImportProcessingParams> GetNextImportProcessingParams();
}
