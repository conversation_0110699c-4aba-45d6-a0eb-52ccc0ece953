. "..\..\Helpers\DaemonFunctions.ps1"

Write-Host Starting Portainer -Foreground Green 
Write-Host -Foreground White 
SwitchDaemon "linux"
docker volume create portainer_data 
docker stop portainer 
docker rm portainer 
# To configure Docker and <PERSON><PERSON><PERSON> to manage Linux and Windows containers see: 
# https://tobiasfenster.io/running-linux-and-windows-containers-at-the-same-time-on-windows-10 
# Starts Portainer in Linux 
docker run -d -p 9000:9000 --name=portainer --restart=always -v /var/run/docker.sock:/var/run/docker.sock -v portainer_data:/data portainer/portainer-ce:latest 
SwitchDaemon "windows"
docker stop portainer_agent 
docker rm portainer_agent 
docker run -d --name portainer_agent --restart=always -v \\.\pipe\docker_engine_windows:\\.\pipe\docker_engine portainer/agent 
Write-Host Use the following IP address to configure an environment in Portainer. Choose Docker Standalone and use port 9001 
docker inspect -f '{{range.NetworkSettings.Networks}}{{.IPAddress}}{{end}}' portainer_agent 
Write-Host Completed. -Foreground Green 