﻿using Azure.Storage.Blobs;

namespace PharmaLex.VigiLit.ContractManagement.Service;
public interface IContractDocumentService
{
    Task<BlobClient> Create(ContractDocumentDescriptor contractUploadDocumentDescriptor, Stream stream, CancellationToken cancellationToken = default);
    Task<Stream> OpenRead(ContractDocumentDescriptor contractUploadDocumentDescriptor, CancellationToken cancellationToken = default);
}
