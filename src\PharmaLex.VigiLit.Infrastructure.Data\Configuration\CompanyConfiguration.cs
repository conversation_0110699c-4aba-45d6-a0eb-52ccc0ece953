using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CompanyConfiguration : EntityBaseMap<Company>
{
    public override void Configure(EntityTypeBuilder<Company> builder)
    {
        base.Configure(builder);

        builder.ToTable("Companies");

        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.Name).IsRequired().HasMaxLength(250);

        builder.HasIndex(e => e.Name).IsUnique();

        builder.HasIndex(e => e.IsActive);

        builder.HasMany(c => c.Projects)
            .WithOne(p => p.Company)
            .HasForeignKey(p => p.CompanyId)
            .IsRequired();

        builder.HasMany(c => c.CompanyUsers)
            .WithOne(p => p.Company)
            .HasForeignKey(p => p.CompanyId)
            .IsRequired();

        builder.Property(e => e.Contact<PERSON>)
            .IsRequired(false)
            .HasMaxLength(250);

        builder.Property(e => e.ContactPersonEmail)
            .IsRequired(false)
            .HasMaxLength(256);
    }
}