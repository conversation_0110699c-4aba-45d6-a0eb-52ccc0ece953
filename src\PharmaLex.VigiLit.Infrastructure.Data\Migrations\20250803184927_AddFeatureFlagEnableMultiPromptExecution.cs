﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddFeatureFlagEnableMultiPromptExecution : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(
                "INSERT INTO [dbo].[FeatureFlags] ( [Name], [Enabled], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) " +
                "SELECT 'EnableMultiPromptExecution', 0, GETUTCDATE(), 'Initial Script', GETUTCDATE(), 'Initial Script' " +
                "WHERE NOT EXISTS (SELECT [Name] FROM [dbo].[FeatureFlags] WHERE [Name] = 'EnableMultiPromptExecution'); ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM [FeatureFlags] WHERE [Name] = 'EnableMultiPromptExecution';");
        }
    }
}
