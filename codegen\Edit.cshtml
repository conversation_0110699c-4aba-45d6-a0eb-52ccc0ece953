{{#if Dependencies}}{{#each Dependencies}}@using {{this}};
{{/each}}{{/if}}
@model {{EntityName}}Model

@{
    ViewData["Title"] = "{{EntityNamePlural}}";
}

<div class="sub-header">
    @if (Model.Id == default)
    {
        <h2 class="brand-color">Create {{EntityName}}</h2> }
    else
    {
        <h2 class="brand-color">Edit {{EntityName}}</h2>
    }

    <div class="controls">
        <a href="/{{EntityNamePlural}}" class="button secondary icon-button-back">Back to {{EntityName}} list</a>
    </div>
</div>

<form method="post" class="gapped-2">
    <div class="flex gapped-3">
        <section class="text-left flex-grow-1">
            <h2 class="underline-block text-left">Details</h2>

            {{#if Properties}}{{#each Properties}}
            <div class="form-group">
                <label>{{this.Name}}</label>
                <input asp-for="{{this.Name}}" type="text" />
            </div>
            {{/each}}{{/if}}

        </section>        
    </div>

    <div class="buttons">
        <a class="button secondary icon-button-cancel" href="/{{EntityNamePlural}}">Cancel</a>
        <button asp-action="@(Model.Id == 0 ? "Create": "Edit" )" type="submit" class="button icon-button-save">Save</button>
    </div>

</form>
