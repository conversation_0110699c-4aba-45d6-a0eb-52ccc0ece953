﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;

namespace PharmaLex.VigiLit.ImportManagement.Ui.FileImport;
public interface IImportFileRepository : ITrackingRepository<ImportFile>
{
    Task<IEnumerable<ImportFile>> GetImportFilesByBatchId(Guid batchId);

    Task<IEnumerable<ImportFile>> GetAll();


    /// <summary>
    /// Retrieves the import file associated with the specified file name and batch ID.
    /// </summary>
    /// <param name="fileName">The name of the file to retrieve.</param>
    /// <param name="batchId">The unique identifier for the batch associated with the import file.</param>
    /// <returns>A <see cref="Task{ImportFile}"/> representing the asynchronous operation, with the import file as its result.</returns>
    /// <exception cref="KeyNotFoundException">Thrown when no matching import file is found for the provided file name and batch ID.</exception>
    /// <remarks>
    /// This method will never return null. If no matching import file is found, a <see cref="KeyNotFoundException"/> will be thrown.
    /// </remarks>
    Task<ImportFile> GetImportFile(string fileName, Guid batchId);
}
