﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Data.Repositories;

public class CaseCompanyRepository : TrackingGenericRepository<CaseCompanies>, ICaseCompanyRepository
{

    public CaseCompanyRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
    {
    }
}