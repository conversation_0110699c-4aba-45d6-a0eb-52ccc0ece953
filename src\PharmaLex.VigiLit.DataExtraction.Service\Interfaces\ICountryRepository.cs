﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.DataExtraction.Service.Interfaces
{
    public interface ICountryRepository : ITrackingRepository<Country>
    {
        /// <summary>
        /// Asynchronously retrieves the names of all the Countries.
        /// </summary>
        /// <returns>
        /// A task that represents the asynchronous operation. The task result contains a collection of strings.
        /// </returns>
        Task<IEnumerable<string>> GetNames();
    }
}
