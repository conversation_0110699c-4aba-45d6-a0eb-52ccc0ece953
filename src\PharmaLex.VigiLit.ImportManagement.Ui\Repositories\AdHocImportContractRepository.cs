using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public class AdHocImportContractRepository : TrackingGenericRepository<AdHocImportContract>, IAdHocImportContractRepository
{
    protected readonly IMapper _mapper;

    public AdHocImportContractRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<AdHocImportContractModel>> GetForListDetails(int adHocImportId)
    {
        var query = context.Set<AdHocImportContract>()
            .Include(c => c.Contract)
                .ThenInclude(c => c.ContractVersionsInternal)
            .Include(ic => ic.Contract)
                .ThenInclude(c => c.Substance)
            .Include(ic => ic.Contract)
                .ThenInclude(c => c.Project)
            .Include(ic => ic.Contract)
                .ThenInclude(c => c.Project)
                    .ThenInclude(p => p.Company)
            .Where(ic => ic.AdHocImportId == adHocImportId && ic.Contract.CurrentContractVersionId != 0)
            .OrderBy(x => x.Contract.Project.Company.Name)
                .ThenBy(x => x.Contract.Project.Name)
                .ThenBy(x => x.Contract.Substance.Name)
            .AsSplitQuery()
            .AsNoTracking();

        return await _mapper.ProjectTo<AdHocImportContractModel>(query).ToListAsync();
    }
}
