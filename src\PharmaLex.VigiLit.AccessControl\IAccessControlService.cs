﻿namespace PharmaLex.VigiLit.AccessControl;
/// <summary>
/// Represents a service for access control management.
/// </summary>
public interface IAccessControlService
{
    /// <summary>
    /// Checks whether the specified context has the required permission.
    /// </summary>
    /// <typeparam name="T">The type of context implementing <see cref="IAccessControlContext"/>.</typeparam>
    /// <param name="context">The context for which permission needs to be checked.</param>
    /// <returns>True if the context has the required permission; otherwise, false.</returns>
    Task<bool> HasPermission<T>(T context) where T : IAccessControlContext;
}
