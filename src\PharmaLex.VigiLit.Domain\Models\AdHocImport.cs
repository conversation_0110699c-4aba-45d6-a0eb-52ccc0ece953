﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using System.Collections.Generic;

namespace PharmaLex.VigiLit.Domain.Models;

public class AdHocImport : VigiLitEntityBase
{
    public AdHocImportStatusType AdHocImportStatusType { get; set; }

    public DateTime? SourceSearchStartDate { get; set; }
    public DateTime? SourceSearchEndDate { get; set; }

    public ICollection<AdHocImportContract> AdHocImportContracts { get; set; } = new List<AdHocImportContract>();

    private AdHocImport()
    {
    }

    public AdHocImport(DateTime sourceSearchStartDate, DateTime sourceSearchEndDate)
    {
        AdHocImportStatusType = AdHocImportStatusType.New;

        SourceSearchStartDate = sourceSearchStartDate;
        SourceSearchEndDate = sourceSearchEndDate;
    }
}
