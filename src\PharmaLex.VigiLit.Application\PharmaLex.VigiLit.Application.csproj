﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
	  <Compile Remove="Interfaces\Services\DataMigration\**" />
	  <Compile Remove="Services\DataMigration\**" />
	  <EmbeddedResource Remove="Interfaces\Services\DataMigration\**" />
	  <EmbeddedResource Remove="Services\DataMigration\**" />
	  <None Remove="Interfaces\Services\DataMigration\**" />
	  <None Remove="Services\DataMigration\**" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="MediatR" Version="13.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.Hosting.Abstractions" Version="2.3.0" />
		<PackageReference Include="Microsoft.FeatureManagement.AspNetCore" Version="4.2.1" />
		<PackageReference Include="Microsoft.Graph" Version="5.87.0" />
		<PackageReference Include="PharmaLex.Authentication.B2C" Version="*********" />
		<PackageReference Include="PharmaLex.Caching.Data" Version="*********" />
		<PackageReference Include="PharmaLex.Helpers" Version="*********" />
		<PackageReference Include="TimeZoneConverter" Version="7.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.Core.UserManagement\PharmaLex.Core.UserManagement.csproj" />
		<ProjectReference Include="..\PharmaLex.Core.UserSessionManagement\PharmaLex.Core.UserSessionManagement.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.ContractManagement.Ui\PharmaLex.VigiLit.ContractManagement.Ui.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Entities\PharmaLex.VigiLit.DataExtraction.Entities.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain.Interfaces\PharmaLex.VigiLit.Domain.Interfaces.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Ui\PharmaLex.VigiLit.ImportManagement.Ui.csproj" />
	</ItemGroup>

</Project>
