using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface ISubstanceRepository : ITrackingRepository<Substance>
{
    Task<IEnumerable<SubstanceModel>> GetAllAsync();
    Task<Substance?> GetByIdAsync(int id);
    Task<Substance?> GetByNameAsync(string name);
    Task<Substance?> GetByNameAndTypeAsync(string name, string type);
    Task<IEnumerable<Substance>> GetRangeByIdsAsync(IEnumerable<int> substanceIds);
    Task AddNewSynonym(Substance substance, IEnumerable<SubstanceSynonym> substanceSynonyms);
    Task<IEnumerable<SubstanceItemModel>> GetForSearch(User user);
    Task<IEnumerable<SubstanceItemModel>> GetActiveSubstancesWithNoClassificationsForReference(int referenceId);

    /// <summary>
    /// Gets the specified substance.
    /// </summary>
    /// <param name="id">The substance identifier.</param>
    /// <summary>Throws exception is not found</summary>
    /// <returns></returns>
    Task<Substance> Get(int id);
}