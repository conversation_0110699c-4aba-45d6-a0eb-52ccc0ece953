﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <AzureFunctionsVersion>v4</AzureFunctionsVersion>
    <OutputType>Exe</OutputType>
	  <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="newrelic\newrelic.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="newrelic\newrelic.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <CopyToPublishDirectory>Always</CopyToPublishDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
	  <PackageReference Include="AutoMapper" Version="14.0.0" />
	  <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Extensions" Version="1.1.0" />
	  <PackageReference Include="Microsoft.Extensions.Azure" Version="1.12.0" />
	  <FrameworkReference Include="Microsoft.AspNetCore.App" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker" Version="1.23.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Sdk" Version="2.0.5" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="1.3.2" />
	  <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.23.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="1.4.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Timer" Version="4.3.1" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.DurableTask" Version="1.6.0" />
	  <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http" Version="3.3.0" />
	  <PackageReference Include="Microsoft.Identity.Client" Version="4.74.1" />
	  <PackageReference Include="Microsoft.Identity.Client.Extensions.Msal" Version="4.74.1" />
	  <PackageReference Include="NewRelic.Agent" Version="10.43.0" />
	  <PackageReference Include="NewRelic.Agent.Api" Version="10.43.0" />
	  <PackageReference Include="NLog" Version="6.0.2" />
	  <PackageReference Include="NLog.Web.AspNetCore" Version="6.0.2" />
	  <PackageReference Include="PharmaLex.FeatureManagement" Version="8.0.0.110" />
	  <PackageReference Include="PharmaLex.FeatureManagement.Entities" Version="8.0.0.110" />
	  <PackageReference Include="System.Text.Json" Version="9.0.7" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\PharmaLex.Core.Configuration\PharmaLex.Core.Configuration.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Application.Services\PharmaLex.VigiLit.Application.Services.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Data.Repositories\PharmaLex.VigiLit.Data.Repositories.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Service\PharmaLex.VigiLit.DataExtraction.Service.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Service\PharmaLex.VigiLit.ImportManagement.Service.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Infrastructure.Import.PubMed\PharmaLex.VigiLit.Infrastructure.Import.PubMed.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Infrastructure\PharmaLex.VigiLit.Infrastructure.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Reporting\PharmaLex.VigiLit.Reporting.csproj" />
    <ProjectReference Include="..\PharmaLex.VigiLit.Reports.TrackingSheets.Domain\PharmaLex.VigiLit.Reports.TrackingSheets.Domain.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="appSettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="host.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="local.settings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
    <None Update="Properties\launchSettings.json">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
	    <CopyToPublishDirectory>Never</CopyToPublishDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
	<Using Include="System.Threading.ExecutionContext" Alias="ExecutionContext" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Tools\" />
  </ItemGroup>

	<PropertyGroup>
		<UserSecretsId>PharmaLex.VigiLit</UserSecretsId>
	</PropertyGroup>

	<PropertyGroup>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
	</PropertyGroup>

</Project>
