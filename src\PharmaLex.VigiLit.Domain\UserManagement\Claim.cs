﻿using PharmaLex.Core.UserManagement.Claims;
using PharmaLex.VigiLit.DataAccessLayer.Base;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.VigiLit.Domain.UserManagement;

public class Claim : VigiLitEntityBase, IClaimEntity
{
    public string Name { get; protected set; }

    public string DisplayName { get; set; }

    public string ClaimType { get; set; }

    protected internal ICollection<User> UsersInternal { get; set; }

    protected Claim()
    {
        UsersInternal = new HashSet<User>();
    }

    [NotMapped]
    public string Key => $"{this.ClaimType}:{this.Name}";
}