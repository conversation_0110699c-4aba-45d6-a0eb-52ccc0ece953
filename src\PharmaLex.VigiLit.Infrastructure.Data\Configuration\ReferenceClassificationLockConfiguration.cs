using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ReferenceClassificationLockConfiguration : EntityBaseMap<ReferenceClassificationLock>
{
    public override void Configure(EntityTypeBuilder<ReferenceClassificationLock> builder)
    {
        base.Configure(builder);

        builder.ToTable("ReferenceClassificationLocks");

        // Don't like this but can't change it without dropping columns and re-creating them.
        // TODO: If you rename this table, fix this at the same time.
        // The Id column always has value 0
        builder.HasKey(t => new { t.ReferenceClassificationId, t.UserId });
        
        builder.HasIndex(p => new { p.ReferenceClassificationId }).IsUnique();
    }
}
