﻿using PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

namespace PharmaLex.VigiLit.DataExtraction.Service.Data;

public class ExtractedReference
{
    [Mandatory]
    public required Title Title { get; set; }
    [Mandatory]
    public required Abstract Abstract { get; set; }
    public required Author[] Authors { get; set; }
    public required Affiliation[] Affiliations { get; set; }
    [Mandatory]
    public required JournalTitle JournalTitle { get; set; }
    public required Doi Doi { get; set; }
    public required IssueNumber IssueNumber { get; set; }
    public required Volume Volume { get; set; }
    public required Issn Issn { get; set; }
    public required Year Year { get; set; }
    public required Pages Pages { get; set; }
    [Mandatory]
    public required CountryOfOccurrence CountryOfOccurrence { get; set; }
    public required Keywords Keywords { get; set; }
}