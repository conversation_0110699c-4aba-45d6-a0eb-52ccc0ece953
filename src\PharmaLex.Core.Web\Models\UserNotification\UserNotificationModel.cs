﻿using PharmaLex.Core.Web.Enums;

namespace PharmaLex.Core.Web.Models.UserNotification;

public class UserNotificationModel
{
    public string Html { get; set; }
    public int Position { get; set; }
    public string Type { get; set; }
    public int? Number { get; set; }
    public int Duration { get; set; }

    public UserNotificationModel(string html,
        UserNotificationPosition position = UserNotificationPosition.TopCenter,
        UserNotificationType type = UserNotificationType.Info, int duration = 2500, int? number = null)
    {
        Html = html;
        Position = (int)position;
        Type = type.ToString().ToLower();
        Duration = duration;
        Number = number;
    }
}