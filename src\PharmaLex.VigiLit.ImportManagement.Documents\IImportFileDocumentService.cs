﻿using PharmaLex.BlobStorage.Descriptors;
namespace PharmaLex.VigiLit.ImportManagement.Documents;
public interface IImportFileDocumentService
{
    Task Create(ImportFileUploadDescriptor importFileUploadDescriptor, Stream stream, CancellationToken cancellationToken = default);
    Task<DocumentProperties> GetDocumentProperties(ImportFileUploadDescriptor importFileUploadDescriptor, CancellationToken cancellationToken = default);
    Task Delete(ImportFileUploadDescriptor importFileUploadDescriptor, CancellationToken cancellationToken = default);
    Task<bool> Exists(ImportFileUploadDescriptor importFileUploadDescriptor, CancellationToken cancellationToken = default);
    Task<Stream> OpenRead<T>(T importFileDescriptor, CancellationToken cancellationToken = default);
    Task<DocumentProperties> Copy(ImportFileUploadDescriptor importFileUploadDescriptor, ImportFileDescriptor importFileDescriptor, CancellationToken cancellationToken = default);
}
