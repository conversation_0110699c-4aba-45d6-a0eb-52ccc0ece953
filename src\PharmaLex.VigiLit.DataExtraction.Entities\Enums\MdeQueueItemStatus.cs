﻿namespace PharmaLex.VigiLit.DataExtraction.Entities.Enums;

public enum MdeQueueItemStatus
{
    None = 0,

    /// <summary>
    /// Queued for sending request to Phlex Vision.
    /// </summary>
    Queued = 1,

    /// <summary>
    /// Being processed by Phlex Vision.
    /// </summary>
    Processing = 2,

    /// <summary>
    /// Phlex Vision has completed successfully.
    /// </summary>
    Completed = 3,

    /// <summary>
    /// Phlex Vision has returned an error.
    /// </summary>
    Errored = 4,
}