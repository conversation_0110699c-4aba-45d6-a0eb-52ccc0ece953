﻿using PharmaLex.VigiLit.ImportManagement.Client;

namespace PharmaLex.VigiLit.ImportManagement.Service.Services;
public interface IManualCorrectionCommandHandler
{
    /// <summary>
    /// Consumes the specified command which causes the given reference to be available for manual correction.
    /// </summary>
    /// <param name="command">The command.</param>
    Task Consume(ManualCorrectionCommand command);
}
