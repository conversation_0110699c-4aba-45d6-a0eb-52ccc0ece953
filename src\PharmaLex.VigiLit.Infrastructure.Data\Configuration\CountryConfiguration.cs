using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CountryConfiguration : EntityBaseMap<Country>
{
    public override void Configure(EntityTypeBuilder<Country> builder)
    {
        base.Configure(builder);

        builder.ToTable("Countries");

        builder.HasIndex(c => c.Name).IsUnique();

        builder.HasIndex(c => c.Iso).IsUnique();

        builder.Property(c => c.Name)
            .HasColumnType("nvarchar")
            .HasMaxLength(128)
            .IsRequired();

        builder.Property(c => c.Iso)
            .HasColumnType("nvarchar")
            .HasMaxLength(2)
            .IsRequired();
    }
}
