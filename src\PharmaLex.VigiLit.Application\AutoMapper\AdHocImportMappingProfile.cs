using AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.AdHoc;
using System.Linq;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class AdHocImportMappingProfile : Profile
{
    public AdHocImportMappingProfile()
    {
        CreateMap<AdHocImport, AdHocImportModel>()
            .ForMember(d => d.SourceSearchStartDate, s => s.MapFrom(x => x.SourceSearchStartDate.HasValue ? x.SourceSearchStartDate.Value.ToString("dd MMM yyyy") : string.Empty))
            .ForMember(d => d.SourceSearchEndDate, s => s.MapFrom(x => x.SourceSearchEndDate.HasValue ? x.SourceSearchEndDate.Value.ToString("dd MMM yyyy") : string.Empty))
            .ForMember(d => d.ContractsCount, s => s.MapFrom(x => x.AdHocImportContracts.Select(x => x.ContractId).Count()));
    }
}
