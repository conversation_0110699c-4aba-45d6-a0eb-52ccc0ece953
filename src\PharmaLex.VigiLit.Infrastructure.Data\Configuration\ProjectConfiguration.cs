using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ProjectConfiguration : EntityBaseMap<Project>
{
    public override void Configure(EntityTypeBuilder<Project> builder)
    {
        base.Configure(builder);

        builder.ToTable("Projects");

        builder.Property(p => p.Name).IsRequired().HasMaxLength(250);

        builder.HasIndex(p => new { p.CompanyId, p.Name }).IsUnique();

        builder.Ignore(p => p.Contracts);
    }
}