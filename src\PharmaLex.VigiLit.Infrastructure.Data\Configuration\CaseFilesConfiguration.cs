﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CaseFilesConfiguration : EntityBaseMap<CaseFiles>
{
    public override void Configure(EntityTypeBuilder<CaseFiles> builder)
    {
        base.Configure(builder);

        builder.ToTable("CaseFiles");

        builder.HasKey(e => e.Id);
    }
}