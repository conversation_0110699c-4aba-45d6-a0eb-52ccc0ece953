do { 
	Write-Host Local Setup Menu -ForegroundColor DarkCyan 
	Write-Host ================ -ForegroundColor DarkCyan 
	Write-Host A. -NoNewLine -ForegroundColor Cyan 
	Write-Host Setup local environment
	Write-Host [Backspace].  -NoNewLine -ForegroundColor Cyan 
	Write-Host '<== Back to Main Menu' 
	
	$key = [Console]::ReadKey($true) 
	
	$keyChar = $key.KeyChar 
	
	if ($keyChar -eq 'a') 
	{ 
		.\setup.ps1
	} 

} until($key.Key -eq 'Backspace' )  
