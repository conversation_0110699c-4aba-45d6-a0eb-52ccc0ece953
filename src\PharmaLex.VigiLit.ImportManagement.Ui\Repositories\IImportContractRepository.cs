using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public interface IImportContractRepository : ITrackingRepository<ImportContract> // Used by ImportQueueService
{
    Task<IEnumerable<ImportContractModel>> GetImportContractsLog(int importId);
    Task<ImportContract?> GetLastScheduledLogEntryForContract(int contractId, int days);
}