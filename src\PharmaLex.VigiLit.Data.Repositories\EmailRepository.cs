using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Data.Repositories;

public class EmailRepository : TrackingGenericRepository<Email>, IEmailRepository
{
    private readonly IMapper _mapper;

    public EmailRepository(PlxDbContext context, IMapper mapper, IUserContext userContext)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<EmailModel>> GetEmailLog(int days)
    {
        var query = context.Set<Email>()
            .Where(i => i.StartDate >= DateTime.UtcNow.AddDays(days * -1))
            .OrderByDescending(e => e.Id)
            .AsNoTracking();

        return await _mapper.ProjectTo<EmailModel>(query).ToListAsync();
    }

    public async Task<Email?> GetEmail(int emailId)
    {
        return await context.Set<Email>()
            .Where(i => i.Id == emailId)
            .FirstOrDefaultAsync();
    }

    public void ClearChangeTracker()
    {
        context.ChangeTracker.Clear();
    }
}
