﻿using System.Text;
using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;
internal class ExpressionBuilder : IExpressionBuilder
{
    public string Build(string expression)
    {
        var sbExpressionBuilder = new StringBuilder(expression);

        sbExpressionBuilder.Replace("\r\n", " ").Replace("\n", " ").Replace("\r", " ");
        sbExpressionBuilder.Replace("”", "\"").Replace("“", "\"");
        ReplaceAndOrOccurrence(sbExpressionBuilder);

        // Define regex for:
        // 1. Quoted phrase:       "some text"
        // 2. Unquoted phrase:     words without special chars (&|())
        // 3. Word with asterisk:  someword*
        // 4. Single word:         word
        var regex = new Regex(@"""([^""]+)""|([^""&|()]+)|(\w+\*)|(\w+)|&&!", RegexOptions.Compiled,
            TimeSpan.FromSeconds(10));

        return regex.Replace(sbExpressionBuilder.ToString(), TransformMatch);
    }

    private static void ReplaceAndOrOccurrence(StringBuilder sbExpressionBuilder)
    {
        var stringExpression = sbExpressionBuilder.ToString();
        var stringToReplace = stringExpression.Replace("\"AND ", "\" AND ", StringComparison.OrdinalIgnoreCase)
            .Replace("\"AND\"", "\" AND \"", StringComparison.OrdinalIgnoreCase)
            .Replace(" AND ", " && ", StringComparison.OrdinalIgnoreCase)
            .Replace("\"OR ", "\" OR ", StringComparison.OrdinalIgnoreCase)
            .Replace("\"OR\"", "\" OR \"", StringComparison.OrdinalIgnoreCase)
            .Replace(" OR\"", " OR \"", StringComparison.OrdinalIgnoreCase)
            .Replace(" NOT ", " &&! ", StringComparison.OrdinalIgnoreCase)
            .Replace(" OR ", " || ", StringComparison.OrdinalIgnoreCase);
        sbExpressionBuilder.Replace(stringExpression, stringToReplace);
    }

    private static string TransformMatch(Match match)
    {
        if (match.Groups[1].Success)
            return WrapWithFunction(match.Groups[1].Value); // Quoted phrase

        if (match.Groups[2].Success)
            return ProcessUnquotedWord(match.Groups[2].Value); // Unquoted phrase

        if (match.Groups[3].Success || match.Groups[4].Success)
            return WrapWithFunction(match.Value); // Wildcard or single word

        return match.Value;
    }

    private static string WrapWithFunction(string wordToWrap)
    {
        return $"IsWordInAbstract(\"{wordToWrap}\")";
    }

    private static string ProcessUnquotedWord(string word)
    {
        word = word.Trim();
        return string.IsNullOrWhiteSpace(word) || word == "(" || word == ")"
            ? word
            : WrapWithFunction(word);
    }
}
