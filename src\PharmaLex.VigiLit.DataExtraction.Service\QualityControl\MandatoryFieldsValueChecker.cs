﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.DataExtraction.Service.Data;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using System.Reflection;

namespace PharmaLex.VigiLit.DataExtraction.Service.QualityControl;

internal class MandatoryFieldsValueChecker : IExtractionValidator
{
    private readonly List<PropertyInfo> _mandatoryFields = [];
    private readonly ILogger<MandatoryFieldsValueChecker> _logger;
    public MandatoryFieldsValueChecker(ILogger<MandatoryFieldsValueChecker> logger)
    {
        PopulatePropertyInfoCollections();
        _logger = logger;
    }

    public bool IsValid(ExtractedReference extractedReference)
    {
        foreach (var mandatoryField in _mandatoryFields)
        {
            var extractedProperty = mandatoryField.GetValue(extractedReference) as IExtractedProperty;
            if (extractedProperty is null || IsEmptyOrWhiteSpace(extractedProperty.Value))
            {
                _logger.LogInformation("Mandatory property: {mandatoryFieldName} is empty", mandatoryField.Name);
                return false;
            }
        }

        return true;
    }

    private static bool IsEmptyOrWhiteSpace(string value)
    {
        return value.All(char.IsWhiteSpace);
    }

    private void PopulatePropertyInfoCollections()
    {
        var properties = typeof(ExtractedReference).GetProperties();

        _mandatoryFields.AddRange(
            properties.Where(info => Attribute.IsDefined(info, typeof(MandatoryAttribute)))
        );
    }
}