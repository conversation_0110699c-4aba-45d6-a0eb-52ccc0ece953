Clear-Host 
do { 
	Write-Host Development Main Menu -ForegroundColor Green 
	Write-Host ===================== -ForegroundColor Green 
	Write-Host S. -NoNewLine -ForegroundColor Cyan 
	Write-Host Local Setup
	Write-Host I. -NoNewLine -ForegroundColor Cyan 
	Write-Host Infrastructure 
	Write-Host M. -NoNewLine -ForegroundColor Cyan 
	Write-Host Generate Migration Script
	Write-Host T. -NoNewLine -ForegroundColor Cyan 
	Write-Host Testing/QA  
	Write-Host X.  -NoNewLine -ForegroundColor Cyan 
	Write-Host Exit 

	$key = [Console]::ReadKey($true) 
  
	$keyChar = $key.KeyChar 
  
	if ($keyChar -eq 's') 
	{ 
		CD .\setup 
		.\menu.ps1 
		CD .. 
	} 
	if ($keyChar -eq 'i') 
	{ 
		CD .\Infrastructure 
		.\menu.ps1 
		CD .. 
	} 
	elseif ($keyChar -eq 'm') 
	{ 
		.\CreateSqlScripts.ps1 
	}
	elseif ($keyChar -eq 't') 
	{ 
		CD .\Testing 
		.\menu.ps1 
		CD .. 
	}

} until($key.KeyChar -eq 'x')  
