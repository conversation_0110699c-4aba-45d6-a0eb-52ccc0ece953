using System.Collections.Generic;
using System.Threading.Tasks;
{{#if Dependencies}}{{#each Dependencies}}using {{this}};
{{/each}}{{/if}}
namespace {{Namespace}}
{
    public interface I{{EntityName}}Service
    {
        Task<IEnumerable<{{EntityName}}Model>> GetAllAsync();
        Task<IEnumerable<{{EntityName}}HistoryModel>> GetHistory(int id);
        Task AddAsync({{EntityName}}Model model);
        Task UpdateAsync({{EntityName}}Model model);
        Task DeleteAsync(int id);
        Task<{{EntityName}}Model> GetByIdAsync(int id);
    }
}
