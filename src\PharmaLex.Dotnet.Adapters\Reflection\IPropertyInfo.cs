namespace PharmaLex.Dotnet.Adapters.Reflection;

public interface IPropertyInfo : IMemberInfo
{
    bool CanRead { get; }
    bool CanWrite { get; }
    bool IsSpecialName { get; }
    IType PropertyType { get; }

    IMethodInfo GetGetMethod();
    IMethodInfo GetGetMethod(bool nonPublic);
    IParameterInfo[] GetIndexParameters();
    IMethodInfo GetSetMethod();
    IMethodInfo GetSetMethod(bool nonPublic);
    void SetValue(object instance, object value, object[] index);
}