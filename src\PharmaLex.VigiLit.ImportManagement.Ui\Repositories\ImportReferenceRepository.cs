﻿using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
internal class ImportReferenceRepository : TrackingGenericRepository<ImportReference>, IImportReferenceRepository
{
    public ImportReferenceRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<ImportReference>> GetAllAsync()
    {
        return await context.Set<ImportReference>()
              .OrderByDescending(c => c.Id)
              .ToListAsync();
    }

    public async Task<ImportReference?> GetById(int id)
    {
        return await context.Set<ImportReference>()
            .Where(i => i.Id == id)
             .FirstOrDefaultAsync();
    }
}
