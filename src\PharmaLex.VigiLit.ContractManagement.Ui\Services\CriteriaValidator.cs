﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Logging;
using System.Collections.ObjectModel;
using Microsoft.Extensions.Configuration;
using System.Text;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Services;

internal class CriteriaValidator(ILogger<CriteriaValidator> logger, IConfiguration configuration) : ICriteriaValidator
{
    private const char OpeningTypographicQuote = '“';
    private const char ClosingTypographicQuote = '”';
    private const char NeutralDoubleQuote = '"';

    private readonly char[] _phraseAllowedSymbols = ['-', '(', ')', ',', '/', '\'', '\r', '\n', '+', '_', '.', '`', 'α', ' ']; // Check what iSearch lists as needs to be in here

    private readonly IEnumerable<string> _searchFieldTags = new List<string>()
    {
        "ad", "all", "aid", "au", "auid", "book", "dcom", "cois", "cn", "crdt", "rn", "ed", "edat", "sb", "1au", "fau",
        "fir", "gr", "ir", "isbn", "ip", "ta", "la",
        "lastau", "lid", "mhda", "majr", "sh", "mh", "lr", "jid", "ot", "pg", "ps", "pa", "pl", "pmid", "dp", "pt",
        "pubn", "si", "sb", "nm", "tw", "ti", "tiab", "tt", "vi"
    };

    private bool IsAnd() => GetNextNChars(4) == "AND ";
    private bool IsOr() => GetNextNChars(3) == "OR ";
    private bool IsNot() => GetNextNChars(4) == "NOT ";
    private bool IsWhiteSpace() => Char.IsWhiteSpace(_ch[0]);

    private bool IsNeutralDoubleQuote() => _ch[0] == NeutralDoubleQuote;
    private bool IsOpeningTypographicQuote() => _ch[0] == OpeningTypographicQuote;
    private bool IsClosingTypographicQuote() => _ch[0] == ClosingTypographicQuote;

    private bool IsOpeningQuote() => IsNeutralDoubleQuote() || IsOpeningTypographicQuote();
    private bool IsClosingQuote() => IsNeutralDoubleQuote() || IsClosingTypographicQuote();

    private bool IsExpressionNext() => IsOpenBracket() || IsValidSearchTermCharacter(_ch) || IsOpeningQuote();

    private bool IsOpenBracket() => _ch[0] == '(';
    private bool IsCloseBracket() => _ch[0] == ')';
    private bool IsOpenSearchFieldTag() => _ch[0] == '[';
    private bool IsCloseSearchFieldTag() => _ch[0] == ']';
    private bool IsWildcard() => _ch[0] == '*';
    private bool IsAdditionalChar() => _ch[0] == '-';
    private bool IsLogicalOperator() => IsAnd() || IsOr() || IsNot();


    private int HighlightStart() => StartOfHighlight(_characterPosition);
    private static int StartOfHighlight(int highlightStart) => Math.Max(highlightStart - 1, 0);

    private int HighlightEnd(int size) => EndOfHighlight(_characterPosition, size);
    private int EndOfHighlight(int highlightStart, int size) => Math.Min(highlightStart + size - 1, _criteria.Length - 1);

    private string _criteria = "";
    private int _characterPosition = 0;
    private string _ch = "";
    private List<SearchStringError> _errorMessages = [];
    private bool _logMessages;

    private readonly Stack<int> _roundBrackets = new Stack<int>();
    private readonly Stack<int> _quotes = new Stack<int>();

    private void ResetErrorMessages()
    {
        _errorMessages = [];
    }

    public bool TryValidate(string criteria, out IReadOnlyCollection<SearchStringError> errorMessages)
    {

        if (bool.TryParse(configuration["LogCriteriaValidationProgress"] ?? "false", out var logMessages))
        {
            _logMessages = logMessages;
        }

        _criteria = criteria;
        _characterPosition = 0;
        ResetErrorMessages();
        GetChar();
        Criteria();
        errorMessages = new ReadOnlyCollection<SearchStringError>(_errorMessages.OrderBy(x => x.Start).ToList());
        return errorMessages.Count == 0;
    }

    private void GetChar()
    {
        _ch = _characterPosition < _criteria.Length ? _criteria.Substring(_characterPosition++, 1) : "\0";
    }

    private string GetNextNChars(int n)
    {
        var substring = _criteria.Substring(_characterPosition - 1,
            (_characterPosition + n) < _criteria.Length ? n : (_criteria.Length - _characterPosition));
        return substring;
    }

    private bool IsValidSearchTermCharacter(string ch)
    {
        var chr = ch[0];
        return Char.IsAsciiLetterOrDigit(chr) || IsWildcard() || IsWhiteSpace() || IsAdditionalChar();
    }

    private bool IsValidSearchPhraseCharacter(string ch)
    {
        var chr = ch[0];
        return IsValidSearchTermCharacter(ch) || _phraseAllowedSymbols.Contains(chr);
    }

    private bool TryGetLogicalOperator(out string logicalOperator)
    {
        if (IsAnd())
        {
            logicalOperator = "AND";
            GetChar();
            GetChar();
            GetChar();
            return true;
        }

        if (IsOr())
        {
            logicalOperator = "OR";
            GetChar();
            GetChar();
            return true;
        }

        logicalOperator = "";
        return false;
    }

    private void ConsumeWhitespace()
    {
        GetChar();
        while (IsWhiteSpace())
        {
            GetChar();
        }
    }

    private bool IsNegated()
    {
        var word = GetNextNChars(3);
        if (word == "NOT")
        {
            GetChar();
            GetChar();
            GetChar();
            return true;
        }

        return false;
    }

    private void Criteria()
    {
        if (IsNegated())
        {
            ConsumeWhitespace();

            if (!IsExpressionNext())
            {
                var message = $"Missing expression in position {_characterPosition}";
                RecordError(HighlightStart(), EndOfHighlight(_characterPosition, 1), message);
            }
            else if (IsLogicalOperator())
            {
                var message = $"NOT cannot be followed by AND, OR or NOT in position {_characterPosition}";
                RecordError(HighlightStart(), EndOfHighlight(_characterPosition, 3), message);
            }
            else
            {
                Expression();
            }
        }
        else
        {
            Expression();
        }
    }


    private void Expression()
    {
        Factor();
        while (TryGetLogicalOperator(out string logicalOperator))
        {
            ConsumeWhitespace();
            Factor();
            Log($"Operator= {logicalOperator}");
        }
    }

    private void Factor()
    {
        if (IsOpenBracket())
        {
            StoreRoundBracket(_characterPosition);
            GetChar();
            Criteria();
            if (!IsCloseBracket())
            {
                var message = $"Unmatched right bracket in position {_roundBrackets.Peek()}";
                RecordError(StartOfHighlight(_roundBrackets.Peek()), EndOfHighlight(_roundBrackets.Peek(), 1), message);
            }

            ClearRoundBracket();

            ConsumeWhitespace();
        }
        else
        {
            FullSearchTerm();
        }
    }
    private void StoreRoundBracket(int characterPosition)
    {
        _roundBrackets.Push(characterPosition);
    }

    private void ClearRoundBracket()
    {
        _roundBrackets.Pop();
    }

    private void FullSearchTerm()
    {
        SearchTerm();
        OptionalSearchFieldTag();
    }

    private void SearchTerm()
    {
        if (TryGetOpeningQuote(out char openingQuote))
        {
            StoreQuote(_characterPosition);

            SearchPhrase();

            if (!IsMatchingClosingQuote(openingQuote))
            {
                var message = $"Mis-matched quote in position {_quotes.Peek()}";
                RecordError(StartOfHighlight(_quotes.Peek()), EndOfHighlight(_quotes.Peek(), 1), message);
            }

            ClearQuote();

            ConsumeWhitespace();
        }
        else if (!IsValidSearchTermCharacter(_ch))
        {
            var message = $"Unexpected character \"{_ch}\" in position {_characterPosition}";
            RecordError(HighlightStart(), HighlightEnd(1), message);
        }
        else
        {
            SearchTerms();
        }
    }

    private void StoreQuote(int characterPosition)
    {
        _quotes.Push(characterPosition);
    }
    
    private void ClearQuote()
    {
        _quotes.Pop();
    }


    private void SearchPhrase()
    {
        var phrase = new StringBuilder();
        GetChar();

        while (IsValidSearchPhraseCharacter(_ch) && !IsClosingQuote())
        {
            CheckWildcard(phrase.ToString());
            phrase.Append(_ch);
            GetChar();
        }
        Log($"Phrase= \"{LogSanitizer.Sanitize(phrase.ToString())}\"");
    }

    private void SearchTerms()
    {
        var searchTerms = new StringBuilder();
        while (IsValidSearchTermCharacter(_ch) && !IsLogicalOperator())
        {
            CheckWildcard(searchTerms.ToString());
            searchTerms.Append(_ch);
            GetChar();
        }
        Log($"Terms= {LogSanitizer.Sanitize(LogSanitizer.Sanitize(searchTerms.ToString()))}");
    }

    private void CheckWildcard(string str)
    {
        if (IsWildcard() && !IsWildCardPositionValid(str))
        {
            var message = $"Wildcard must start with at least 4 characters in position {_characterPosition}";
            RecordError(StartOfHighlight(_characterPosition - str.Length - 1), _characterPosition, message);
        }
    }

    private static bool IsWildCardPositionValid(string str)
    {
        return str.Length >= 4 && !str.Substring(str.Length - 4).Any(char.IsWhiteSpace);
    }

    private void OptionalSearchFieldTag()
    {
        if (!IsOpenSearchFieldTag()) return;

        SearchFieldTag();

        if (!IsCloseSearchFieldTag())
        {
            var message = $"Mis-matched closing tag in position {_characterPosition}";
            RecordError(HighlightStart(), HighlightEnd(1), message);
        }

        ConsumeWhitespace();
    }

    private bool TryGetOpeningQuote(out char openingQuote)
    {
        openingQuote = ' ';
        if (IsOpeningQuote())
        {
            openingQuote = _ch[0];
            return true;
        }

        return false;
    }

    private bool IsMatchingClosingQuote(char openingQuote)
    {
        return (openingQuote == OpeningTypographicQuote && IsClosingTypographicQuote()) ||
               (openingQuote == NeutralDoubleQuote && IsNeutralDoubleQuote());
    }

    private bool IsValidSearchFieldTagCharacter()
    {
        return Char.IsAsciiLetterOrDigit(_ch[0]);
    }

    private void SearchFieldTag()
    {
        var searchFieldTag = new StringBuilder();
        GetChar();
        while (IsValidSearchFieldTagCharacter())
        {
            searchFieldTag.Append(_ch);
            GetChar();
        }

        Log($"SearchFieldTag= {LogSanitizer.Sanitize(searchFieldTag.ToString())}");


        ValidateSearchFieldTag(searchFieldTag.ToString());
    }

    private void ValidateSearchFieldTag(string searchFieldTag)
    {
        if (!_searchFieldTags.Contains(searchFieldTag))
        {
            var message = $"Invalid Search Field Tag {searchFieldTag} in position {_characterPosition - searchFieldTag.Length}";
            RecordError(HighlightStart(), HighlightEnd(_characterPosition - searchFieldTag.Length), message);
        }
    }

    private void RecordError(int start, int end, string message)
    {
        _errorMessages.Add(new SearchStringError { Start = start, End = end, ErrorMessage = message });
        Log($"Error = {LogSanitizer.Sanitize(message)}");
    }

    private void Log(string message)
    {
        if (_logMessages)
        {
            logger.LogInformation("CriteriaValidation: {Message}", message);
        }
    }
}