﻿using PharmaLex.VigiLit.DataAccessLayer.Base;

namespace PharmaLex.VigiLit.Domain.Models;

public class AdHocImportContract : VigiLitEntityBase
{
    public int AdHocImportId { get; set; }
    public int ContractId { get; set; }

    public AdHocImport AdHocImport { get; set; }
    public Contract Contract { get; set; }

    private AdHocImportContract()
    {
    }

    public AdHocImportContract(int contractId)
    {
        ContractId = contractId;
    }
}
