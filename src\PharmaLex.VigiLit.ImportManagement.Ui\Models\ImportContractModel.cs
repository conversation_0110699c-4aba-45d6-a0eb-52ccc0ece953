﻿namespace PharmaLex.VigiLit.ImportManagement.Ui.Models;

public class ImportContractModel
{
    public int Id { get; set; }

    public int ContractId { get; set; }
    public int SubstanceId { get; set; }
    public int ProjectId { get; set; }
    public int CompanyId { get; set; }

    public required string SubstanceName { get; set; }
    public required string ProjectName { get; set; }
    public required string CompanyName { get; set; }

    public required string ImportDate { get; set; }
    public required string PubMedModificationDate { get; set; }

    public required string StartDate { get; set; }
    public required string EndDate { get; set; }
    public required string Duration { get; set; }
    public required string ImportContractStatusType { get; set; }

    public int ReferencesCount { get; set; }
    public int NewReferencesCount { get; set; }
    public int UpdatesCount { get; set; }
    public int SilentUpdatesCount { get; set; }
}