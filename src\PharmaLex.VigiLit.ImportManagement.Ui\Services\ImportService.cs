﻿using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Services;

internal class ImportService : IImportService
{
    private readonly IImportRepository _importRepository;
    private readonly IImportContractRepository _importContractRepository;
    private readonly IAdHocImportContractRepository _adHocImportContractRepository;

    public ImportService(
        IImportRepository importRepository,
        IImportContractRepository importContractRepository,
        IAdHocImportContractRepository adHocImportContractRepository)
    {
        _importRepository = importRepository;
        _importContractRepository = importContractRepository;
        _adHocImportContractRepository = adHocImportContractRepository;
    }

    public async Task<IEnumerable<ImportModel>> GetImportLog()
    {
        int days = Constants.ImportLogIntervalDays;
        return await _importRepository.GetImportLog(days);
    }

    public async Task<IEnumerable<ImportContractModel>> GetImportContractsLog(int importId)
    {
        return await _importContractRepository.GetImportContractsLog(importId);
    }

    public async Task<IEnumerable<AdHocImportContractModel>> GetAdHocImportContractsList(int adHocImportId)
    {
        return await _adHocImportContractRepository.GetForListDetails(adHocImportId);
    }
}