﻿using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class ReferenceHistoryActionMappingProfile : Profile
{
    public ReferenceHistoryActionMappingProfile()
    {
        CreateMap<ReferenceHistoryAction, ReferenceHistoryActionModel>()
            .ForMember(d => d.ReferenceHistoryActionTypeText, s => s.MapFrom(x => x.ReferenceHistoryActionType.GetDescription()))
            .ForMember(d => d.UserName, s => s.MapFrom(x => x.User.Id > 0 ? $"{x.User.GivenName} {x.User.FamilyName}" : "System"));
    }
}