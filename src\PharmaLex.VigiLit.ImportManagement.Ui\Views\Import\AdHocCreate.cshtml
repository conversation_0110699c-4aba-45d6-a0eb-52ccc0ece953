@{
    ViewData["Title"] = "Create Ad-Hoc Import";
}

<div id="ad-hoc-create" v-cloak>
    @Html.AntiForgeryToken()
    <div class="sub-header">
        <h2>Create Ad-Hoc Import</h2>
        <div class="controls">
            <button type="button" :disabled="!saveEnabled" @@click="handleSave" class="btn-default">Save</button>
            <a class="button secondary icon-button-cancel btn-default" href="../Import/AdHocList">Cancel</a>
        </div>
    </div>
    <section>
        <h2>1. Select a date range (PubMed Modification Date)</h2>
        <div class="form-group" style="display:inline-block; width:220px; margin:5px 10px 0 10px;">
            <label for="modDateFrom">From:</label>
            <input type="date" v-model="modDateFrom" id="modDateFrom">
        </div>
        <div class="form-group" style="display:inline-block; width:220px">
            <label for="modDateTo">To:</label>
            <input type="date" v-model="modDateTo" id="modDateTo">
        </div>
        <hr />
        <h2>2. Select contracts</h2>
        <div class="horizontal-filter flex flex-wrap">
            <select v-model="companyId" :onclick="loadProjects" style="min-width:220px" aria-label="company">
                <option value="0">Choose a company</option>
                <option v-for="company in companies" :value="company.id">{{company.name}}</option>
            </select>
            <select v-model="projectId" style="min-width:220px" aria-label="project">
                <option value="0">Choose a project</option>
                <option v-for="project in projects" :value="project.id">{{project.name}}</option>
            </select>
            <button type="button" @@click="loadContracts" class="large">Search</button>
        </div>
        <hr />
        <filtered-table v-if="contracts !== null" :items="contracts" :columns="columns"
            selectable @@on-selection-change="handleSelectionChange">
        </filtered-table>
    </section>
</div>

@section Scripts {

<script type="text/javascript">
        const token = document.getElementsByName("__RequestVerificationToken")[0].value;
        var pageConfig = {
            appElement: "#ad-hoc-create",
            methods: {
                loadCompanies: function () {
                    fetch('/Import/AdHocCreate_GetCompanies', {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(res => {
                            if (!res.ok) {
                                throw res;
                            }
                            return res.json();
                        })
                        .then(data => {
                            this.companies = data;
                        });
                },
                loadProjects: function () {
                    fetch(`/Import/AdHocCreate_GetProjects?companyId=${this.companyId}`, {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(res => {
                            if (!res.ok) {
                                throw res;
                            }
                            return res.json();
                        })
                        .then(data => {
                            this.projects = data;
                            if (!this.projects.some(p => p.id === this.projectId)) {
                                this.projectId = 0;
                            }
                        });
                },
                loadContracts: function () {
                    this.contracts = null;
                    fetch(`/Import/AdHocCreate_GetContracts?companyId=${this.companyId}&projectId=${this.projectId}`, {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(res => {
                            if (!res.ok) {
                                throw res;
                            }
                            return res.json();
                        })
                        .then(data => {
                            this.selectedContractIds = [];
                            this.contracts = data;
                        });
                },
                handleSelectionChange: function (selectedIds) {
                    this.selectedContractIds = selectedIds;
                },
                handleSave: function () {
                    fetch(`/Import/AdHocCreate_Save`, {
                        method: "POST",
                        credentials: 'same-origin',
                        body: JSON.stringify({
                            sourceSearchStartDate: this.modDateFrom,
                            sourceSearchEndDate: this.modDateTo,
                            selectedContractIds: this.selectedContractIds
                        }),
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    })
                        .then(res => {
                            if (!res.ok) {
                                throw res;
                            }
                            window.location.href = 'AdHocList';
                        })
                        .catch(error => {
                            console.log(error);
                            plx.toast.show('Something went wrong, please try again.', 2, 'failed', null, 2500);
                        });
                }
            },
            computed: {
                saveEnabled() {
                    return this.modDateFrom && this.modDateTo
                        && Date.parse(this.modDateFrom) <= Date.parse(this.modDateTo)
                        && this.selectedContractIds.length > 0;
                }
            },
            created() {
                this.loadCompanies();
            },
            data: function () {
                return {
                    companies: null,
                    projects: null,
                    companyId: 0,
                    projectId: 0,
                    contracts: null,
                    selectedContractIds: [],
                    modDateFrom: null,
                    modDateTo: null,
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'id',
                                sortKey: 'id',
                                header: 'Contract ID',
                                type: 'number',
                                style: 'width: 16.6%;'
                            },
                            {
                                dataKey: 'projectName',
                                sortKey: 'projectName',
                                header: 'Project Name',
                                type: 'text',
                                style: 'width: 16.6%;'
                            },
                            {
                                dataKey: 'substanceName',
                                sortKey: 'substanceName',
                                header: 'Substance Name',
                                type: 'text',
                                style: 'width: 16.6%;'
                            },
                            {
                                dataKey: 'contractType',
                                sortKey: 'contractType',
                                header: 'Type',
                                type: 'text',
                                style: 'width: 16.6%;'
                            },
                            {
                                dataKey: 'searchPeriod',
                                sortKey: 'searchPeriod',
                                header: 'Search Period',
                                edit: {},
                                type: 'text',
                                style: 'width: 16.6%;'
                            }
                        ]
                    }
                };
            }
        };
</script>
}

@section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
}