apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "vigilit-ai.fullname" . }}
  labels:
    {{- include "vigilit-ai.labels" . | nindent 4 }}

spec:
  selector:
    matchLabels:
      {{- include "vigilit-ai.selectorLabels" . | nindent 6 }}
  replicas: {{ .Values.replicas }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "vigilit-ai.selectorLabels" . | nindent 8 }}
        azure.workload.identity/use: "true"
    spec:
      automountServiceAccountToken: false
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      serviceAccountName: {{ include "vigilit-ai.fullname" . }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          #livenessProbe:
          #  tcpSocket:
          #    port: 80
          #  failureThreshold: 5
          #  periodSeconds: 30
          #  timeoutSeconds: 3
          #readinessProbe:
          #  tcpSocket:
          #    port: 80
          #  failureThreshold: 5
          #  periodSeconds: 30
          #  timeoutSeconds: 3
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          env:
            - name: NEW_RELIC_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  name: newreliclicensekey
                  key: KEY
            - name: NEWRELIC_PROFILER_LOG_DIRECTORY
              value: "\\app"
            - name: NEWRELIC_LOG_LEVEL
              value: "off"
            - name: NEW_RELIC_DISTRIBUTED_TRACING_ENABLED
              value: "true"
            - name: NEW_RELIC_APP_NAME
              value: {{ .Values.newRelicAppName }}
            - name: ASPNETCORE_ENVIRONMENT
              value: {{ .Values.aspNetCoreEnvironment }}
            - name: ASPNETCORE_URLS
              value: "http://+:8080"
            - name: KeyVaultName
              value: {{ .Values.keyVaultName }}
            - name: AzureAd__ClientId
              value: {{ .Values.azureWorkload.clientId }}
            - name: MessageBus__AzureServiceBus__Namespace
              value: {{ .Values.serviceBus }}
            - name: AiEndpointSettings__Uri
              value: {{ .Values.AiEndpointSettingsUri }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
