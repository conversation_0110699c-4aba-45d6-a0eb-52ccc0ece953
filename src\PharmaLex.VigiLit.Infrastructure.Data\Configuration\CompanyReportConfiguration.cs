using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CompanyReportConfiguration : EntityBaseMap<CompanyReport>
{
    public override void Configure(EntityTypeBuilder<CompanyReport> builder)
    {
        base.Configure(builder);

        builder.ToTable("CompanyReports");

        builder.HasIndex(c => new { c.CompanyId, c.ReportId }).IsUnique();

        builder.HasOne(ic => ic.Report).WithMany(i => i.CompanyReports).HasForeignKey(ic => ic.ReportId);
        builder.HasOne(ic => ic.Company).WithMany(i => i.CompanyReports).HasForeignKey(ic => ic.CompanyId);
    }
}