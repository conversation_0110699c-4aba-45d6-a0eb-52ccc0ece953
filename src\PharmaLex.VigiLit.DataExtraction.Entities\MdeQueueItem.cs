﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.DataExtraction.Entities.Enums;

namespace PharmaLex.VigiLit.DataExtraction.Entities;

public class MdeQueueItem : VigiLitEntityBase
{
    public Guid BatchId { get; set; }

    public required string Filename { get; set; }

    public required string DocumentLocation { get; set; }

    public MdeQueueItemStatus Status { get; set; }

    public Guid CorrelationId { get; set; }

    public Source Source { get; set; }
}