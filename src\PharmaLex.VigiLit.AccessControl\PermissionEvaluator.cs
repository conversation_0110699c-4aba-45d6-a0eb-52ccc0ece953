﻿using System.Diagnostics.CodeAnalysis;
using System.Security.Claims;

namespace PharmaLex.VigiLit.AccessControl;

/// <summary>
/// Provides a base class for permission evaluators that perform access control based on the specified context.
/// </summary>
/// <remarks>Note that the implementation should remain internal to the project and registered in the services collection from within the containing project.</remarks>
/// <typeparam name="T">The type of access control context.</typeparam>
[ExcludeFromCodeCoverage(Justification = "Abstract class - code is not executed")]
public abstract class PermissionEvaluator<T> : IPermissionEvaluator<T> where T : IAccessControlContext
{
#pragma warning disable 1998
    public abstract Task<bool> HasPermissions(T context);
#pragma warning restore 1998


    /// <summary>
    /// Gets the user ID from the specified <see cref="ClaimsPrincipal"/>.
    /// </summary>
    /// <param name="claimsPrincipal">The <see cref="ClaimsPrincipal"/> representing the user.</param>
    /// <returns>The user ID extracted from the claims.</returns>
    /// <exception cref="UnauthorizedAccessException">
    /// Thrown when the user ID cannot be determined.
    /// </exception>
    protected int GetUserId(ClaimsPrincipal claimsPrincipal)
    {
        try
        {
            var claim = claimsPrincipal.Claims.Single(x => x.Type == "plx:userid");
            var userId = int.Parse(claim.Value);
            return userId;
        }
        catch (Exception)
        {
            throw new UnauthorizedAccessException();
        }
    }
}
