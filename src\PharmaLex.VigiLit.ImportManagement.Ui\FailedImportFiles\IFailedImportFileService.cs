﻿using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.Generics;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;

namespace PharmaLex.VigiLit.ImportManagement.Ui.FailedImportFiles
{
    public interface IFailedImportFileService : IGenericCardService<FailedImportFile, FailedImportFileModel>, IHasSearchByDoi<FailedImportFile>, IDownloadable<DownloadFile>
    {
    }
}
