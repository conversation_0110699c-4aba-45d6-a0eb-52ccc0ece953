﻿using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface IClassificationService
{
    Task<ReferenceClassification> GetByIdAsync(int referenceClassificationId);
    
    Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetReferencesToClassify(int importId);
    
    Task Classify(ReferenceClassificationModel updatedClassification);
    Task Sign(int importId);

    Task ReClassify(ReferenceClassificationModel updatedClassification);
    Task EditFromReferenceHistory(ReferenceClassificationModel updatedClassification);
    Task<int> GetClassifiedCount(int importId);
    Task<int> GetToDoCount(int importId);
    Task<int> GetPreclassifiedCount(int importId);
    Task<IEnumerable<ReferenceClassificationWithReferenceModel>> GetAllClassified(int importId);
    Task<ReferenceHistoryDetailsPageModel> GetReferenceHistoryDetailsInitialPageLoad(int referenceClassificationId, User user);
    Task<ReferenceHistoryDetailsPageModel> GetReferenceHistoryDetailsByAction(int referenceClassificationId, int actionId, int previousActionId, User user);
    Task<bool> CanEditReferenceClassification(int id);
    void AddReferenceHistoryAction(int classificationId, ReferenceHistoryActionType historyActionType);
    Task SaveReferenceHistoryChangesAsync();
}