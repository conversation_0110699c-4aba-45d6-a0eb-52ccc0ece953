using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface ISubstanceService
{
    Task<IEnumerable<SubstanceModel>> GetAllAsync();
    Task AddAsync(SubstanceModel model);
    Task UpdateAsync(SubstanceModel model);
    Task DeleteSynonymAsync(int id);
    Task<SubstanceModel> GetByIdAsync(int id);
    Task<SubstanceModel> GetByNameAsync(string name);
    Task<bool> ValidateNameAsync(string name, int id);
    Task<IEnumerable<SubstanceItemModel>> GetForSearch(User user);
    Task<IEnumerable<SubstanceItemModel>> GetActiveSubstancesWithNoClassificationsForReference(int referenceId);
    Task<IEnumerable<SubstanceModel>> GetAllWithSynonymsCachedAsync();

    /// <summary>
    /// Gets the specified substance.
    /// </summary>
    /// <param name="substanceId">The substance identifier.</param>
    /// <returns></returns>
    Task<Substance> Get(int substanceId);
}