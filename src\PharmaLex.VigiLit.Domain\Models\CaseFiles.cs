﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.VigiLit.Domain.Models;

public class CaseFiles : VigiLitEntityBase
{
    [ForeignKey("Cases")]
    public int CaseId { get; set; }
    public Cases Case { get; set; }

    [Required, MaxLength(256)]
    public string FileName { get; set; }

    [Required]
    public int FileSize { get; set; }

    public CaseFiles() { }

    public CaseFiles(Cases caseRecord, string fileName, int fileSize)
    {
        CaseId = caseRecord.Id;
        Case = caseRecord;
        FileName = fileName;
        FileSize = fileSize;
    }
}