using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.DataExtraction.Service.Interfaces;

public interface IJournalRepository : ITrackingRepository<Journal>
{
    /// <summary>
    /// Asynchronously retrieves the names of all the journals.
    /// </summary>
    /// <returns>
    /// A task that represents the asynchronous operation. The task result contains a collection of strings.
    /// </returns>
    Task<IEnumerable<string>> GetNames();
}