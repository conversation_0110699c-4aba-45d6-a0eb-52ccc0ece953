﻿using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.ContractManagement.Ui.Services;
internal static class IssnValidator
{
    /// <summary>
    /// Determines whether [is valid issn] [the specified input].
    /// </summary>
    /// <param name="input">The input.</param>
    /// <returns>
    ///   <c>true</c> if [is valid issn] [the specified input]; otherwise, <c>false</c>.
    /// </returns>
    public static bool IsValidIssn(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return false;
        }

        var cleaned = input.Replace("-", "").ToUpper();

        if (!Regex.IsMatch(cleaned, @"^\d{7}[\dX]$", RegexOptions.None, TimeSpan.FromMilliseconds(500)))
        {
            return false;
        }

        var sum = 0;
        for (var i = 0; i < 7; i++)
        {
            sum += (cleaned[i] - '0') * (8 - i);
        }

        var remainder = sum % 11;
        var checkDigit = (11 - remainder) % 11;
        var expectedCheckChar = checkDigit == 10 ? 'X' : (char)('0' + checkDigit);

        return cleaned[7] == expectedCheckChar;
    }
}
