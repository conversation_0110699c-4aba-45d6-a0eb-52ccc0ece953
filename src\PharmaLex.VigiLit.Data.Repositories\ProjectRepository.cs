using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Data.Repositories;

public class ProjectRepository : TrackingGenericRepository<Project>, IProjectRepository
{

    public ProjectRepository(PlxDbContext context, IUserContext userContext) : base(context, userContext.User)
    {
    }

    public async Task<IEnumerable<Project>> GetAllAsync()
    {
        return await context.Set<Project>()
            .AsNoTracking()
            .ToListAsync();
    }

    public async Task<IEnumerable<Project>> GetByCompanyIdAsync(int companyId)
    {
        return await context.Set<Project>()
            .AsNoTracking()
            .Where(p => p.CompanyId == companyId)
            .ToListAsync();
    }

    public Task<Project?> GetByIdAsync(int id)
    {
        return context.Set<Project>()
            .FirstOrDefaultAsync(u => u.Id == id);
    }

    public Task<Project?> GetByNameAndCompanyAsync(string name, int companyId)
    {
        return context.Set<Project>()
            .FirstOrDefaultAsync(p => p.CompanyId == companyId && p.Name == name);
    }
}
