﻿using System.Text.RegularExpressions;

namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;

public class AbstractTextSearcher : IAbstractTextSearcher
{
    public bool IsWordInAbstract(string searchTerm)
    {
        string pattern;
        string starCharacter = "*";

        // Check if the term ends with an asterisk
        if (searchTerm.EndsWith(starCharacter))
        {
            // Create a regex pattern that matches words starting with the term minus the asterisk
            string baseTerm = Regex.Escape(searchTerm.TrimEnd('*'));
            pattern = $@"\b{baseTerm}\w*\b";
        }
        else
        {
            // Create a regex pattern that matches the whole word
            pattern = $@"\b{Regex.Escape(searchTerm)}\b";
        }

        // Use Regex to match the pattern in the abstract text
        return Regex.IsMatch(AbstractText, pattern, RegexOptions.IgnoreCase, TimeSpan.FromSeconds(10));
    }
    public string AbstractText { get; set; } = default!;
}