﻿using Microsoft.Extensions.Options;
using PharmaLex.BlobStorage.Descriptors;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models.Document.Case;

namespace PharmaLex.VigiLit.Infrastructure.Document.Case;

internal class CaseDocumentUploadService : ICaseDocumentUploadService
{
    private readonly IDocumentService _documentService;
    private readonly AzureStorageCaseDocumentUploadOptions _caseDocumentUploadOptions;
    private readonly AzureStorageCaseDocumentOptions _caseDocumentOptions;

    public CaseDocumentUploadService(
        IDocumentService documentService,
        IOptions<AzureStorageCaseDocumentUploadOptions> caseDocumentUploadOptions,
        IOptions<AzureStorageCaseDocumentOptions> caseDocumentOptions)
    {
        _documentService = documentService;
        _caseDocumentUploadOptions = caseDocumentUploadOptions.Value;
        _caseDocumentOptions = caseDocumentOptions.Value;
    }

    public async Task Create(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, Stream stream, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(caseDocumentUploadDescriptor);
        await _documentService.Create(documentDescriptorUpload, stream, cancellationToken);
    }

    public async Task<bool> Exists(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(caseDocumentUploadDescriptor);
        var exists = await _documentService.Exists(documentDescriptorUpload, cancellationToken);
        return exists;
    }

    public async Task<Stream> OpenRead(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(caseDocumentUploadDescriptor);
        var stream = await _documentService.OpenRead(documentDescriptorUpload, cancellationToken);
        return stream;
    }

    public async Task Delete(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(caseDocumentUploadDescriptor);
        await _documentService.Delete(documentDescriptorUpload, cancellationToken);
    }

    public async Task<DocumentProperties> Save(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor, CaseDocumentDescriptor caseDocumentDescriptor, CancellationToken cancellationToken = default)
    {
        var documentDescriptorUpload = GetDocumentDescriptor(caseDocumentUploadDescriptor);
        var documentDescriptor = GetDocumentDescriptor(caseDocumentDescriptor);
        var documentProperties = await _documentService.CopyFrom(documentDescriptorUpload, documentDescriptor, cancellationToken);
        return documentProperties;
    }

    private DocumentDescriptor GetDocumentDescriptor(CaseDocumentUploadDescriptor caseDocumentUploadDescriptor)
    {
        var blobName = caseDocumentUploadDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_caseDocumentUploadOptions.AccountName, _caseDocumentUploadOptions.ContainerName, blobName);
        return documentDescriptor;
    }

    private DocumentDescriptor GetDocumentDescriptor(CaseDocumentDescriptor caseDocumentDescriptor)
    {
        var blobName = caseDocumentDescriptor.GetFullPath();
        var documentDescriptor = new DocumentDescriptor(_caseDocumentOptions.AccountName, _caseDocumentOptions.ContainerName, blobName);
        return documentDescriptor;
    }
}