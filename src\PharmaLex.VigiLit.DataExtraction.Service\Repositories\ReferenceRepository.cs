using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.DataExtraction.Service.Interfaces;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.DataExtraction.Service.Repositories;

internal class ReferenceRepository : TrackingGenericRepository<Reference>, IReferenceRepository
{
    public ReferenceRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<bool> DoesDoiExist(string doiId)
    {
        return await context.Set<Reference>()
            .AnyAsync(r => r.Doi == doiId);
    }
}