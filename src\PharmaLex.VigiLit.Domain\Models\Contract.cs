using PharmaLex.Helpers;
using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace PharmaLex.VigiLit.Domain.Models;

public class Contract : VigiLitEntityBase
{
    private static readonly DateTime NoContractStartDate = new(1900, 1, 1, 1,0,0, DateTimeKind.Utc);

    public int SubstanceId { get; protected set; }
    public int ProjectId { get; protected set; }

    public Substance Substance { get; protected set; }

    public Project Project { get; protected set; }

    public IList<ContractVersion> ContractVersionsInternal { get; set; } = new List<ContractVersion>();
    public IReadOnlyCollection<ContractVersion> ContractVersions => new ReadOnlyCollection<ContractVersion>(ContractVersionsInternal);

    public ICollection<ImportContract> ImportContracts { get; set; }
    public ICollection<AdHocImportContract> AdHocImportContracts { get; set; }

    public int CurrentContractVersionId { get; set; }

    public DateTime ContractStartDate { get; set; } = NoContractStartDate;

    public int ScreeningType { get; set; }

    public Contract()
    {

    }

    public Contract(int substanceId, int projectId)
    {
        SubstanceId = substanceId;
        ProjectId = projectId;
    }

    public void AddContractVersion(ContractVersion contractVersion)
    {
        ContractVersionsInternal.Add(contractVersion);
    }

    public ContractVersion GetCurrentContractVersion()
    {
        return ContractVersionsInternal.Single(cs => cs.ContractVersionStatus == ContractVersionStatus.Approved);
    }

    public ContractVersion GetContractVersion(int contractVersionId)
    {
        return ContractVersionsInternal.SingleOrDefault(cs => cs.Id == contractVersionId);
    }

    public string GetSearchTerm(int contractVersionId)
    {
        return ContractVersionsInternal.First(cs => cs.Id == contractVersionId).SearchString;
    }

    public string GetContractTypeDescription()
    {
        var contractVersion = ContractVersionsInternal.OrderByDescending(x => x.Id).First();

        if (contractVersion.ContractVersionJournals.Count > 0)
        {
            return "n/a";
        }

        return contractVersion.ContractType.GetDescription();
    }

    public string GetSearchPeriodDescription()
    {
        var contractVersion = ContractVersionsInternal.OrderByDescending(x => x.Id).First();

        if (contractVersion.ContractVersionJournals.Count > 0)
        {
            return "n/a";
        }

        return contractVersion.SearchPeriodDays == 0 ? "Unlimited" : string.Format("{0} days", contractVersion.SearchPeriodDays);
    }
    public string GetWeekDayDescription()
    {
        var contractVersion = ContractVersionsInternal.OrderByDescending(x => x.Id).First();

        if (contractVersion.ContractVersionJournals.Count >  0)
        {
            return "n/a";
        }

        return contractVersion.ContractWeekday.GetDescription();
    }

    public bool HasContractStarted()
    {
        return ContractStartDate != NoContractStartDate;
    }
}
