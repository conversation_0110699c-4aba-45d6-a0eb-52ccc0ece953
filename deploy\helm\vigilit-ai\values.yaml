image:
  repository: phlexglobal.azurecr.io/vigilit-ai
  pullPolicy: Always
  tag: dev

nameOverride: "vigilit-ai"
fullnameOverride: "vigilit-ai"

resources:
  limits:
    cpu: 2000m
    memory: 2Gi
  requests:
    cpu: 500m
    memory: 100Mi

azureWorkload:
  clientId: 4bee4aa8-c08c-4d92-8828-dc86e4834fc6 #axon-dev-aks-eun-sp-vigilit-ai-dev app
  tenantId: 66b904a2-2bfc-4d24-a410-96b77b32bf77
  tokenExpiration: "86400" # Token is valid for 1 day

newreliclicensekey: "#{newrelic-pharmalex-license-key}#"
newRelicAppName: "vgt-dev-ai-eun"

nodeSelector:
  kubernetes.io/os: linux

securityContext:
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
      - NET_RAW
      - ALL
    add:
      - NET_BIND_SERVICE
  runAsNonRoot: true
  runAsUser: 65534
  runAsGroup: 65534

podSecurityContext:
  seccompProfile:
    type: RuntimeDefault
  runAsUser: 65534
  runAsGroup: 65534
  runAsNonRoot: true

AiEndpointSettingsUri: "https://vigilit-ai-dev.smartphlex.com/latest/process"
