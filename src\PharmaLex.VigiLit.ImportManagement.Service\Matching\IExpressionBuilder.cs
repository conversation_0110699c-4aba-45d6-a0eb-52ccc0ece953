﻿namespace PharmaLex.VigiLit.ImportManagement.Service.Matching;

/// <summary>
/// Builder for translating "PubMed" style queries into C#.
/// </summary>
internal interface IExpressionBuilder
{
    /// <summary>
    /// Builds the specified expression.
    /// </summary>
    /// <param name="expression">The expression.</param>
    /// <returns>returns a C# version for use with matching references</returns>
    string Build(string expression);
}