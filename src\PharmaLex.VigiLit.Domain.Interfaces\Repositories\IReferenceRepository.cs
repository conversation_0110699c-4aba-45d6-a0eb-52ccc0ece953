using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface IReferenceRepository : ITrackingRepository<Reference> 
{
    // *** Not used during importing
    Task<IEnumerable<ReferenceModel>> GetAllAsync();
    Task<ReferenceDetailedModel> GetReferenceSnapshot(int id, DateTime timestamp);
    Task<IEnumerable<ReferenceSupportModel>> Search(string term, User user);
    Task<ReferenceSendGridTemplateModel> GetForEmail(int referenceId);
    Task<bool> DoesDoiExist(string doiId);
    Task<Reference> GetReferenceById(int id);
}