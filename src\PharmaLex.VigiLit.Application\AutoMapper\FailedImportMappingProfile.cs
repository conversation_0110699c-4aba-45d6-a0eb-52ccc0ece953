﻿using AutoMapper;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.FailedImportFiles;

namespace PharmaLex.VigiLit.Application.AutoMapper;
public class FailedImportMappingProfile : Profile
{

    public FailedImportMappingProfile()
    {
        CreateMap<FailedImportFile, FailedImportFileModel>()
            .ForMember(d => d.FailedImportStatus, opt => opt.MapFrom(src => src.Status))
            .ReverseMap();
        CreateMap<FailedImportFile, ImportReference>()
            .ForMember(x => x.Id, opt => opt.Ignore());
    }
}

