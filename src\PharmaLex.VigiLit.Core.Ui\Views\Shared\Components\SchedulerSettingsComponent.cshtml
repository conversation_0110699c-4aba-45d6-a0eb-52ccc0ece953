﻿<script type="text/x-template" id="scheduler-settings-panel">
    <div class="scheduler-settings-container" @@click.stop>
        <div class="schedule-section">
            <h3 class="section-title">Schedule</h3>

            <div class="repeat-controls">
                <label class="repeat-label">Repeat every</label>
                <div class="repeat-input-group">
                    <input type="number"
                           v-model="repeatInterval"
                           min="1"
                           max="4"
                           class="repeat-number-input"
                           @@input="updateSchedule" />

                    <select v-model="repeatUnit"
                            class="repeat-unit-select"
                            @@change="updateSchedule">
                        <option value="week">Week</option>
                        <option value="month">Month</option>
                    </select>
                </div>
            </div>

            <!-- Day Selection for Weekly Schedule -->
            <div v-if="repeatUnit === 'week'" class="day-selection">
                <div class="day-selection-header">
                    <span class="day-selection-label">Every {{ repeatInterval === 1 ? 'week' : `${repeatInterval} weeks` }} on</span>
                </div>
                <div class="day-buttons">
                    <button type="button"
                            v-for="day in days"
                            :key="day.value"
                            :class="['day-button', { active: selectedDays.includes(day.value) }]"
                            @@click="toggleDay(day.value)">
                        {{ day.label }}
                    </button>
                </div>
            </div>

            <!-- Day Selection for Monthly Schedule -->
            <div v-if="repeatUnit === 'month'" class="day-selection">
                <div class="day-selection-header">
                    <span class="day-selection-label">Every {{ repeatInterval === 1 ? 'month' : `${repeatInterval} months` }} on</span>
                </div>

                <div class="monthly-options">
                    <label> Day of Week: </label>
                    <!-- Day of Week Selection -->
                    <div class="monthly-weekday-selection">
                        <div class="weekday-controls">
                            <select v-model="selectedWeekOccurrence" @@change="updateSchedule" class="week-occurrence-select">
                                <option value="1">1st</option>
                                <option value="2">2nd</option>
                                <option value="3">3rd</option>
                                <option value="4">4th</option>
                                <option value="-1">Last</option>
                            </select>
                            <select v-model="selectedWeekDay" @@change="updateSchedule" class="weekday-select">
                                <option value="1">Monday</option>
                                <option value="2">Tuesday</option>
                                <option value="3">Wednesday</option>
                                <option value="4">Thursday</option>
                                <option value="5">Friday</option>
                                <option value="6">Saturday</option>
                                <option value="0">Sunday</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</script>
<script type="text/javascript">
    vueApp.component('scheduler-settings', {
        template: '#scheduler-settings-panel',
        emits: ['update:modelValue'],
        props: {
            modelValue: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                repeatInterval: 1,
                repeatUnit: 'week',
                selectedDays: [0], // Default to Sunday
                selectedWeekOccurrence: 1, // 1st, 2nd, 3rd, 4th, -1 (last)
                selectedWeekDay: 0, // Monday = 1, Sunday = 0
                days: [
                    { label: 'Mon', value: 1 },
                    { label: 'Tue', value: 2 },
                    { label: 'Wed', value: 3 },
                    { label: 'Thu', value: 4 },
                    { label: 'Fri', value: 5 },
                    { label: 'Sat', value: 6 },
                    { label: 'Sun', value: 0 }
                ]

            }
        },
        methods: {
            toggleDay(dayValue) {
                // Only allow single day selection for weekly schedules
                this.selectedDays = [dayValue];
                this.updateSchedule();
            },
            updateSchedule() {
                const cronExpression = this.generateCronExpression();
                this.$emit('update:modelValue', cronExpression);
            },
            generateCronExpression() {
                // Cron format: second minute hour day-of-month month day-of-week
                const interval = parseInt(this.repeatInterval);

                switch (this.repeatUnit) {
                    case 'week': {
                        // Runs weekly on selected day at midnight
                        const selectedDay = this.selectedDays[0] ?? 0; // Sunday = 0
                        return `0 0 0 ? * ${selectedDay}`; // "?" means no specific day-of-month
                    }

                    case 'month': {
                        const occurrence = this.selectedWeekOccurrence;
                        const weekday = this.selectedWeekDay;

                        if (occurrence === -1) {
                            // Last weekday of every N months (Quartz format)
                            return `0 0 0 ? */${interval} ${weekday}L`;
                        } else {
                            // Nth weekday of every N months
                            return `0 0 0 ? */${interval} ${weekday}#${occurrence}`;
                        }
                    }

                    default:
                        // Default to Sunday every week
                        return '0 0 0 ? * 0';
                }
            },

            parseCronExpression(cronExp) {
                if (!cronExp) return;
                const parts = cronExp.split(' ');
                if (parts.length < 6) return;

                // Check for custom weekly interval notation (dayOfWeek field with /)
                if (parts[5].includes('/')) {
                    const dayOfWeekPart = parts[5].split('/');
                    const day = parseInt(dayOfWeekPart[0]);
                    const interval = parseInt(dayOfWeekPart[1]);
                    this.selectedDays = [day];
                    this.repeatInterval = interval || 1;
                    this.repeatUnit = 'week';
                    return;
                }

                // Parse monthly patterns with weekday occurrence (#) - Handle both with and without month intervals
                if (parts[5].includes('#')) {
                    const [weekday, occurrence] = parts[5].split('#');
                    this.selectedWeekDay = parseInt(weekday);
                    this.selectedWeekOccurrence = parseInt(occurrence);
                    this.repeatUnit = 'month';

                    // Check for month interval in the month field (parts[4])
                    // Handle both */3 and */*3 patterns
                    if (parts[4].includes('/')) {
                        const monthParts = parts[4].split('/');
                        // Get the interval part (last element after split)
                        const intervalPart = monthParts[monthParts.length - 1];
                        this.repeatInterval = parseInt(intervalPart) || 1;
                    } else {
                        this.repeatInterval = 1;
                    }
                    return;
                }

                // Parse monthly patterns with last weekday (L) - Handle both with and without month intervals
                if (parts[5].includes('L')) {
                    const weekday = parseInt(parts[5].replace('L', ''));
                    this.selectedWeekDay = weekday;
                    this.selectedWeekOccurrence = -1;
                    this.repeatUnit = 'month';

                    // Check for month interval in the month field (parts[4])
                    // Handle both */3 and */*3 patterns
                    if (parts[4].includes('/')) {
                        const monthParts = parts[4].split('/');
                        // Get the interval part (last element after split)
                        const intervalPart = monthParts[monthParts.length - 1];
                        this.repeatInterval = parseInt(intervalPart) || 1;
                    } else {
                        this.repeatInterval = 1;
                    }
                    return;
                }

                // Parse weekly patterns with single day
                if (parts[3] === '*' && parts[4] === '*' && !isNaN(parseInt(parts[5])) && !parts[5].includes(',')) {
                    this.selectedDays = [parseInt(parts[5])];
                    this.repeatInterval = 1;
                    this.repeatUnit = 'week';
                    return;
                }

                // Parse weekly patterns with multiple days (legacy support - convert to single day)
                if (parts[5].includes(',') && parts[3] === '*' && parts[4] === '*') {
                    const days = parts[5].split(',').map(d => parseInt(d)).filter(d => !isNaN(d));
                    this.selectedDays = [days[0]]; // Take first day only
                    this.repeatInterval = 1;
                    this.repeatUnit = 'week';
                    return;
                }

                // Parse monthly patterns with interval (but not weekday patterns)
                if (parts[4].includes('/') && !parts[5].includes('#') && !parts[5].includes('L')) {
                    const monthParts = parts[4].split('/');
                    // Get the interval part (last element after split)
                    const intervalPart = monthParts[monthParts.length - 1];
                    this.repeatInterval = parseInt(intervalPart) || 1;
                    this.repeatUnit = 'month';

                    if (!isNaN(parseInt(parts[3]))) {
                        this.selectedMonthDates = [parseInt(parts[3])];
                    }
                    return;
                }

                // Simple weekly (single day)
                if (parts[3] === '*' && parts[4] === '*' && !isNaN(parseInt(parts[5]))) {
                    this.selectedDays = [parseInt(parts[5])];
                    this.repeatInterval = 1;
                    this.repeatUnit = 'week';
                }
                // Simple monthly (every 1st = once monthly)
                else if (!isNaN(parseInt(parts[3])) && parts[4] === '*') {
                    this.selectedMonthDates = [parseInt(parts[3])];
                    this.repeatInterval = 1;
                    this.repeatUnit = 'month';
                }
            }
        },
        watch: {
            // Watch for changes in modelValue from parent
            modelValue: {
                handler(newCronValue) {
                        this.parseCronExpression(newCronValue);
                },
                immediate: true // Parse on component mount
            },
            // Watch for changes in internal state to emit updates
            repeatInterval() {
                this.updateSchedule();
            },
            repeatUnit(newUnit, oldUnit) {
                // Reset to default values when unit changes
                if (oldUnit && newUnit !== oldUnit && newUnit != 'month') {
                    this.selectedWeekOccurrence = 1;
                    this.selectedWeekDay = 0; // Sunday
                }
                this.updateSchedule();
            },
            selectedWeekDay() {
                this.updateSchedule();
            },
            selectedWeekOccurrence() {
                this.updateSchedule();
            }
        },
        mounted() {
            if (this.modelValue) {
                this.parseCronExpression(this.modelValue);
            } else {
                this.repeatInterval = 1;
                this.repeatUnit = 'week';
                this.selectedWeekOccurrence = 1;
                this.selectedWeekDay = 0;
            }
            this.updateSchedule();
        }
    });
</script>
