using AutoMapper;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class SubstanceMappingProfile : Profile
{
    public SubstanceMappingProfile()
    {
        CreateMap<Substance, SubstanceModel>();
        CreateMap<Substance, SubstanceSimpleModel>().ReverseMap();
        CreateMap<Substance, SubstanceItemModel>();
    }
}
