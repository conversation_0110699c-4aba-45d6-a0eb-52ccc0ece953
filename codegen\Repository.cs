using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using System.Collections.Generic;
using System.Threading.Tasks;
{{#if Dependencies}}{{#each Dependencies}}using {{this}};
{{/each}}{{/if}}
namespace {{Namespace}}
{
    public class {{EntityName}}Repository: TrackingGenericRepository<{{EntityName}}>, I{{EntityName}}Repository
    {
        private readonly IMapper _mapper;
        
        public {{EntityName}}Repository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
        {
            _mapper = mapper;
        }

        public async Task<IEnumerable<{{EntityName}}>> GetAllAsync() => await this.context.Set<{{EntityName}}>().ToListAsync();

        public Task<{{EntityName}}> GetByIdAsync(int id) => context.Set<{{EntityName}}>().FirstOrDefaultAsync(u => u.Id == id);
    }
}
