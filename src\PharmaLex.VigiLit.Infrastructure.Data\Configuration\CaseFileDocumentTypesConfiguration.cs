﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CaseFileDocumentTypesConfiguration : EntityBaseMap<CaseFileDocumentTypes>
{
    public override void Configure(EntityTypeBuilder<CaseFileDocumentTypes> builder)
    {
        base.Configure(builder);

        builder.ToTable("CaseFileDocumentTypes");

        builder.<PERSON><PERSON>ey(e => e.Id);
    }
}