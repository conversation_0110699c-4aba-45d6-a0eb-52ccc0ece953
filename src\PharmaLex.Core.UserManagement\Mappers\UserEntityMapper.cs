﻿using AutoMapper;
using PharmaLex.Core.UserManagement.Users;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserManagement.Mappers;

internal class UserEntityMapper<TUserEntity> : Profile
    where TUserEntity : EntityBase, IUserEntity
{
    public UserEntityMapper()
    {
        CreateMap<TUserEntity, UserFindResult>()
            .ForMember(um => um.Name, u
                => u.MapFrom(x => $"{x.GivenName} {x.FamilyName} ({x.Email})"))
            .ForMember(um => um.Value, u
                => u.MapFrom(x => x.Email));
    }
}
