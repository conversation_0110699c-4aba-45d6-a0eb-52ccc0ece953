using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using System.Linq;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class ContractVersionProfile : Profile
{
    public ContractVersionProfile()
    {
        CreateMap<ContractVersion, ContractVersionEditModel>();
        CreateMap<ContractVersionEditModel, ContractVersion>();
        CreateMap<ContractVersion, string>().ConvertUsing(c => c.SearchString);

        CreateMap<ContractVersion, ContractVersionViewModel>()
               .ForMember(cv => cv.LastModifiedByUserName, cvd => cvd.MapFrom(cv => $"{cv.User.GivenName} {cv.User.FamilyName}"))
               .ForMember(cv => cv.ContractWeekday, cvd => cvd.MapFrom(cv => cv.ContractWeekday.GetDescription()))
               .ForMember(cv => cv.ContractType, cvd => cvd.MapFrom(cv => cv.ContractType.GetDescription()))
               .ForMember(cv => cv.JournalTitles, cvd => cvd.MapFrom(cv => cv.ContractVersionJournals.Select(x => x.Journal.Name)))
               .ForMember(cv => cv.SearchPeriodDays, cvd => cvd.MapFrom((cv => cv.SearchPeriodDays == 0 ? "Unlimited" : string.Format("{0} days", cv.SearchPeriodDays))));
    }
}
