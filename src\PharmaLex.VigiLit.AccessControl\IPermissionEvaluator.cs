﻿namespace PharmaLex.VigiLit.AccessControl;

/// <summary>
/// Represents a permission evaluator for a specific type of access control context.
/// </summary>
/// <typeparam name="T">The type of context implementing <see cref="IAccessControlContext"/>.</typeparam>
public interface IPermissionEvaluator<in T> : IPermissionEvaluator
    where T : IAccessControlContext
{
    /// <summary>
    /// Checks whether the specified context has the required permissions.
    /// </summary>
    /// <param name="context">The context for which permissions need to be checked.</param>
    /// <returns>True if the context has the required permissions; otherwise, false.</returns>
    Task<bool> HasPermissions(T context);
}

/// <summary>
/// Represents a permission evaluator.
/// </summary>
public interface IPermissionEvaluator
{
}
