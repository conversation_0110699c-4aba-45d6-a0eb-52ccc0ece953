. "..\Helpers\ContainerFunctions.ps1"
. "..\Helpers\DaemonFunctions.ps1"

do { 
	Write-Host Infrastructure Menu -ForegroundColor DarkCyan 
	Write-Host =================== -ForegroundColor DarkCyan 
	Write-Host A. -NoNewLine -ForegroundColor Cyan 
	Write-Host Build SQL Image 
	Write-Host [Backspace].  -NoNewLine -ForegroundColor Cyan 
	Write-Host '<== Back to Main Menu' 
	
		$key = [Console]::ReadKey($true) 
	
	$keyChar = $key.KeyChar 
	
	if ($keyChar -eq 'a') 
	{ 
		CD .\Sql
		.\BuildSqlContainer.ps1 
		CD ..
	} 
} until($key.Key -eq 'Backspace' )  
