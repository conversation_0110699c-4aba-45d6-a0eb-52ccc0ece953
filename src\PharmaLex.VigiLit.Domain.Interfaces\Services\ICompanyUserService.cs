﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface ICompanyUserService
{
    Task<string> AddNewCompanyUser(CompanyUserModel companyUserModel);
    Task<string> AddNewInternalUser(CompanyUserModel companyUser);
    Task<CompanyUserModel> GetCompanyUser(int companyId, int userId);
    Task<CompanyUserModel> ResendSignOnInvitation(CompanyUserModel model);
    Task<CompanyUserModel> DeleteUserFromBounceList(CompanyUserModel model);
    Task<CompanyUserModel> DeleteUserFromBlockList(CompanyUserModel model);
    Task<List<EmailPreferenceModel>> GetEmailPreferencesAsync();
    Task<string> CreateInvitationLink(CompanyUserModel companyUser);
    Task<InvitationEmailModel> InitialiseInvitationEmail(CompanyUserModel companyUser);
    Task<CompanyUserWithCompanyModal> GetCompanyUserByEmail(string email);
    Task<UserModel> CreateCompanyUser(int companyId, string givenName, string familyName, string email, DateTime activationExpiryDate, string activationLink, List<int> emailPreferenceIds, CompanyUserType companyUserType);
    Task UpdateCompanyUser(int id, int companyId, bool active, string givenName, string familyName, string email, List<int> emailPreferenceIds, CompanyUserType companyUserType);
}