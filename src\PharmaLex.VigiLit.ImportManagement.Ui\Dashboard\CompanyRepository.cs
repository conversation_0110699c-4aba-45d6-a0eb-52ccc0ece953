using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

internal class CompanyRepository : TrackingGenericRepository<Company>, ICompanyRepository
{
    public CompanyRepository(PlxDbContext context, IUserContext userContext)
        : base(context, userContext.User)
    {
    }

    public async Task<List<Company>> GetAllCompaniesWithEmailSuppressions()
    {
        return await context.Set<Company>()
            .Include(c => c.CompanyUsers)
                .ThenInclude(u => u.User)
                .ThenInclude(u => u.EmailSuppression)
            .Where(c => c.CompanyUsers.Any(x => x.User.EmailSuppression != null))
            .AsNoTracking()
            .ToListAsync();
    }
}