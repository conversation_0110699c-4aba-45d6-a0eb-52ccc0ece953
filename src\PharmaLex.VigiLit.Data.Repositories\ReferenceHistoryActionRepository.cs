﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Data.Repositories;

public class ReferenceHistoryActionRepository : TrackingGenericRepository<ReferenceHistoryAction>, IReferenceHistoryActionRepository
{
    protected readonly IMapper _mapper;

    public ReferenceHistoryActionRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public Task<ReferenceHistoryAction?> GetByIdAsync(int id)
    {
        return context.Set<ReferenceHistoryAction>()
            .Where(a => a.Id == id)
            .FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<ReferenceHistoryActionModel>> GetReferenceActions(int referenceClassificationId)
    {
        var query = context.Set<ReferenceHistoryAction>()
            .Include(r => r.ReferenceClassification)
            .Include(r => r.ReferenceClassification.Reference)
            .Include(r => r.User)
            .Where(r => r.ReferenceClassification.Id == referenceClassificationId)
            .OrderByDescending(r => r.Id)
            .AsNoTracking();

        return await _mapper.ProjectTo<ReferenceHistoryActionModel>(query).ToListAsync();
    }

    public void ClearChangeTracker()
    {
        context.ChangeTracker.Clear();
    }
}