﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PharmaLex.VigiLit.ImportManagement.Entities;
public class ImportFile : VigiLitEntityBase
{
    public required string FileName { get; set; }
    public Guid BatchId { get; set; }
    public int FileSize { get; set; }
    [MaxLength(64)]
    [Column(TypeName = "varchar(64)")]
    public required string FileHash { get; set; }
}