﻿using PharmaLex.Core.UserManagement.Users;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserManagement;

#pragma warning disable S2326 // Like to be able to specify the user type for the repository? to revisit...

public interface IUserSearchService<TUserEntity>
    where TUserEntity : EntityBase, IUserEntity
{
    /// <summary>
    /// Returns a collection of users that match the search term.
    /// </summary>
    /// <param name="searchTerm"></param>
    /// <returns></returns>
    Task<IEnumerable<UserFindResult>> Find(string searchTerm);
}

#pragma warning restore S2326 