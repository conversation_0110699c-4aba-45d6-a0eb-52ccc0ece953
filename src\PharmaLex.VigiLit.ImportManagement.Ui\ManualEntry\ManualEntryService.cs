﻿using AutoMapper;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Ui.Generics;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

namespace PharmaLex.VigiLit.ImportManagement.Ui.ManualEntry;

internal class ManualEntryService : GenericCardService<IGenericCardRepository<ImportManualEntry>, ImportManualEntry, ManualEntryModel>, IManualEntryService
{
    public ManualEntryService(
        IGenericCardRepository<ImportManualEntry> manualEntryRepository,
        IImportReferenceRepository importReferenceRepository,
        IMapper mapper) : base(manualEntryRepository, importReferenceRepository, mapper)
    {
    }

    public override async Task<List<ImportDisplayCard>> GetCards()
    {
        var displayCards = new List<ImportDisplayCard>();
        var manualEntries = await GetAll();
        foreach (var manualEntry in manualEntries)
        {
            displayCards.Add(new ImportDisplayCard()
            {
                Id = manualEntry.Id.ToString(),
                ImportType = "Manual",
                Filename = "",
                Title = manualEntry.JournalTitle ?? string.Empty,
                ArticleTitle = manualEntry.Title ?? string.Empty,
                DateFrom = "",
                DateTo = "",
                FileCount = 0,
                FilesPlus = 0,
                CreatedBy = manualEntry.CreatedBy,
                LastUpdatedBy = manualEntry.LastUpdatedBy,
                FailedImportStatus = 0

            });
        }

        return displayCards;
    }

    public async Task<ImportManualEntry?> GetByDoi(string doi)
    {
        var importManualEntries = await _repository.GetAll();
        return importManualEntries.FirstOrDefault(x => x.Doi == doi);
    }
}
