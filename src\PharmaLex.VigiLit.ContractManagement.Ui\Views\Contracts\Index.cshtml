@using PharmaLex.Core.Web.Helpers
@model IEnumerable<PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.SimpleContractModel>

@{
    ViewData["Title"] = "Contracts";
}

@Html.AntiForgeryToken()

<div id="contract">
    <div class="sub-header">
        <h2>Contracts</h2>
        <div class="controls">
            <div>
                <button type="button" id="btnExport" class="btn btn-default btn-spinner" @@click="onExport" :disabled="disableExportButton" class="btn-default">
                    <span class="btn-label btn-span">Export</span>
                    <span class="spinner btn-span"></span>
                </button>
            </div>
        </div>
    </div>

    <div v-cloak>
        <section>
            <filtered-table :items="contract" :columns="columns" :filters="filters" :link="link"></filtered-table>
        </section>
    </div>
</div>

@section Scripts {
<script type="text/javascript">
        let contractsHistoryUrl = "@($"Contracts/History/")";
        var pageConfig = {
            appElement: "#contract",
            data: function () {
                return {
                    link: contractsHistoryUrl,
                    contract: @Html.Raw(AntiXss.ToJson(Model)),
                    columns: {
                        idKey: 'id',
                        config: [
                            {
                                dataKey: 'companyName',
                                sortKey: 'companyName',
                                header: 'Company',
                                edit: {},
                                type: 'text',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'projectName',
                                sortKey: 'projectName',
                                header: 'Project',
                                edit: {},
                                type: 'text',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'substanceName',
                                sortKey: 'substanceName',
                                header: 'Substance',
                                edit: {},
                                type: 'text',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'contractType',
                                sortKey: 'contractType',
                                header: 'Type',
                                edit: {},
                                type: 'text',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'contractWeekday',
                                sortKey: 'contractWeekday',
                                header: 'Weekday',
                                edit: {},
                                type: 'text',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'searchPeriod',
                                sortKey: 'searchPeriod',
                                header: 'Search Period',
                                edit: {},
                                type: 'text',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'contractStartDate',
                                sortKey: 'contractStartDate',
                                header: 'Contract Start',
                                edit: {
                                    convert: e => {
                                        const date = moment(e);
                                        if (date.year() < 1901)
                                        {
                                            return 'Not Started';
                                        }
                                        return date.format('DD MMM YYYY')
                                    }
                                },
                                type: 'date',
                                style: 'width: 12.5%;'
                            },
                            {
                                dataKey: 'isActive',
                                sortKey: 'isActive',
                                header: 'Active',
                                type: 'bool',
                                style: 'width: 12.5%;'
                            }
                        ]
                    },
                    filters: [
                        {
                            key: 'companyName',
                            options: [],
                            type: 'search',
                            header: 'Search Company',
                            fn: v => p => p.companyName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'projectName',
                            options: [],
                            type: 'search',
                            header: 'Search Project',
                            fn: v => p => p.projectName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'substanceName',
                            options: [],
                            type: 'search',
                            header: 'Search Substance',
                            fn: v => p => p.substanceName.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'contractType',
                            options: [],
                            type: 'search',
                            header: 'Search Type',
                            fn: v => p => p.contractType.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'contractWeekday',
                            options: [],
                            type: 'search',
                            header: 'Search Weekday',
                            fn: v => p => p.contractWeekday.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        },
                        {
                            key: 'searchPeriod',
                            options: [],
                            type: 'search',
                            header: 'Search Search Period',
                            fn: v => p => p.searchPeriod.toLowerCase().includes(v.toLowerCase()),
                            convert: v => v
                        }
                    ]
                };
            },
            computed: {
                disableExportButton() {
                    return this.contract.length == 0;
                }
            },
            methods: {
                onExport: function () {
                    let exportUrl = `/Contracts/Export`;
                    $("#btnExport").attr('data-loading', '');
                    var onFailure = function() {
                        plx.toast.show('Unable to export, please try again', 2, 'failed', null, 2500);
                    }
                    var onComplete = function() {
                        $("#btnExport").removeAttr('data-loading');
                    }
                    DownloadFile.fromUrl(exportUrl, null, null, onFailure, onComplete);
                }
            },
        };
</script>
}

@section VueComponentScripts{
<partial name="Components/Vue3/FilteredTable" />
}
