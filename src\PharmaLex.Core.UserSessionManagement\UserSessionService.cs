﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Routing;
using PharmaLex.Core.UserSessionManagement.Entities;

namespace PharmaLex.Core.UserSessionManagement;

public class UserSessionService : IUserSessionService
{
    private readonly IUserSessionRepository _userSessionRepository;

    public UserSessionService(IUserSessionRepository userSessionRepository)
    {
        _userSessionRepository = userSessionRepository;
    }

    public async Task<UserSession?> ProcessIncomingSession(ActionExecutingContext context, int userId, int sessionLengthSeconds)
    {
        var userSession = await _userSessionRepository.GetByUserIdAsync(userId);

        if (!IsLoggingOut(context.RouteData.Values) && UserFoundAndAuthorised(userId))
        {

            if (userSession != null)
            {
                var sessionValid = IsValidSession(userSession, context.HttpContext.Session.Id, context.RouteData.Values);

                if (sessionValid)
                {
                    // Immediately after recording logon in session table, session data will be empty so record it
                    if (IsNewSession(userSession))
                    {
                        InitiateSessionStorage(context);
                        userSession.SessionData = context.HttpContext.Session.Id;
                    }

                    userSession.SessionExpiry = DateTime.UtcNow.AddSeconds(sessionLengthSeconds);
                    await UpdateUserSessionAsync(userSession);
                }
                else
                {
                    throw new InvalidOperationException($"Session not present for user {userId}");
                }
            }
            else
            {
                throw new InvalidOperationException($"Session not present for user {userId}");
            }
        }

        return userSession;
    }


    public async Task<bool> ProcessTimerCall(HttpContext context, int userId)
    {
        var sessionValid = false;
        var userSession = await _userSessionRepository.GetByUserIdAsync(userId);

        if (!IsLoggingOut(context.Request.RouteValues) && UserFoundAndAuthorised(userId) && userSession != null)
        {
            sessionValid = IsValidSession(userSession, context.Session.Id, context.Request.RouteValues);
        }

        return sessionValid;
    }

    private static bool UserFoundAndAuthorised(int userId)
    {
        return userId != 0;
    }

    public bool IsValidSession(UserSession userSession, string sessionId, RouteValueDictionary routes)
    {
        var validSession = false;

        if (IsLoggingOut(routes))
        {
            validSession = true;
        }

        if (userSession != null && userSession.SessionData == null)
        {
            validSession = true;
        }
        else if (userSession?.SessionData == sessionId && DateTime.UtcNow <= userSession?.SessionExpiry)
        {
            validSession = true;
        }
        else
        {
            // not valid session
        }

        return validSession;
    }

    private static void InitiateSessionStorage(ActionExecutingContext context)
    {
        // in order to persist session id, have to record any value in SessionData
        context.HttpContext.Session.Set("SessionData", [0]);
    }

    private static bool IsNewSession(UserSession userSession)
    {
        return userSession.SessionData == null;
    }

    private static bool IsLoggingOut(RouteValueDictionary routes)
    {
        return routes.Any(x => x.Value != null && x.Value.Equals("LogoutEx"));
    }

    public async Task InitialiseUserSession(int userId, string ipAddress)
    {
        await DeleteUserSessionAsync(userId);

        var newSession = new UserSession
        {
            UserId = userId,
            SessionExpiry = DateTime.UtcNow,
            IpAddress = ipAddress
        };

        _userSessionRepository.Add(newSession);
        await _userSessionRepository.SaveChangesAsync();
    }

    public async Task DeleteUserSessionsAsync(List<int> userIds)
    {
        if (userIds.Count > 0)
        {
            var userSessions = await _userSessionRepository.GetSessionsForUsersAsync(userIds);
            foreach(var userSession in userSessions)
            {
                _userSessionRepository.Remove(userSession);
            }
            await _userSessionRepository.SaveChangesAsync();
        }
    }

    public async Task DeleteUserSessionAsync(int userId)
    {
        var userSessions = await _userSessionRepository.GetAllByUserIdAsync(userId);

        foreach (var userSession in userSessions)
        {
            _userSessionRepository.Remove(userSession);
        }

        await _userSessionRepository.SaveChangesAsync();
    }

    private async Task UpdateUserSessionAsync(UserSession userSession)
    {
        if (userSession != null)
        {
            await _userSessionRepository.SaveChangesAsync();
        }
    }

}