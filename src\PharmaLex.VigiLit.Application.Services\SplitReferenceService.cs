﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ReferenceManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Application.Services;
public class SplitReferenceService : ISplitReferenceService
{

    private readonly IReferenceClassificationRepository _referenceClassificationRepository;
    private readonly ICompanyInterestRepository _companyInterestRepository;
    private readonly IClassificationService _classificationService;
    private readonly IMapper _mapper;

    public SplitReferenceService(IReferenceClassificationRepository referenceClassificationRepository, ICompanyInterestRepository companyInterestRepository,
                                                IClassificationService classificationService, IMapper mapper)
    {
        _referenceClassificationRepository = referenceClassificationRepository;
        _companyInterestRepository = companyInterestRepository;
        _classificationService = classificationService;
        _mapper = mapper;
    }

    public async Task CreateClassification(ReferenceSplitModel referenceSplitModel)
    {
        var classification = _mapper.Map<ReferenceClassification>(referenceSplitModel.ReferenceClassification);

        var newClassification = classification.Copy();

        newClassification.IsActive = true;
        newClassification.ReferenceId = referenceSplitModel.ReferenceId;
        newClassification.ReferenceState = ReferenceState.Split;

        _referenceClassificationRepository.Add(newClassification);
        await _referenceClassificationRepository.SaveChangesAsync();
        await AddCompanyInterests(referenceSplitModel.SelectedCompanyIds, newClassification);

        _classificationService.AddReferenceHistoryAction(newClassification.Id, ReferenceHistoryActionType.SplitReference);
        await _classificationService.SaveReferenceHistoryChangesAsync();
    }

    private async Task AddCompanyInterests(List<int> companyIds, ReferenceClassification classification)
    {
        foreach (var id in companyIds)
        {
            var companyInterest = new CompanyInterest()
            {
                ReferenceClassificationId = classification.Id,
                ReferenceId = classification.ReferenceId,
                CompanyId = id
            };
            companyInterest.AddEmailRelevantEvent(EmailRelevantEventActionType.SplitReference, classification.ClassificationCategoryId);
            _companyInterestRepository.Add(companyInterest);

        }
        await _companyInterestRepository.SaveChangesAsync();
    }
}
