﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;
using PharmaLex.VigiLit.ReferenceManagement;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;

public class ImportContractReferenceClassificationRepository : TrackingGenericRepository<ImportContractReferenceClassification>, IImportContractReferenceClassificationRepository
{
    private readonly IMapper _mapper;

    public ImportContractReferenceClassificationRepository(PlxDbContext context, IUserContext userContext, IMapper mapper)
        : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task<IEnumerable<ImportContractReferenceClassification>> GetForSigning(int importId, int batchSize)
    {
        var classifications = await context.Set<ImportContractReferenceClassification>()
        .Include(icrc => icrc.ImportContract)
        .Include(icrc => icrc.ReferenceClassification)
        .Where(icrc =>
            // belongs to the selected import
            icrc.ImportContract.ImportId == importId &&
            // status allows signing
            (icrc.ReferenceClassification.ReferenceState == ReferenceState.Approved ||
             icrc.ReferenceClassification.ReferenceState == ReferenceState.Preclassified))
        .OrderBy(icrc => icrc.Id)
        .ToListAsync();

        return classifications.DistinctBy(icrc => icrc.ReferenceClassification.Id).Take(batchSize);

    }

    public async Task<DashboardDetailsResult> GetImportDashboardDetailsRows(int importId, int page, int pageSize)
    {
        var query = context.Set<ImportContractReferenceClassification>()
            .Include(icrc => icrc.ImportContract)
                .ThenInclude(ic => ic.Contract)
                    .ThenInclude(c => c.Substance)
            .Include(icrc => icrc.ImportContract)
                .ThenInclude(ic => ic.Contract)
                    .ThenInclude(c => c.Project)
                        .ThenInclude(p => p.Company)
            .Include(icrc => icrc.ReferenceClassification)
                .ThenInclude(rc => rc.Reference)
            .Where(icrc => icrc.ImportContract.ImportId == importId)
            .OrderBy(icrc => icrc.ImportContract.Contract.Project.Company.Name)
                .ThenBy(icrc => icrc.ImportContract.Contract.Project.Name)
                .ThenBy(icrc => icrc.ImportContract.Contract.Substance.Name)
                // enforce order for server-side paging when there are many rows from the same contract.
                .ThenBy(icrc => icrc.ReferenceClassificationId)
            .AsNoTracking();

        // total rows
        var totalRowCount = await query.CountAsync();

        // pagination
        query = query
            .Skip((page - 1) * pageSize)
            .Take(pageSize);

        var rows = await _mapper.ProjectTo<DashboardDetailsRowModel>(query).ToListAsync();

        return new DashboardDetailsResult()
        {
            Rows = rows,
            TotalRowCount = totalRowCount
        };
    }

    public async Task<IEnumerable<DashboardDetailsRowModel>> GetImportDashboardDetailsExport(int importId)
    {
        var query = context.Set<ImportContractReferenceClassification>()
            .Include(icrc => icrc.ImportContract)
                .ThenInclude(ic => ic.Contract)
                    .ThenInclude(c => c.Substance)
            .Include(icrc => icrc.ImportContract)
                .ThenInclude(ic => ic.Contract)
                    .ThenInclude(c => c.Project)
                        .ThenInclude(p => p.Company)
            .Include(icrc => icrc.ReferenceClassification)
                .ThenInclude(rc => rc.Reference)
            .Where(icrc => icrc.ImportContract.ImportId == importId)
            .OrderBy(icrc => icrc.ImportContract.Contract.Project.Company.Name)
                .ThenBy(icrc => icrc.ImportContract.Contract.Project.Name)
                .ThenBy(icrc => icrc.ImportContract.Contract.Substance.Name)
                .ThenBy(icrc => icrc.ReferenceClassificationId)
            .AsNoTracking();

        return await _mapper.ProjectTo<DashboardDetailsRowModel>(query).ToListAsync();
    }

    public void ClearChangeTracker()
    {
        context.ChangeTracker.Clear();
    }
}
