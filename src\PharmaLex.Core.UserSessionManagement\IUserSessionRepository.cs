﻿using PharmaLex.Core.UserSessionManagement.Entities;
using PharmaLex.DataAccess;

namespace PharmaLex.Core.UserSessionManagement;

public interface IUserSessionRepository : ITrackingRepository<UserSession>
{
    Task<UserSession?> GetByUserIdAsync(int userId);

    Task<IEnumerable<UserSession>> GetAllByUserIdAsync(int userId);

    Task<IEnumerable<UserSession>> GetSessionsForUsersAsync(List<int> userIds);
}