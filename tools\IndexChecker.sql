--
-- Run this script and then copy and paste the "MaintenanceScript" column into a new query window in SSMS and execute it
--

SELECT
    dbschemas.[name] AS SchemaName,
    dbtables.[name] AS TableName,
    dbindexes.[name] AS IndexName,
    dbindexes.[index_id],
    indexstats.avg_fragmentation_in_percent,
    indexstats.page_count,
    CASE
        WHEN indexstats.avg_fragmentation_in_percent < 5 THEN 'Ignore'
        WHEN indexstats.avg_fragmentation_in_percent BETWEEN 5 AND 30 THEN 'Reorganize'
        WHEN indexstats.avg_fragmentation_in_percent > 30 THEN 'Rebuild'
        ELSE 'N/A'
        END AS ActionRecommendation,
    CASE
        WHEN indexstats.avg_fragmentation_in_percent BETWEEN 5 AND 30 THEN
            'ALTER INDEX [' + dbindexes.[name] + '] ON [' + dbschemas.[name] + '].[' + dbtables.[name] + '] REORGANIZE;'
        WHEN indexstats.avg_fragmentation_in_percent > 30 THEN
            'ALTER INDEX [' + dbindexes.[name] + '] ON [' + dbschemas.[name] + '].[' + dbtables.[name] + '] REBUILD WITH (ONLINE = ON);'
        ELSE
            '-- No action required'
        END AS MaintenanceScript


FROM
    sys.dm_db_index_physical_stats (DB_ID(), NULL, NULL, NULL, 'LIMITED') AS indexstats
        INNER JOIN
    sys.tables dbtables ON dbtables.[object_id] = indexstats.[object_id]
        INNER JOIN
    sys.schemas dbschemas ON dbtables.[schema_id] = dbschemas.[schema_id]
        INNER JOIN
    sys.indexes AS dbindexes ON dbindexes.[object_id] = indexstats.[object_id]
        AND indexstats.index_id = dbindexes.index_id
WHERE
    indexstats.database_id = DB_ID()
  AND dbindexes.index_id > 0  -- Exclude heap tables
  AND indexstats.page_count > 1000
ORDER BY
    indexstats.avg_fragmentation_in_percent DESC;
