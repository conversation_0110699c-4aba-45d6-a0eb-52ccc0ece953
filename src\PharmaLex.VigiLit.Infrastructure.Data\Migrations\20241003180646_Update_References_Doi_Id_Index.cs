﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class Update_References_Doi_Id_Index : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("IF EXISTS (SELECT * FROM sysindexes where name = 'IX_References_Doi_Id') DROP INDEX [IX_References_Doi_Id] ON [dbo].[References] WITH ( ONLINE = OFF )");
            migrationBuilder.Sql("CREATE NONCLUSTERED INDEX [IX_References_Doi_Id] ON [dbo].[References] ([Doi] ASC,[Id] ASC) INCLUDE(Abstract,AffiliationTextFirstAuthor,Authors,CountryOfOccurrence,DateRevised,FullPagination,Issn,Issue,[Language],PMID,PublicationType,PublicationYear,Title,Volume,VolumeAbbreviation,PeriodEnd,PeriodStart,CreatedDate,CreatedBy,LastUpdatedDate,LastUpdatedBy,Keywords,MeshHeadings,JournalTitle)");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP INDEX [IX_References_Doi_Id] ON [dbo].[References] WITH ( ONLINE = OFF )");
        }
    }
}
