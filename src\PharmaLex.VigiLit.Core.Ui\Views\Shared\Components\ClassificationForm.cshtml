﻿@using Microsoft.FeatureManagement
@inject IFeatureManager FeatureManager
@{
    const string enableSpecialSituations = "EnableSpecialSituations";
    var isSpecialSituationsEnabled = await FeatureManager.IsEnabledAsync(enableSpecialSituations);
}

<script type="text/x-template" id="classification-form-template">

        <div>

            <h2>{{header}}</h2>

            <div class="form-group inline-flex ai-status ai-status-success" v-if="isSignedReferenceState" >
                <i class="m-icon">assignment_turned_in</i>
                <div>The reference is classified, and the user will not receive AI suggestion.</div>
            </div>

            <div class="form-group inline-flex ai-status ai-status-success" v-if= "classificationMode == 'preclassification' && isAiEnabled === false && isSignedReferenceState === false">
                <i class="m-icon">assignment_turned_in</i>
                <div>AI suggestions are currently turned off</div>
            </div>

            <div class="form-group inline-flex ai-status ai-status-awaiting-response" v-if="displayAiStatusAwaitingResponse">
                <i class="m-icon">assignment_late</i>
                <div>SmartPhlex has been unable to find any suggestions at this time due to the AI API service. Please try again later.</div>
            </div>

            <div class="form-group inline-flex ai-status ai-status-success" v-if="displayAiStatusSuccess &&  isSignedReferenceState === false">
                <i class="m-icon">assignment_turned_in</i>
                <div>SmartPhlex has finished analyzing the reference. Please take a look at the suggestions below</div>

            </div>

            <div class="form-group inline-flex ai-status ai-status-failed" v-if="displayAiStatusFailed">
                <i class="m-icon">assignment_late</i>
                <div>There has been an error on the Vigilit side, Please try again later.</div>
            </div>

            <div class="ai-retry" v-if="displayAiStatusFailed || displayAiStatusAwaitingResponse">
                <button type="button"  id="retryBtn" @@click="$emit('retryai', { referenceClassification, aiSuggestedClassification })"  class="btn btn-default btn-spinner"><span class="btn-label btn-span">Retry AI Suggestion</span> <span class="spinner btn-span"></span></button>
            </div>

    @* AI Suggestion panel *@
            <div :class="['form-group', 'ai-suggestion', 'category', { 'pending': aiCategoryDecisionPending }, { 'accepted': aiCategoryDecisionAccepted }, { 'rejected': aiCategoryDecisionRejected }]" v-if="displayAiCategorySuggestion">
                <div class="inline-flex header">
                    <div class="header-text" v-if="aiCategoryDecisionPending">SmartPhlex has suggested</div>
                    <div class="header-text" v-if="aiCategoryDecisionAccepted">Complete</div>
                    <div class="header-text" v-if="aiCategoryDecisionRejected">Choose your option</div>
                    <div class="header-buttons" v-if="aiCategoryDecisionPending">
                        <div class="icon-button" @@click="acceptCategorySuggestion" v-if="canAcceptCategorySuggestion"><i class="m-icon">done</i></div>
                        <div class="icon-button disabled" v-else><i class="m-icon">done</i></div>
                        <div class="icon-button" @@click="rejectCategorySuggestion"><i class="m-icon">close</i></div>
                    </div>
                    <div class="header-buttons" v-else>
                        <div class="undo-button" @@click="undoCategoryDecision">Undo</div>
                    </div>
                </div>
                <div class="suggested-value">
                    <div>Classification Category</div>
                    <div v-if="aiCategoryDecisionRejected">
                        <select :id="`refclassification_${referenceClassification.id}`" v-model="referenceClassification.classificationCategoryId" :disabled="disableClassificationCategory">
                            <option value="0" disabled selected hidden>Select Classification Category</option>
                            <option v-for="category in classificationCategories" :value="category.id" >
                                {{category.name}}
                            </option>
                        </select>
                    </div>
                    <div class="value-box" v-else>
                        <template v-if="aiSuggestedClassification.category">{{aiSuggestedClassification.category}}</template>
                        <template v-else>No Suggestion Provided</template>
                    </div>
                    
                @if (isSpecialSituationsEnabled)
                {
                    <div v-if="showPotentialCaseAdditionalFields">
                            <div class="potential-case-additional-fields-ai" v-if="showPotentialCaseAdditionalFields && aiCategoryDecisionRejected && isPotentialCase">
                                <div for="additions">Special Situation</div>
                                <div class="custom-select">
                                     <multi-select :item-list="potentialCaseAdditionalFields.listItems" :placeholder="'Select Special Situation'"
                                                  :selections="potentialCaseAdditionalFields"
                                          :enable-filter="true">
                                    </multi-select>
                                </div>
                            </div>
                    </div>

                }

                </div>

                <div class="reasoning">
                    <div>AI Reasoning</div>
                    <div v-if="aiSuggestedClassification.categoryReason">{{aiSuggestedClassification.categoryReason}}</div>
                    <div v-else>No Reason Provided</div>
                </div>
                <div class="previous-value" v-if="previousCategoryName">
                    <b>Previous Value:</b> {{previousCategoryName}}
                </div>
            </div>

    @* Non-AI panel *@
            <div class="form-group" v-if="displayCategory">
                <label :for="`refclassification_${referenceClassification.id}`">Classification Category</label>
                <div class="custom-select">
                    <select :id="`refclassification_${referenceClassification.id}`" v-model="referenceClassification.classificationCategoryId" :disabled="disableClassificationCategory">
                        <option value="0" disabled selected hidden>Select Classification Category</option>
                        <option v-for="category in classificationCategories" :value="category.id" >
                            {{category.name}}
                        </option>
                    </select>
                </div>
                
                @if (isSpecialSituationsEnabled)
                {
                    <div v-if="showPotentialCaseAdditionalFields">
                        <div class="potential-case-additional-fields-non-ai" v-if="showPotentialCaseAdditionalFields && isPotentialCase">
                            <label for="additions">Special Situation</label>
                            <div class="custom-select">
                                 <multi-select :item-list="potentialCaseAdditionalFields.listItems" :placeholder="'Select Special Situation'"
                                              :selections="potentialCaseAdditionalFields"
                                      :enable-filter="true">
                                </multi-select>
                            </div>
                        </div>
                    </div>
                }

            </div>

            <div class="form-group" v-if="displayMinimalCriteria">
                <label :for="`criteria_${referenceClassification.id}`">Minimal Criteria</label>
                <div class="custom-select">
                    <select :id="`criteria_${referenceClassification.id}`" v-model="referenceClassification.minimalCriteria">
                        <option value="" disabled selected hidden>Select Minimal Criteria</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>

            <div class="form-group" v-if="displayCountryOfOccurrence">
                <label :for="`country_${referenceClassification.id}`">Country of Occurrence</label>
                <div class="custom-select">
                    <select :id="`country_${referenceClassification.id}`" v-model="referenceClassification.countryOfOccurrence">
                        <option value="" disabled selected hidden>Select Country of Occurrence</option>
                        <option v-for="country in countries" :value="country">
                            {{country}}
                        </option>
                    </select>
                </div>
            </div>

            <div class="form-group" v-if="displayPSURRelevanceAbstract">
                <label :for="`psurrelevanceabstract_${referenceClassification.id}`">PSUR Relevant</label>
                <div class="custom-select">
                    <select :id="`psurrelevanceabstract_${referenceClassification.id}`" v-model="referenceClassification.psurRelevanceAbstract" :disabled="disablePSURRelevanceAbstract">
                        <option value="N/A" disabled selected hidden>Select PSUR Relevant</option>
                        <option value="Yes">Yes</option>
                        <option value="No">No</option>
                    </select>
                </div>
            </div>

            <div class="form-group" v-if="displayPvSafetyDatabaseId">
                <label :for="`pvsafetydatabaseid_${referenceClassification.id}`">PV Safety Database ID</label>
                <div>
                    <input :id="`pvsafetydatabaseid_${referenceClassification.id}`" v-model="referenceClassification.pvSafetyDatabaseId" type="text" spellcheck="false"/>
                </div>
            </div>

            <div :class="['form-group', 'ai-suggestion', { 'pending': aiDosageFormDecisionPending }, { 'accepted': aiDosageFormDecisionAccepted }, { 'rejected': aiDosageFormDecisionRejected }]" v-if="displayAiDosageFormSuggestion">
                <div class="inline-flex header">
                    <div class="header-text" v-if="aiDosageFormDecisionPending">SmartPhlex has suggested</div>
                    <div class="header-text" v-if="aiDosageFormDecisionAccepted">Complete</div>
                    <div class="header-text" v-if="aiDosageFormDecisionRejected">Choose your option</div>
                    <div class="header-buttons" v-if="aiDosageFormDecisionPending">
                        <div class="icon-button" @@click="acceptDosageFormSuggestion" v-if="canAcceptDosageFormSuggestion"><i class="m-icon">done</i></div>
                        <div class="icon-button disabled" v-else><i class="m-icon">done</i></div>
                        <div class="icon-button" @@click="rejectDosageFormSuggestion"><i class="m-icon">close</i></div>
                    </div>
                    <div class="header-buttons" v-else>
                        <div class="undo-button" @@click="undoDosageFormDecision">Undo</div>
                    </div>
                </div>
                <div class="suggested-value">
                    <div>Dosage Form</div>
                    <div v-if="aiDosageFormDecisionRejected">
                        <div class="dosage-form">
                            <div>
                                <input :id="`dosageform_${referenceClassification.id}`" type="text" v-model="referenceClassification.dosageForm" class="dosageForm" :disabled="disableDosageForm" maxlength="100" />
                            </div>
                            <div class="dosage-form-badges" v-if="!disableDosageForm">
                                <span v-for="option in dosageOptions" :value="option" v-on:click="referenceClassification.dosageForm = option">{{option}}</span>
                            </div>
                            <div class="dosage-form-badges disabled" v-if="disableDosageForm">
                                <span v-for="option in dosageOptions" :value="option">{{option}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="value-box" v-else>
                        <template v-if="aiSuggestedClassification.dosageForm">{{aiSuggestedClassification.dosageForm}}</template>
                        <template v-else>No Suggestion Provided</template>
                    </div>
                </div>
                <div class="reasoning">
                    <div>AI Reasoning</div>
                    <div v-if="aiSuggestedClassification.dosageFormReason">{{aiSuggestedClassification.dosageFormReason}}</div>
                    <div v-else>No Reason Provided</div>
                </div>
                <div class="previous-value" v-if="previousDosageForm">
                    <b>Previous Value:</b> {{previousDosageForm}}
                </div>
            </div>

            <div class="form-group" v-if="displayDosageForm">
                <label :for="`dosageform_${referenceClassification.id}`">Dosage Form</label>
                <div class="dosage-form">
                    <div>
                        <input :id="`dosageform_${referenceClassification.id}`" type="text" v-model="referenceClassification.dosageForm" class="dosageForm" :disabled="disableDosageForm" maxlength="100" />
                    </div>
                    <div class="dosage-form-badges" v-if="!disableDosageForm">
                        <span v-for="option in dosageOptions" :value="option" v-on:click="referenceClassification.dosageForm = option">{{option}}</span>
                    </div>
                    <div class="dosage-form-badges disabled" v-if="disableDosageForm">
                        <span v-for="option in dosageOptions" :value="option">{{option}}</span>
                    </div>
                </div>
            </div>

            <div class="form-group" v-if="displayCountryOfOccurrence">
                <input :id="`countryverified_${referenceClassification.id}`" type="checkbox" value="true" v-model="countryOfOccurrenceVerified" />
                <label :for="`countryverified_${referenceClassification.id}`">Country of Occurrence Verified</label>
            </div>

        </div>
        <div v-if="classificationMode == 'preclassification' && (referenceClassification.preclassifierName || (classificationGroupSize > 1 || !referenceClassification.isActive))">
            <div class="separated">
                <span class="preClassifierName">{{referenceClassification.preclassifierName}}</span>
            </div>
            <div class="separated flex justify-end pb-0" v-if="classificationGroupSize > 1 || !referenceClassification.isActive">
                <label class="switch-container switch-active-inactive" tabindex="0" aria-label="Active Toggle"
                v-on:keydown.enter.prevent.stop="referenceClassification.isActive = !referenceClassification.isActive"
                v-on:keydown.space.prevent.stop="referenceClassification.isActive = !referenceClassification.isActive">
                    <input :id="`isActive_${referenceClassification.id}`" type="checkbox" class="switch" :checked="referenceClassification.isActive" v-model="referenceClassification.isActive" />
                    <label :for="`isActive_${referenceClassification.id}`" class="switch"></label>
                </label>
            </div>
        </div>
        <div v-if="classificationMode == 'repreclassification'">
            <div class="separated">
              <span class="preClassifierName">{{referenceClassification.preclassifierName}}</span>
            </div>
            <div class="separated flex justify-end pb-0">
              <button type="button" @@click="$emit('repreclassify', referenceClassification)" :disabled="referenceClassification.buttonDisabled" class="btn-default">Update</button>
            </div>
        </div>
        <div v-if="classificationMode == 'classification'">
            <div class="separated">
                <span class="preClassifierName">{{referenceClassification.preclassifierName}}</span>
            </div>
            <div class="separated flex justify-end pb-0">
                <button type="button" @@click="$emit('approve', referenceClassification)" :disabled="!isValidClassification" class="btn-default">Approve Reference</button>
            </div>
        </div>
        <div v-if="classificationMode == 'reclassification'">
            <div class="separated">
                <span class="preClassifierName">{{referenceClassification.preclassifierName}}</span>
            </div>
            <div class="separated flex justify-end pb-0">
                <button type="button" @@click="emitReapprove(referenceClassification)" :disabled="referenceClassification.buttonDisabled" class="btn-default">Update</button>
            </div>
        </div>
        <div v-if="classificationMode == 'referenceHistory'">
            <div class="separated">
                <span class="preClassifierName">{{referenceClassification.preclassifierName}}</span>
            </div>
            <div class="separated flex justify-end pb-0">
                <button type="button" @@click="emitReapprove(referenceClassification)" :disabled="referenceClassification.buttonDisabled" class="btn-default">Update</button>
            </div>
        </div>

</script>
<script type="text/javascript">
    const dosages = [
        "N.A",
        "N.I",
        "IN VITRO",
        "I.V",
        "IN VIVO",
        "P.O"
    ];

    const aiDecisionTypes = {
        none: 0,
        accepted: 1,
        rejected: 2
    };

    vueApp.component('classification-form', {
        template: '#classification-form-template',
        props: {
            classificationCategories: Array,
            countries: Array,
            referenceClassification: Object,
            classificationMode: String,
            header: String,
            classificationGroupSize: Number,
            isCountryOfOccurrenceVerified: Boolean,
            displayAiSuggestions: Boolean,
            aiSuggestedClassification: Object,
            retryInProgress: Boolean,
            showPotentialCaseAdditionalFields: {
                type: Boolean,
                default: false
            },
            potentialCaseAdditionalFields: {
                type: Object,
                default: {}
            }
        },
        emits: ['classify', 'repreclassify', 'approve', 'reapprove', 'retryai'],
        data: function () {
            return {
                dosageOptions: dosages,
                classificationCaseName: null,
                countryOfOccurrenceVerified: false,
                countryOfOccurrence: this.referenceClassification.countryOfOccurrence,
                previousCategoryId: this.referenceClassification.classificationCategoryId,
                previousDosageForm: this.referenceClassification.dosageForm,
                referenceState: this.referenceClassification.referenceState,
                potentialCaseAdditionalFieldSelections: {
                    selectedIds: []
                }
            };
        },
        methods: {
            emitReapprove: function (referenceClassification) {
                this.$emit('reapprove', referenceClassification);
                this.countryOfOccurrenceVerified = this.isCountryOfOccurrenceVerified;
            },
            acceptCategorySuggestion: function () {
                this.referenceClassification.classificationCategoryId = this.aiSuggestedClassification.categoryId;
                this.aiSuggestedClassification.categoryDecision = aiDecisionTypes.accepted;
            },
            rejectCategorySuggestion: function () {
                this.referenceClassification.classificationCategoryId = 0;
                this.aiSuggestedClassification.categoryDecision = aiDecisionTypes.rejected;
            },
            undoCategoryDecision: function () {
                this.referenceClassification.classificationCategoryId = this.previousCategoryId;
                this.aiSuggestedClassification.categoryDecision = aiDecisionTypes.none;
            },
            acceptDosageFormSuggestion: function () {
                this.referenceClassification.dosageForm = this.aiSuggestedClassification.dosageForm;
                this.aiSuggestedClassification.dosageFormDecision = aiDecisionTypes.accepted;
            },
            rejectDosageFormSuggestion: function () {
                this.referenceClassification.dosageForm = null;
                this.aiSuggestedClassification.dosageFormDecision = aiDecisionTypes.rejected;
            },
            undoDosageFormDecision: function () {
                this.referenceClassification.dosageForm = this.previousDosageForm;
                this.aiSuggestedClassification.dosageFormDecision = aiDecisionTypes.none;
            }
        },
        computed: {
            isValidClassification() {
                return this.referenceClassification.classificationCategoryId > 0
                    && (!this.displayMinimalCriteria || !!this.referenceClassification.minimalCriteria)
                    && (!this.displayCountryOfOccurrence || (!!this.referenceClassification.countryOfOccurrence && this.countryOfOccurrenceVerified))
                    && (!this.displayPSURRelevanceAbstract || this.referenceClassification.psurRelevanceAbstract !== 'N/A')
                    && (!!this.referenceClassification.dosageForm);
            },
            isSignedReferenceState() {
                return this.referenceClassification.referenceState == 3 || this.referenceClassification.referenceStateText == "Preclassified";
            },
            isPotentialCase() {
                return this.referenceClassification.classificationCategoryId == 1;
            },
            isAiEnabled() {
                return this.classificationMode == 'preclassification' && this.displayAiSuggestions && this.aiSuggestedClassification;
            },
            displayAiStatusAwaitingResponse() {
                return this.isAiEnabled && this.aiSuggestedClassification.status == aiAnalysisStatusTypes.awaitingResponse && this.referenceClassification.isActive && !this.isSignedReferenceState;
            },
            displayAiStatusSuccess() {
                return this.isAiEnabled && this.aiSuggestedClassification.status == aiAnalysisStatusTypes.success && this.referenceClassification.isActive;
            },
            displayAiStatusFailed() {
                return this.isAiEnabled && this.aiSuggestedClassification.status == aiAnalysisStatusTypes.failed && this.referenceClassification.isActive;
            },
            displayAiCategorySuggestion() {
                return this.isAiEnabled && this.referenceClassification.isActive && !this.aiFailedOrAwaitingResponse && !this.isSignedReferenceState;
            },
            canAcceptCategorySuggestion() {
                return this.aiSuggestedClassification.categoryId;
            },
            aiCategoryDecisionPending() {
                return this.aiSuggestedClassification.categoryDecision == aiDecisionTypes.none
            },
            aiCategoryDecisionAccepted() {
                return this.aiSuggestedClassification.categoryDecision == aiDecisionTypes.accepted;
            },
            aiCategoryDecisionRejected() {
                return this.aiSuggestedClassification.categoryDecision == aiDecisionTypes.rejected;
            },
            aiFailedOrAwaitingResponse() {
                return this.displayAiStatusFailed || this.displayAiStatusAwaitingResponse;
            },
            previousCategoryName() {
                if (this.previousCategoryId > 0) {
                    return this.classificationCategories.find(x => x.id === this.previousCategoryId)?.name;
                } else {
                    return null;
                }
            },
            disableClassificationCategory() {
                return !this.referenceClassification.isActive;
            },
            disablePSURRelevanceAbstract() {
                return !this.referenceClassification.isActive;
            },
            displayCategory() {
                return !this.displayAiCategorySuggestion || (this.displayAiCategorySuggestion && this.aiFailedOrAwaitingResponse);
            },
            displayMinimalCriteria() {
                return (this.classificationMode == 'classification' || this.classificationMode == 'reclassification' || this.classificationMode == 'referenceHistory') && this.isPotentialCase;
            },
            displayCountryOfOccurrence() {
                return (this.classificationMode == 'classification' || this.classificationMode == 'reclassification' || this.classificationMode == 'referenceHistory') && this.isPotentialCase;
            },
            displayPSURRelevanceAbstract() {
                return this.classificationMode == 'referenceHistory';
            },
            displayPvSafetyDatabaseId() {
                return this.classificationMode == 'referenceHistory';
            },
            displayAiDosageFormSuggestion() {
                return this.isAiEnabled && this.referenceClassification.isActive && !this.aiFailedOrAwaitingResponse && !this.isSignedReferenceState;
            },
            canAcceptDosageFormSuggestion() {
                return this.aiSuggestedClassification.dosageForm;
            },
            aiDosageFormDecisionPending() {
                return this.aiSuggestedClassification.dosageFormDecision == aiDecisionTypes.none
            },
            aiDosageFormDecisionAccepted() {
                return this.aiSuggestedClassification.dosageFormDecision == aiDecisionTypes.accepted;
            },
            aiDosageFormDecisionRejected() {
                return this.aiSuggestedClassification.dosageFormDecision == aiDecisionTypes.rejected;
            },
            displayDosageForm() {
                return !this.displayAiDosageFormSuggestion || (this.displayAiDosageFormSuggestion && this.aiFailedOrAwaitingResponse);
            },
            disableDosageForm() {
                return !this.referenceClassification.isActive;
            }
        },
        created() {

            if (this.classificationMode == 'readonly') {
                if (this.referenceClassification.classificationCategoryId == 0) {
                    this.classificationCaseName = 'N/A';
                } else {
                    this.classificationCaseName = this.classificationCategories[this.referenceClassification.classificationCategoryId - 1].name;
                }
            }
            // To save countryOfOccurrence for only potential case when master accessor classifies the referenceClassification.
            this.referenceClassification.countryOfOccurrence = this.isPotentialCase ? this.countryOfOccurrence : null;

            this.potentialCaseAdditionalFieldSelections.selectedIds = [];
        },
        watch: {
            "referenceClassification.classificationCategoryId"(newValue) {
                this.referenceClassification.countryOfOccurrence = newValue == 1 ? this.countryOfOccurrence : null;
                this.referenceClassification.buttonDisabled = !this.isValidClassification;

                if (this.displayAiCategorySuggestion && this.aiCategoryDecisionRejected) {
                    if (newValue == this.aiSuggestedClassification.categoryId) {
                        this.aiSuggestedClassification.categoryDecision = aiDecisionTypes.accepted;
                    }
                }
            },
            "referenceClassification.minimalCriteria"(newValue) {
                this.referenceClassification.buttonDisabled = !this.isValidClassification;
            },
            "referenceClassification.countryOfOccurrence"(newValue) {
                this.referenceClassification.buttonDisabled = !this.isValidClassification;
            },
            "referenceClassification.psurRelevanceAbstract"(newValue) {
                this.referenceClassification.buttonDisabled = !this.isValidClassification;
            },
            "referenceClassification.dosageForm"(newValue) {
                this.referenceClassification.buttonDisabled = !this.isValidClassification;

                if (this.displayAiDosageFormSuggestion && this.aiDosageFormDecisionRejected) {
                    if (newValue == this.aiSuggestedClassification.dosageForm) {
                        this.aiSuggestedClassification.dosageFormDecision = aiDecisionTypes.accepted;
                    }
                }
            },
            countryOfOccurrenceVerified: {
                handler(newValue) {
                    this.referenceClassification.buttonDisabled = !this.isValidClassification;
                },
                immediate: true
            },
            "referenceClassification.isActive"(newValue) {
                if (!newValue) {
                    if (this.isAiEnabled) {
                        this.undoCategoryDecision();
                        this.undoDosageFormDecision();
                    } else {
                        this.referenceClassification.classificationCategoryId = this.previousCategoryId;
                        this.referenceClassification.dosageForm = this.previousDosageForm;
                    }
                }
            },
        }
    });
</script>