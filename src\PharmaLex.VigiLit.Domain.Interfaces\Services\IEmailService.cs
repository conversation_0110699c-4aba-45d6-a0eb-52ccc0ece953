﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public interface IEmailService
{
    Task SendDailyReferenceClassificationEmails(EmailTriggerType emailTriggerType);
    Task SendInvitationEmail(InvitationEmailModel email);
    Dictionary<int, Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>>> GetEmailParts(Dictionary<EmailReason, List<DailyClassificationEmailSendGridTemplateModelRow>> emailSections);
}
