﻿using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Services;

public interface IImportService
{
    Task<IEnumerable<ImportModel>> GetImportLog();
    Task<IEnumerable<ImportContractModel>> GetImportContractsLog(int importId);
    Task<IEnumerable<AdHocImportContractModel>> GetAdHocImportContractsList(int adHocImportId);
}
