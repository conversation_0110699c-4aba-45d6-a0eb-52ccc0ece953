using AutoMapper;
using PharmaLex.Helpers;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class EmailMessageRelevantEventMappingProfile : Profile
{
    public EmailMessageRelevantEventMappingProfile()
    {
        CreateMap<EmailMessageRelevantEvent, EmailMessageClassificationModel>()
            .ForMember(d => d.Id, s => s.MapFrom(x => x.EmailRelevantEvent.CompanyInterest.ReferenceClassificationId))
            .ForMember(d => d.SourceId, s => s.MapFrom(x => x.EmailRelevantEvent.CompanyInterest.Reference.SourceId))
            .ForMember(d => d.DOI, s => s.MapFrom(x => x.EmailRelevantEvent.CompanyInterest.Reference.Doi))
            .ForMember(d => d.ReferenceClassificationId, s => s.MapFrom(x => x.EmailRelevantEvent.CompanyInterest.ReferenceClassificationId))
            .ForMember(d => d.SubstanceName, s => s.MapFrom(x => x.EmailRelevantEvent.CompanyInterest.ReferenceClassification.Substance.Name))
            .ForMember(d => d.Title, s => s.MapFrom(x => x.EmailRelevantEvent.CompanyInterest.Reference.Title))
            .ForMember(d => d.EmailReason, s => s.MapFrom(x => x.EmailRelevantEvent.EmailReason.GetDescription()))
            .ForMember(d => d.ClassificationCategoryName, s => s.MapFrom(x => x.EmailRelevantEvent.ClassificationCategory.Name));
    }
}
