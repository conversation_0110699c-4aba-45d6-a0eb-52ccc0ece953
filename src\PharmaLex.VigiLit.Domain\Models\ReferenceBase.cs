﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.ReferenceManagement;
using System.ComponentModel.DataAnnotations;

namespace PharmaLex.VigiLit.Domain.Models;
public abstract class ReferenceBase : VigiLitEntityBase, IReference, IHasStorageLocation
{
    public string Abstract { get; set; }
    public string AffiliationTextFirstAuthor { get; set; }
    public string Authors { get; set; }
    public string CountryOfOccurrence { get; set; }
    public DateTime DateRevised { get; set; }
    public string Doi { get; set; }
    public string FullPagination { get; set; }
    public string Issn { get; set; }
    public string Issue { get; set; }
    public string Language { get; set; }
    public int SourceSystem { get; set; }
    [MaxLength(450)]
    public string SourceId { get; set; }
    public string PublicationType { get; set; }
    public ushort? PublicationYear { get; set; }
    public string Title { get; set; }
    public string Volume { get; set; }
    public string VolumeAbbreviation { get; set; }
    public string Keywords { get; set; }
    public string MeshHeadings { get; set; }
    public string JournalTitle { get; set; }
    public string DocumentLocation { get; set; }

    public void TakeUpdates(IReference source)
    {
        Abstract = source.Abstract;
        AffiliationTextFirstAuthor = source.AffiliationTextFirstAuthor;
        Authors = source.Authors;
        CountryOfOccurrence = source.CountryOfOccurrence;
        DateRevised = source.DateRevised;
        Doi = source.Doi;
        FullPagination = source.FullPagination;
        Issn = source.Issn;
        Issue = source.Issue;
        Keywords = source.Keywords;
        Language = source.Language;
        MeshHeadings = source.MeshHeadings;
        SourceId = source.SourceId;
        SourceSystem = source.SourceSystem;
        PublicationType = source.PublicationType;
        PublicationYear = source.PublicationYear;
        Title = source.Title;
        Volume = source.Volume;
        VolumeAbbreviation = source.VolumeAbbreviation;
        JournalTitle = source.JournalTitle;
    }
}
