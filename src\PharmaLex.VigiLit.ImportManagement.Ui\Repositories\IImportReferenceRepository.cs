﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
internal interface IImportReferenceRepository : ITrackingRepository<ImportReference>
{
    /// <summary>
    /// Gets all import references.
    /// </summary>
    /// <returns></returns>
    Task<IEnumerable<ImportReference>> GetAllAsync();
    /// <summary>
    /// Get import reference by ID. 
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    Task<ImportReference?> GetById(int id);
}
