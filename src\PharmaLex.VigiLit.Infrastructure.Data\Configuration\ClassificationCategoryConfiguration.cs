using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ClassificationCategoryConfiguration : EntityBaseMap<ClassificationCategory>
{
    public override void Configure(EntityTypeBuilder<ClassificationCategory> builder)
    {
        base.Configure(builder);

        builder.HasIndex(p => new { p.Id, p.PushServiceRelevant });

        builder.Property(e => e.Name)
            .IsRequired(false)
            .HasMaxLength(50);

        builder.ToTable("ClassificationCategories", t =>
        {
            t.IsTemporal();
        });
    }
}