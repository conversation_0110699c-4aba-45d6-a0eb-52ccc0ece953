﻿using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using PharmaLex.VigiLit.AccessControl;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

namespace PharmaLex.VigiLit.Case;
internal class CaseAccessEvaluator : PermissionEvaluator<CaseAccessContext>
{
    private readonly IAuthorizationService _authorizationService;
    private readonly IUserRepository _userRepository;

    public CaseAccessEvaluator(IAuthorizationService authorizationService, IUserRepository userRepository)
    {
        _authorizationService = authorizationService;
        _userRepository = userRepository;
    }

    private static bool IsPending(CaseModel caseModel)
    {
        return caseModel.Status == CaseStatus.Pending;
    }

    private async Task<bool> IsNotCaseManagement(ClaimsPrincipal user)
    {
        var authorizationResult = await _authorizationService.AuthorizeAsync(user, Policies.CaseManagement);
        return !authorizationResult.Succeeded;
    }

    public override async Task<bool> HasPermissions(CaseAccessContext context)
    {
        if (IsPending(context.CaseModel) && await IsNotCaseManagement(context.User))
        {
            throw new UnauthorizedAccessException();
        }

        var authorizationResult = await _authorizationService.AuthorizeAsync(context.User, Policies.InternalUser);
        if (authorizationResult.Succeeded)
        {
            return true;
        }

        authorizationResult = await _authorizationService.AuthorizeAsync(context.User, Policies.CaseExternal);
        if (!authorizationResult.Succeeded)
        {
            throw new UnauthorizedAccessException();
        }

        var userId = GetUserId(context.User);
        var user = await _userRepository.GetForSecurity(userId);
        if (user?.CompanyUser == null || !user.CompanyUser.Active)
        {
            throw new UnauthorizedAccessException();
        }

        var isCaseForUserCompany = context.CaseModel.CaseCompanies.Select(company => company.CompanyId).Contains(user.CompanyUser.CompanyId);
        if (isCaseForUserCompany)
        {
            return true;
        }

        throw new UnauthorizedAccessException();
    }
}
