﻿using AutoMapper;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

namespace PharmaLex.VigiLit.Application.Services;

public class CaseFileDocumentTypeService : ICaseFileDocumentTypeService
{
    private readonly ICaseFileDocumentTypeRepository _caseFileDocumentTypeRepository;
    private readonly IMapper _mapper;

    public CaseFileDocumentTypeService(
        ICaseFileDocumentTypeRepository caseFileDocumentTypeRepository,
        IMapper mapper)
    {
        _caseFileDocumentTypeRepository = caseFileDocumentTypeRepository;
        _mapper = mapper;
    }

    public async Task<IEnumerable<CaseFileDocumentTypeModel>> GetAllAsync()
    {
        var cases = await _caseFileDocumentTypeRepository.GetAllAsync();
        return _mapper.Map<IEnumerable<CaseFileDocumentTypeModel>>(cases);
    }
}