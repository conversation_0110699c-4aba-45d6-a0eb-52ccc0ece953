﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddFailedImportFileTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "FailedImportFiles",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Abstract = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AffiliationTextFirstAuthor = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Authors = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    CountryOfOccurrence = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateRevised = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Doi = table.Column<string>(type: "nvarchar(250)", maxLength: 250, nullable: true),
                    FullPagination = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Issn = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Issue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Language = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SourceSystem = table.Column<int>(type: "int", nullable: false),
                    SourceId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PublicationType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PublicationYear = table.Column<int>(type: "int", nullable: true),
                    Title = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Volume = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VolumeAbbreviation = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Keywords = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MeshHeadings = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    JournalTitle = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AbstractConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    AffiliationTextFirstAuthorConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    AuthorsConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    CountryOfOccurrenceConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    DateRevisedConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    DoiConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    FullPaginationConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    IssnConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    IssueConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    LanguageConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    SourceSystemConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    SourceIdConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    PublicationTypeConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    PublicationYearConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    TitleConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    VolumeConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    VolumeAbbreviationConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    KeywordsConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    MeshHeadingsConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    JournalTitleConfidence = table.Column<byte>(type: "tinyint", nullable: false),
                    DocumentLocation = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: false),
                    CorrelationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    CreatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false),
                    LastUpdatedDate = table.Column<DateTime>(type: "datetime", nullable: false),
                    LastUpdatedBy = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FailedImportFiles", x => x.Id);
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "FailedImportFiles");
        }
    }
}
