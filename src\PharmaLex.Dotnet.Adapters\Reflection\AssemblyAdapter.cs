using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

[System.Diagnostics.DebuggerDisplay("{FullName}")]
public class AssemblyAdapter : IAssembly
{
    private readonly Assembly _assembly;

    public AssemblyAdapter(Assembly assembly)
    {
        _assembly = assembly;
    }

    public string FullName => _assembly.FullName;

    public bool GlobalAssemblyCache => _assembly.GlobalAssemblyCache;

    public string Location => _assembly.Location;

    public Stream GetManifestResourceStream(string name)
    {
        return _assembly.GetManifestResourceStream(name);
    }

    public IAssemblyName Name => new AssemblyNameAdapter(_assembly.GetName());

    public ICollection<IType> Types
    {
        get
        {
            Type[] types;

            try
            {
                types = _assembly.GetTypes();
            }
            catch (ReflectionTypeLoadException ex)
            {
                types = ex.Types.Where(each => each != null).ToArray();
            }

            IType[] typeList = new IType[types.Length];

            for (var i = 0; i < types.Length; i++)
                typeList[i] = new TypeAdapter(types[i]);

            return typeList;
        }
    }

    public object CreateInstance(string typeName, bool ignoreCase)
    {
        return _assembly.CreateInstance(typeName, ignoreCase);
    }

    public object[] GetCustomAttributes(bool inherit)
    {
        return _assembly.GetCustomAttributes(inherit);
    }

    public object[] GetCustomAttributes(Type attributeType, bool inherit)
    {
        return _assembly.GetCustomAttributes(attributeType, inherit);
    }

    public bool IsDefined(Type attributeType, bool inherit)
    {
        return _assembly.IsDefined(attributeType, inherit);
    }

    public ICollection<IType> GetTypes()
    {
        return Types;
    }
}