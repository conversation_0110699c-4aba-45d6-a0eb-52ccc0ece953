-- =============================================
-- Author:      <PERSON>
-- Create date: 2023-08-22
-- Description: Creates substances.
-- =============================================

DROP PROCEDURE IF EXISTS [CreateSubstance]
GO

CREATE PROCEDURE [CreateSubstance] @ClaimType nvarchar(450), @Name nvarchar(250)
AS
	BEGIN TRAN

		INSERT INTO [Substances] ([Name], [Type], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy])
			VALUES (@Name, @ClaimType, GETUTCDATE(), 'script', GETUTCDATE(), 'script') 
			
	COMMIT TRAN
GO

EXEC [CreateSubstance] @ClaimType = 'ch', @Name = '18F-fluorodeoxyglucose';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = '1-Propanol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = '2-Propanol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = '68Ga‐PSMA‐11';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = '99Tc-Mebrofenin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Abacavir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Abiraterone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Aceclofenac';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Acetylcysteine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Acetylsalicylic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Acyclovir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Adrenaline';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Aesculus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Aether sulfuricus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Agnus castus#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Agomelatine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Albendazol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Alendronic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Alfatradiol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Alfuzosin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Allantoin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Allopurinol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Almasilate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Alprazolam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Alumina';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Aluminium chloride hexahydrate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Aluminium diacetate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Aluminium hydroxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amantadine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ambrisentan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ambroxol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amifampridine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amiloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Aminocaproic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amisulpride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amitriptyline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amlodipine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ammonia';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amoxicillin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amoxicillin_Clavulanic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amyl metacresol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Amylase';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Anagrelide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Anastrozole';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Angelica';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Anti-human T-lymphocyte immunoglobulin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Antimuscarinics';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Apis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Apixaban';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Apraclonidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Apremilast';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Aprepitant';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Arginine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Aripiprazole';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Arnica';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Artemisia';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Artichoke';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Atenolol';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Atomoxetine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Atorvastatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Atropine Sulphate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Avena';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Azacitidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Azithromycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bacille Calmette-Guerin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Baclofen';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Barnidipine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Bearberry';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bemetizide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bendroflumethiazide';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Benedictine herb';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Benperidol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Benserazide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Benznidazol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Benzydamine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Benzyl benzoate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Benzylnicotinate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Benzylpenicillin';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Berberis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Betahistine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Betamethasone';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Betula';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bevacizumab';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bezafibrate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bicalutamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bifonazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bilastine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bimatoprost';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Biotin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Biperiden';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bisacodyl';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bisoprolol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bitter fennel oil#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bortezomib';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Botulinum A Toxin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Botulinum_death_life threatening';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Botulinum_Other trade name';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Brimonidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Brinzolamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Brivudine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bromelain';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bromhexine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bromopride';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Bryonia';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Budesonide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bumetanide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bupivacaine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Buprenorphin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Bupropion';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Buspirone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Butoconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cabazitaxel';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Cactus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Caffeine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Calcipotriol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Calcium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Calcium acetate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Calcium carbonate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Calcium carbonicum hahnemanni';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Calcium chloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Calcium folinate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Calcium hydroxylapatite';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Calcium pantothenate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Calcium sulfuricum';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Camellia sinensis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Camphora#';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Camphora#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Candesartan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cantharis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Capsaicin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Captopril';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Caraway oil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Carbamazepine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Carbidopa';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Carbimazole';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Carbo';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Carbocisteine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Carbon Dioxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Carduus marianus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Carmellose';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Carvedilol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cefaclor';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cefadroxil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cefixime';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cefotiam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ceftriaxone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cefuroxime axetil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Celecoxib';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cellfina';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cetirizine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cetraria islandica';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cetylpyridinium chloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chamomilla#';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Chelidonium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chlorhexidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chlormadinone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chloroprocaine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chloroxylenol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chlorphenamine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chlorprothixene';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chlorthalidone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Chlorzoxazone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cifoban';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cilostazol';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Cimicifugae';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cinacalcet';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cinchocaine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cineole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cinnabaris';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Cinnamomum';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Cinnamon#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cinnarizine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ciprofloxacine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Citalopram';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Citicoline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Citric acid';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Citronella#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clarithromycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clavulanic Acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clemizol-penicillin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clindamycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clobetasole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clofarabine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clomipramine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clonazepam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clopidogrel';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clotiazepam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clotrimazole';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Clove';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Clozapine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Cnicus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Codeine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Colestyramine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Colocynthis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Coriander';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Coriandrum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'COVID-19 vaccine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Crataegus';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Cucurbita';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Cynara';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cyproheptadine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cyproterone acetate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Cystine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dabigatran';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Daptomycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Darifenacine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Darunavir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Deferasirox';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Denosumab';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dequalinium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Desloratadine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Desogestrel';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Devil''s claw';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dexamethasone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dexmedetomidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dexpanthenol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dextromethorphan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Diacerein';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Diamorphine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Diazepam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dichlorbenzyl alcohol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Diclofenac';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Didecyldimethylammonium chloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dienogest';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Dill';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Diltiazem';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dimenhydrinate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dimepranol acedoben';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dimethyl fumarate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Diphenhydramine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Diprophylline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Docosan-1-ol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Donepezil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dorzolamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Doxepin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Doxorubicin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Doxycycline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Doxylamine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dronedarone';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Drosera';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Drospirenone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Duloxetine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Dutasteride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ebastine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Echinacea';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Econazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Efavirenz';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Eletriptan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Enalapril';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Entacapone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Entecavir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ephedrine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Epirubicin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Eplerenone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Erdosteine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Eribulin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Erlotinib';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Erythromycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Eslicarbazepine acetate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Esomeprazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Estradiol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Estriol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Eszopiclone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ethanol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ethinyloestradiol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ethosuximide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Etonogestrel';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Etoricoxib';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Eucalyptus#';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Eucalyptus#';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Eugenol';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Eupatorium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Exemestane';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Exparel';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Extractum Cepae';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ezetimibe';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Factor IX';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Factor VIII';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Famotidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fampridine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Febuxostat';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fefolato';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fennel';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fentanyl';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ferrous gluconate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ferucarbotran';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fesoterodine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Finasteride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fingolimod hydrochloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Flecainide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Flucloxacillin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fluconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fludrocortisone acetate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Flunarizine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fluocinolone acetonide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fluocortolone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fluoxetine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Flupentixol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fluphenazine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fluticasone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fluvastatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fluvoxamine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Folic Acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Formoterol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Fosfomycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_ Gemcitabine 1';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Acyclovir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Adalimumab';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Addamel N/Addaven';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Adenosine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Amikacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Aminomix';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Aminosteril';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Aminoven';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Amoxycillin_Clavulanic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ampicillin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Anastrozole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Anidulafungin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Bendamustine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Bicalutamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Bleomycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Bortezomib';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Carboplatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Caspofungin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Cefepime';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ceftazidime';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ceftriaxone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Cefuroxime';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Chorionic gonadotropin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ciprofloxacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Cisatracurium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Cisplatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Cladribine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Clindamycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Cytarabine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Dexmedetomidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Dipeptiven';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Diphenhydramine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Docetaxel';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Dopamine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Doxorubicin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Entecavir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Epirubicin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ertapenem';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Etoposide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Fentanyl';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Flucloxacillin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Fluconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Fludarabine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Flumazenil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Folinic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Furosemide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ganciclovir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Geloplasma/Plasmion';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Gemcitabine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Glamin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Glucose';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Glycophos';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Granisetron';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Haloperidol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Heparin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Hydroxyethylstarch_Voluven_HAES';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ibuprofen';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Idarubicin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ifosfamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Imatinib';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Imipenem/Cilastatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Intralipid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ionolyte';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Irinotecan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Kabiven';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ketorolac';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ketosteril';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ketosteril_Ketarenil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Lenalidomide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Letrozole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Levobupivacaine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Levofloxacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Lidocaine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Linezolid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Lipovenoes';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Mannitol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Meropenem';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Mesna';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Methotrexate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Metronidazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Midazolam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Mitoxantrone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Morphine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Moxifloxacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Nephrotect';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Norepinephrine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Octreotide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Oleovit';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Omegaven';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ondansetron';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Oxaliplatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Oxytocin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Paclitaxel';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Palonosetron';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Pamidronate disodium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Paracetamol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Pediaven_Kidiaven';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Peditrace';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Pemetrexed';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Piperacillin_Tazobactam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Posaconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Potassium Chloride_Sodium Chloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Propofol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Remifentanil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ringer Lactate Solution';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ringer solution';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Rocuronium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Ropivacaine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_SmofKabiven';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_SMOFlipid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Sodium acetate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Sodium Chloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Soluvit';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Structolipid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Temozolomide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Topotecan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Vamin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Vaminolact';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Vancomycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Vinorelbine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Vitalipid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Voluven';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Voriconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Water for injections';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'FREK_Zoledronic Acid';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Fulvestrant';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Furosemide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Gabapentin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Gadoteric acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Galantamine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Gallium68-Germanium68';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Gefitinib';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Gelsemium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Gemcitabine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Gentamicin';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Gentiana';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Gestodene';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Glimepiride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Glycerol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'glyceryl trinitrate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Glycopyrronium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Guaifenesin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Haemofiltration';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Haloperidol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Healing Clay';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Heparin local';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hexamidine isethionate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Honey';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Human albumine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hyaluronic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hyaluronic acid dermal';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Hydrastis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hydrochlorothiazide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hydrocortisone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hydromorphone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hydroxychloroquine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hydroxyzine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Hypericum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Hypromellose';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ibandronic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ibuprofen';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Icatibant';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Idarubicin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ignatia';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Imatinib';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Imidapril';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Imipramine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Immunoglobulin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Inosine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Iodoform';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Iohexol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ipecacuanha';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Irbesartan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Iron';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Isoconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Itraconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ivabradine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ivermectin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Journee';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Jylamvo';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Kalium bichromicum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Keratin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ketoconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ketoprofen';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ketorolac Trometamol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lachesis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lacosamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lactic Acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lactobacillus acidophilus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lactose';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lamivudine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lamotrigine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lanolin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lansoprazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Latanoprost';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lavandula#';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Ledum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Leflunomide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lenalidomide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lercanidipine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Letrozole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levetiracetam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levodopa';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levofloxacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levomefolate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Levomenthol#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levomepromazine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levomethadone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levonorgestrel';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levosimendan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levosulpiride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Levothyroxine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lidocaine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lipase';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lipoic Acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Liquid nitrogen';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lisdexamfetamine dimesylate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lisinopril';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Lithium Carbonicum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lithium sulphate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Lobelia';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lonadial';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Loperamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Loratadine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lorazepam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lornoxicam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Losartan';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Luffa';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lurasidone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lutetium177';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Lycopodium';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Lycopus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Lysine acetylsalicylate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Mace#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Macimorelin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Macrogol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Macrogolglycerol hydroxystearate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Magnesium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Magnesium carbonate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Magnesium hydroxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Magnesium oxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Magnesiumtrisilicat';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Malic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Maprotiline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Mebeverine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Mecillinam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'medical air';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Medrogestone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Melatonin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Melissa#';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Melissae';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Meloxicam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Melperone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Memantine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Mentha#';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Menthae';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Meropenem';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Metamizole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Metenamin-hippurat';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Metformin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Methacholine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Methadone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Methionine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Methocarbamol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Methotrexate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Methoxyflurane';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Methylphenidate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Methylprednisolone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Metoclopramide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Metoprolol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Metronidazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Mianserin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Miconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Micro day';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Micronized flavonoid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Midazolam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Migretil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Milnacipran';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Minoxidil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Miostenil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Mirtazapine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Misoprostol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Mitomycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Moclobemide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Modafinil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Mometasone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Montelukast';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Morphine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Mountain pine oil#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Moxifloxacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Mycophenolate mofetil';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Myristica';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Naloxone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Naltrexone';
EXEC [CreateSubstance] @ClaimType = 'Type', @Name = 'Name';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Naphazoline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Naproxen';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Naratriptan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Natifar';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Natrium sulfuricum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nebivolol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Neocutis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Neomycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Neostigmine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nepafenac';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nevirapine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nicergoline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nicotinamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nicotine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nimodipine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nitrazepam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nitrendipine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nitric oxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nitrofurantoin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nitrous oxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nonivamid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Norfloxacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Norgestimate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nortriptyline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nux vomica';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Nystatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Octenidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Olanzapine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Omega-3-acid ethyl esters 90';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Omega-3-Fatty Acid Ethyl Esters';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Omeprazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ondansetron';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Opicapone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Opipramol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Orlistat';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ornithine aspartate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Orthosiphon';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Otilonium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxaliplatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxazepam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxcarbazepine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxiconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxybutynin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxycodone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxygen';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxygen_1';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxygen_2';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxytetracycline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Oxytocin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pafolacianin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Paliperidone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Palivizumab';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'p-Aminobenzoic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pantoprazole';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Papaver';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Paracetamol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Paraffin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Paricalcitol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Paroxetine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Passiflora';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pegfilgrastim';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pemetrexed';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Peppermint oil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pepsin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Perazine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Perfluorodecalin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Peritoneal dialysis';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Perphenazine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Phenazone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Phenobarbital';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Phenoxybenzamine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Phenoxymethylpenicillin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Phenyl salicylate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Phenylephrine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Phenylephrine hydrochloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Phlogenzym';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pilocarpine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Pine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pine oil#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pioglitazone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pipamperone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Piracetam';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pirfenidone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pitolisant';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pivmecillinam';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Plantago';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Plantago lanceolata#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Podophyllotoxin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Polidocanol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Polipres';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Polymyxin B Sulfate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Polystyrene sulfonate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Potassium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Potassium bicarbonate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Potassium chloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Potassium iodide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Povidone Iodine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pramipexole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Prasugrel';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pravastatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Prednisolone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pregabalin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Probenecid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Procaine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Progesterone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Promethazine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Propiverine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Propranolol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Propylene glycol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Propylparaben';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Prucalopride succinate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Pulsatilla';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Pyridoxine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Quetiapine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Quinine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rabeprazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Racecadotril';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ramipril';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ranolazine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rasagiline';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Rhus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rifaximin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Riluzol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Risedronic Acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Risperidone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rituximab';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rivaroxaban';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rivastigmine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rizatriptan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ropinirole';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Rosemary';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rosuvastatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rotigotine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Roxithromycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Rutosid-Trihydrat';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sabal serrulatum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Saccharomyces cerevisiae';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Salbutamol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Salicylic Acid';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Salvia#';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Salviae';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Sarsaparilla';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Saw palmetto';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Scilla';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Selegiline';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Selenium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Selenium chemically defined';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Senna';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sertraline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sevelamer';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sildenafil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Silica';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Silicea';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Silodosin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Silymarin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Simeticone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Simvastatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sitagliptin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium bicarbonate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium chloride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium citrate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium clodronate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium fluoride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium hypochlorite';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium oxybate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium Picosulfate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sodium sulfate';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Solidago';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Solifenacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Soya';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Spigelia';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Spironolactone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Spongia';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Spruce';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Stannous agent';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Stannous fluoride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Strontium ranelate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sucralfate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sufentanil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sulfafurazole';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Sulfur';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sulpiride';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sultiame';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sumatriptan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Sunitinib';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Suxamethonium';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Symphytum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Syzygium#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tadalafil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tamoxifen';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tamsulosin';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Tannin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tapentadol';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Taraxacum';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tartarus stibiatus';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Telmisartan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tenofovir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Terazosin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Terbinafine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Teriflunomide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Teriparatide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Testosterone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Testosterone undecanoate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tetrabenazine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Theophylline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Thiamazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Thiocolchicoside';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Thioridazine';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Thuja';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Thymi';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Thymol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Thymus#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tianeptine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tibolone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ticagrelor';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ticlopidine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tigecycline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Timolol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tinidazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tioconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tiotropium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Albendazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Aluminium Hydroxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Amlodipine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Artemether';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Atorvastatin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Azithromycin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Ceftriaxone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Cetirizine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Ciprofloxacin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Fluconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Lansoprazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Losartan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Lumefantrine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Magnesium Hydroxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Metronidazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Nifedipine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Omeprazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Paracetamol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Sildenafil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Simethicone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'TLG-Sodium carboxymethyl cellulose';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tobramycin';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Tocopherol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tolcapone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tolperisone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tolterodine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Topiramate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tosylchloramide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tramadol';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tranylcypromine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Travoprost';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Trazodone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Triamcinolone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Triamterene';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Triflusal';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Trimipramine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Triticum repens';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Trospium';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Turpentine oil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Tyrothricin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ulipristal';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ulthera';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Urapidil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Urea';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ursodeoxycholic Acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Valacyclovir';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Valeriana';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Valganciclovir';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Valproic Acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Valsartan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vardenafil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Varenicline';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Venlafaxine';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Verapamil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vicombil';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vildagliptin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vinorelbin';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Viscum album#';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vitamin A';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vitamin B1';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vitamin B12';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vitamin B2';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vitamin B6';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vitamin C';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vitamin D';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Vitamin E';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Voriconazole';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Wobe mugos';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Wobenzym';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Wobenzym dragee';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Xylometazoline';
EXEC [CreateSubstance] @ClaimType = 'ph', @Name = 'Yarrow';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Yttrium90';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zilretta';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zinc';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zinc oxide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zinc sulphate';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Ziprasidone';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zoledronic acid';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zolmitriptan';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zolpidem';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zonisamide';
EXEC [CreateSubstance] @ClaimType = 'ch', @Name = 'Zopiclone';
GO

DROP PROCEDURE IF EXISTS [CreateSubstance]
GO
