﻿using AutoMapper;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Entities;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Models;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.ImportManagement.Ui.Services;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Generics;

#pragma warning disable S125, S2436
internal class GenericCardService<TRepository, TCardEntity, TViewModel> : IGenericCardService<TCardEntity, TViewModel>
    where TRepository : IGenericCardRepository<TCardEntity>
    where TCardEntity : CoreEntityBase<int>
    where TViewModel  : IViewModel
{
    protected readonly TRepository _repository;
    protected readonly IImportReferenceRepository _importReferenceRepository;
    protected readonly IMapper _mapper;

    public GenericCardService(
        TRepository repository,
        IImportReferenceRepository importReferenceRepository,
        IMapper mapper)
    {
        _repository = repository;
        _importReferenceRepository = importReferenceRepository;
        _mapper = mapper;
    }

    public async Task Add(TViewModel model)
    {
        var cardEntity = _mapper.Map<TCardEntity>(model);
        _repository.Add(cardEntity);
        await _repository.SaveChangesAsync();
    }

    public async Task<TViewModel> Get(int id)
    {
        var cardEntity = await _repository.GetById(id);
        return _mapper.Map<TViewModel>(cardEntity);
    }

    public async Task Update(TViewModel model)
    {
        var cardEntity = await _repository.GetById(model.Id);
        if (cardEntity == null)
        {
            throw new InvalidOperationException($"Card with Id {model.Id} does not exist in the system.");
        }

        _mapper.Map(model, cardEntity);
        await _repository.SaveChangesAsync();
    }

    public async Task Abandon(int id)
    {
        try
        {
            if (id <= 0)
            {
                throw new ArgumentException("Invalid id. id must be greater than zero.", nameof(id));
            }

            var cardEntity = await _repository.GetById(id);
            if (cardEntity == null)
            {
                throw new InvalidOperationException($"Card with ID {id} does not exist in the system.");
            }

            _repository.Remove(cardEntity);
            await _repository.SaveChangesAsync();
        }
        catch (ArgumentException ex)
        {
            throw new ArgumentException($"Error: {ex.Message}", ex);
        }
        catch (InvalidOperationException ex)
        {
            throw new InvalidOperationException($"Operation Failed: {ex.Message}", ex);
        }
    }

    public async Task Enqueue(int id)
    {
        var cardEntity = await _repository.GetById(id);

        if (cardEntity == null)
        {
            throw new InvalidOperationException($"Card with Id {id} does not exist in the system.");
        }

        var importReference = _mapper.Map<ImportReference>(cardEntity);
        importReference.StatusType = ImportReferenceStatusType.Queued;
        _importReferenceRepository.Add(importReference);
        await _importReferenceRepository.SaveChangesAsync();

        _repository.Remove(cardEntity);
        await _repository.SaveChangesAsync();
    }

    public virtual Task<List<ImportDisplayCard>> GetCards()
    {
        return Task.FromResult(new List<ImportDisplayCard>());
    }

    public async Task<IEnumerable<TCardEntity>> GetAll()
    {
        var cardEntities = await _repository.GetAll();
        return cardEntities;
    }
}
#pragma warning restore S125, S2436