﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.FeatureManagement.Entities;
using PharmaLex.DataAccess;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;
public class FeatureFlagsConfiguration : EntityBaseMap<FeatureFlag>
{
    public override void Configure(EntityTypeBuilder<FeatureFlag> builder)
    {
        base.Configure(builder);

        builder.ToTable("FeatureFlags");

        builder.Property(ff => ff.Name).IsRequired().HasMaxLength(512);

        builder.HasIndex(ff => new { ff.Id });

        builder.HasIndex(ff => ff.Name).IsUnique();
    }
}
