﻿using PharmaLex.VigiLit.Core.Aggregator.Models;

namespace PharmaLex.VigiLit.Core.Aggregator.Providers;

/// <summary>
/// Interface for aggregators that search for references.
/// </summary>
/// <typeparam name="T">Type of search criteria</typeparam>
public interface IReferenceProvider<in T> where T : ISearchCriteria
{
    /// <summary>
    /// Performs a search on an aggregator with the specified search criteria.
    /// </summary>
    /// <param name="criteria">Search criteria for finding references</param>
    /// <param name="timeout">Timeout value before the search is aborted</param>
    /// <returns>Reference results which contains a collection of references</returns>
    Task<ReferenceResults> GetReferences(T criteria, DateTime timeout);
}