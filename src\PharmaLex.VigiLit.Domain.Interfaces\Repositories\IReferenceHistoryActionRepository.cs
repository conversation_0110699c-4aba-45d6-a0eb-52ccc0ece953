﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface IReferenceHistoryActionRepository : ITrackingRepository<ReferenceHistoryAction>
{
    Task<ReferenceHistoryAction?> GetByIdAsync(int id);
    Task<IEnumerable<ReferenceHistoryActionModel>> GetReferenceActions(int referenceClassificationId);
    void ClearChangeTracker();
}