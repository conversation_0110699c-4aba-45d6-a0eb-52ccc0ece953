using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface IReportingCompanyRepository : ITrackingRepository<Company>
{
    Task<Company?> GetByIdAsync(int id);
    Task<IEnumerable<CompanyItemModel>> GetCompanyItems(User user);
    Task<IEnumerable<Company>> GetActiveCompanies();
}