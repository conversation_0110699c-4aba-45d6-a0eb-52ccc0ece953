﻿using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Infrastructure.Emails;

public class EmailLogService : IEmailLogService
{
    private readonly IEmailRepository _emailRepository;
    private readonly IEmailMessageRepository _emailMessageRepository;
    private readonly IEmailMessageRelevantEventRepository _emailMessageRelevantEventRepository;

    public EmailLogService(
        IEmailRepository emailRepository,
        IEmailMessageRepository emailMessageRepository,
        IEmailMessageRelevantEventRepository emailMessageRelevantEventRepository)
    {
        _emailRepository = emailRepository;
        _emailMessageRepository = emailMessageRepository;
        _emailMessageRelevantEventRepository = emailMessageRelevantEventRepository;
    }

    public async Task<Email> CreateAndSaveEmailLog(EmailType emailType, EmailTriggerType emailTriggerType)
    {
        Email email = new()
        {
            EmailType = emailType,
            EmailTriggerType = emailTriggerType,
            StartDate = DateTime.UtcNow,
            EmailStatusType = EmailStatusType.Started
        };

        _emailRepository.Add(email);

        await _emailRepository.SaveChangesAsync();

        return email;
    }

    public async Task LogOutcome(Email? email, EmailStatusType status, int sentEmailsCount, int failedEmailsCount)
    {
        if (email != null)
        {
            email.EmailStatusType = status;
            email.EndDate = DateTime.UtcNow;
            email.SentEmailsCount = sentEmailsCount;
            email.FailedEmailsCount = failedEmailsCount;

            await _emailRepository.SaveChangesAsync();
        }
    }

    public async Task<IEnumerable<EmailModel>> GetEmailLog()
    {
        return await _emailRepository.GetEmailLog(Constants.EMAIL_LOG_INTERVAL_DAYS);
    }

    public async Task<IEnumerable<EmailMessageModel>> GetEmailMessagesLog(int emailId)
    {
        return await _emailMessageRepository.GetForEmailMessageLog(emailId);
    }

    public async Task<EmailMessage> GetEmailMessage(int emailMessageId)
    {
        return await _emailMessageRepository.GetEmailMessage(emailMessageId);
    }

    public async Task<IEnumerable<EmailMessageClassificationModel>> GetEmailMessageClassifications(int emailMessageId)
    {
        return await _emailMessageRelevantEventRepository.GetEmailMessageClassifications(emailMessageId);
    }
}