using System.Reflection;

namespace PharmaLex.Dotnet.Adapters.Reflection;

public interface IAssembly : ICustomAttributeProvider
{
    bool GlobalAssemblyCache { get; }
    string FullName { get; }
    string Location { get; }

    Stream GetManifestResourceStream(string name);
    IAssemblyName Name { get; }
    ICollection<IType> Types { get; }
    object CreateInstance(string typeName, bool ignoreCase);

    ICollection<IType> GetTypes();
}