﻿namespace PharmaLex.VigiLit.AccessControl;

internal class AccessControlService : IAccessControlService
{
    private readonly List<IPermissionEvaluator> _checks = new();

    public AccessControlService(IEnumerable<IPermissionEvaluator> evaluators)
    {
        foreach (var evaluator in evaluators)
        {
            _checks.Add(evaluator);
        }
    }

    public async Task<bool> HasPermission<T>(T context) where T : IAccessControlContext
    {
        foreach (var checker in _checks)
        {
            if (checker is IPermissionEvaluator<T> typedChecker)
            {
                return await typedChecker.HasPermissions(context);
            }
        }
        throw new NotSupportedException($"No checker found for context of type {typeof(T)}.");
    }
}
