﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface ICaseRepository : ITrackingRepository<Cases>
{
    Task<IEnumerable<Cases>> GetAllAsync(CaseStatus? caseStatus = null);
    Task<IEnumerable<Cases>> GetAllWithDetailsAsync(CaseSearchRequest? searchRequest = null);
    Task<Cases> GetByIdAsync(int id);
    Task<Cases> GetByIdWithDetailsAsync(int id);
}