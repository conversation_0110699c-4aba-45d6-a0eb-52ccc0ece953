﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using NLog;
using NLog.Web;
using PharmaLex.Core.Configuration;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.AiAnalysis.Entities;
using PharmaLex.VigiLit.DataAccessLayer;
using Phlex.Core.MessageBus.Extensions;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using LogLevel = Microsoft.Extensions.Logging.LogLevel;

namespace PharmaLex.VigiLit.AiAnalysis.Service;

[ExcludeFromCodeCoverage]
internal static class Program
{
    public static void Main(string[] args)
    {
        CreateHostBuilder(args).Build().Run();
    }

    public static IHostBuilder CreateHostBuilder(string[] args)
    {
        var logger = LogManager.Setup().LoadConfigurationFromAppSettings().GetCurrentClassLogger();
        try
        {
            var nrAgent = new Lazy<NewRelic.Api.Agent.IAgent>(NewRelic.Api.Agent.NewRelic.GetAgent());
            foreach (var (key, value) in nrAgent.Value.GetLinkingMetadata())
            {
                GlobalDiagnosticsContext.Set(key, value);
            }

            return Host.CreateDefaultBuilder(args)
                .AddConfiguration(Assembly.GetExecutingAssembly(), args)
                .ConfigureServices((hostContext, services) =>
                {
                    services.AddScoped<PlxDbContext, VigiLitDbContext>();
                    services.RegisterDbContext<VigiLitDbContext>();

                    services.RegisterAiAnalysisEntities();

                    services.AddScoped<IVigiLitUserContext, VigilitUserContext>();
                    services.AddScoped<IUserContext, VigilitUserContext>();

                    var configuration = hostContext.Configuration;
                    services.Configure<AiEndpointOptions>(configuration.GetSection(AiEndpointOptions.AiEndpointSettings));
                    services.AddMessageBus(configuration, configurator =>
                    {
                        configurator.AddCommandHandler<PreClassifyReferenceCommandHandler>(configuration);
                        configurator.UsingTransportFromConfiguration(configuration);
                    });
                    services.AddHttpClient();
                })
                .ConfigureLogging((hostingContext, logging) =>
                {
                    logging.ClearProviders();
                    logging.SetMinimumLevel(LogLevel.Trace);
                }).UseNLog();
        }
        catch (Exception ex)
        {
            //NLog: catch setup errors
            logger.Error(ex, "Stopped program because of exception");
        }
        finally
        {
            // Ensure to flush and stop internal timers/threads before application-exit (Avoid segmentation fault on Linux)
            LogManager.Shutdown();
        }
        return null!;
    }
}