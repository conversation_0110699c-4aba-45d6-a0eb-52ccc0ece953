﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ClaimConfiguration : EntityBaseMap<Claim>
{
    public override void Configure(EntityTypeBuilder<Claim> builder)
    {
        base.Configure(builder);

        builder.ToTable("Claims");

        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(1024);

        builder.Property(e => e.DisplayName)
            .IsRequired()
            .HasMaxLength(1024);

        builder.Property(e => e.ClaimType)
            .IsRequired(false)
            .HasMaxLength(50);

        builder.HasIndex(e => e.Name);
    }
}