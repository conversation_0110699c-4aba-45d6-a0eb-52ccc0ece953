﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class UpdateReportConfig : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"UPDATE [Reports] SET [AllCompanies] = 1 WHERE [Name] = 'Tracking Sheets';

                                   IF EXISTS (SELECT [Id] FROM [Companies] WHERE [Name] = 'APOGEPHA Arzneimittel GmbH')
                                   BEGIN
                                        INSERT INTO [CompanyReports] ( [CompanyId], [ReportId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                            VALUES( (SELECT [Id] FROM [Companies] WHERE [Name] = 'APOGEPHA Arzneimittel GmbH'),
                                                    (SELECT [Id] FROM [Reports] WHERE [Name] = 'Apogepha Client Report'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' )
	                               END;

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Tracking Sheets'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'SuperAdmin'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Tracking Sheets'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'InternalSupport'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Tracking Sheets'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'PreAssessor'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Tracking Sheets'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'MasterAssessor'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Tracking Sheets'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'ClientResearcher'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Tracking Sheets'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'PharmaLexResearcher'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Tracking Sheets'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'PharmaLexClientResearcher'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Tracking Sheets'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'CaseFileOperator'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Apogepha Client Report'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'SuperAdmin'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Apogepha Client Report'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'InternalSupport'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );

                                   INSERT INTO [ReportClaims] ( [ReportId], [ClaimId], [CreatedDate], [CreatedBy], [LastUpdatedDate], [LastUpdatedBy] ) 
                                        VALUES( (SELECT [Id] FROM [Reports] WHERE [Name] = 'Apogepha Client Report'),
                                                (SELECT [Id] FROM [Claims] WHERE [Name] = 'ClientResearcher'), GETUTCDATE(), 'Script', GETUTCDATE(), 'Script' );
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // VigiLit does not go back in versions
        }
    }
}
