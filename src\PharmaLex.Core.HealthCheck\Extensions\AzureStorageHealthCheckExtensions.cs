﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using PharmaLex.BlobStorage.Interfaces;
using PharmaLex.BlobStorage.Services;

namespace PharmaLex.Core.HealthCheck.Extensions;

public static class AzureStorageHealthCheckExtensions
{
    public static IHealthChecksBuilder AddHealthCheck<TClientProvider, TStorageOptions>(
        this IHealthChecksBuilder builder,
        string name,
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default)
        where TClientProvider : IBlobContainerClientProvider
        where TStorageOptions : IEnumerable<AzureStorageDocumentOptions>
    {
        return builder.Add(new HealthCheckRegistration(
            name,
            sp => new AzureStorageHealthCheck<TClientProvider, AzureStorageDocumentOptions>(sp.GetRequiredService<TStorageOptions>(),
                sp.GetRequiredService<TClientProvider>(),
                sp.GetRequiredService<ILoggerFactory>()),
            failureStatus,
            tags));

    }
}