using PharmaLex.DataAccess;
using System.Collections.Generic;
using System.Threading.Tasks;
{{#if Dependencies}}{{#each Dependencies}}using {{this}};
{{/each}}{{/if}}
namespace {{Namespace}}
{
    public interface I{{EntityName}}Repository: ITrackingRepository<{{EntityName}}>
    {
        Task<IEnumerable<{{EntityName}}>> GetAllAsync();
        Task<{{EntityName}}> GetByIdAsync(int id);        
    }
}
