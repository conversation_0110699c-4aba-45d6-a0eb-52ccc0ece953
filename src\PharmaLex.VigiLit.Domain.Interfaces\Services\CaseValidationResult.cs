﻿namespace PharmaLex.VigiLit.Domain.Interfaces.Services;

public class CaseValidationResult
{
    public readonly bool IsSuccess;
    public readonly string? Error;

    private CaseValidationResult(bool isSuccess, string? error)
    {
        IsSuccess = isSuccess;
        Error = error;
    }

    public static CaseValidationResult Success()
        => new(true, null);

    public static CaseValidationResult Fail(string error)
        => new(false, error);

}