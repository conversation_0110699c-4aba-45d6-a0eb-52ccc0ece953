﻿using Microsoft.Extensions.Options;
using System;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Infrastructure.Emails;

namespace PharmaLex.VigiLit.ImportApp.Common;

internal class WebsiteUriProvider : IWebsiteUriProvider
{
    private readonly EmailOptions _emailOptions;

    public WebsiteUriProvider(IOptions<EmailOptions> options)
    {
        _emailOptions = options.Value;
    }

    public Uri Provide()
        => _emailOptions.WebsiteUri;
}
