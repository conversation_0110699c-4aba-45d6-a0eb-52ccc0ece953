﻿using PharmaLex.VigiLit.DataAccessLayer.Base;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.UserManagement;

namespace PharmaLex.VigiLit.Domain.Models;

public class EmailSuppression : VigiLitEntityBase
{
    public int? UserId { get; set; }
    public EmailSuppressionType EmailSuppressionType { get; set; }
    public long Created { get; set; }
    public string Email { get; set; }
    public string Reason { get; set; }
    public string Status { get; set; }
    public User User { get; set; }
}
