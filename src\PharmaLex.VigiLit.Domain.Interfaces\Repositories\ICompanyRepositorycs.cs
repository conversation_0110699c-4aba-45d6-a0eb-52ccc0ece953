﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.UserManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;
public interface ICompanyRepository : ITrackingRepository<Company>
{
    Task<IEnumerable<Company>> GetAllAsync();
    Task<Company?> GetByIdAsync(int id);
    Task<Company?> GetByNameAsync(string name);
    Task<Company?> GetWithContractsByIdAsync(int id);
    Task<Company?> GetWithProjectsByIdAsync(int id);
    Task<Company?> GetWithUsersAndSuppressionsByIdAsync(int id);
    Task<List<Company>> GetAllCompaniesWithEmailSuppressions();
    Task<IEnumerable<CompanyEmailModel>> GetActiveCompaniesWithUsers();
    Task<IEnumerable<Company>> GetCompaniesForSubstanceAsync(int substanceId);
    Task<IEnumerable<CompanyItemModel>> GetCompanyItems(User user);
    Task<IEnumerable<CompanyModel>> GetActiveCompaniesWithActiveContractsForSubstance(int substanceId);
    Task<IEnumerable<Company>> GetActiveCompanies();
}