﻿using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class SendGridEmailSuppressionConfiguration : EntityBaseMap<EmailSuppression>
{
    public override void Configure(EntityTypeBuilder<EmailSuppression> builder)
    {
        base.Configure(builder);

        builder.HasIndex(s => s.EmailSuppressionType);
        builder.HasIndex(s => s.Email);

        builder.Property(s => s.EmailSuppressionType).IsRequired();
        builder.Property(s => s.Email).IsRequired();
        builder.Property(s => s.Reason).IsRequired();
        builder.Property(s => s.Status).HasMaxLength(100);
    }
}
