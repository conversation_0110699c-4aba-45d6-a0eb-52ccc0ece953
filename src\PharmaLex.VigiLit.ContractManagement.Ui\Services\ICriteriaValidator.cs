﻿namespace PharmaLex.VigiLit.ContractManagement.Ui.Services;

public interface ICriteriaValidator
{
    /// <summary>
    /// Tries to validate the criteria used for searching and matching.
    /// </summary>
    /// <param name="criteria">The criteria.</param>
    /// <param name="errorMessages">Collection of any error messages.</param>
    /// <returns>True if the criteria is formatted correctly, else returns False and a collection of error messages</returns>
    bool TryValidate(string criteria, out IReadOnlyCollection<SearchStringError> errorMessages);
}