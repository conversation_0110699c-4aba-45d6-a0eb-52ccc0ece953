using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class ImportConfiguration : EntityBaseMap<Import>
{
    public override void Configure(EntityTypeBuilder<Import> builder)
    {
        base.Configure(builder);

        builder.ToTable("Imports");

        builder.HasIndex(c => new { c.CreatedDate });
        builder.HasIndex(c => new { c.ImportType });
        builder.HasIndex(c => new { c.ImportStatusType });
        builder.HasIndex(c => new { c.ImportDashboardStatusType });
    }
}