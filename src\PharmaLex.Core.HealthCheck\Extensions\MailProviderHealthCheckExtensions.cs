﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;

namespace PharmaLex.Core.HealthCheck.Extensions;

public static class MailProviderHealthCheckExtensions
{
    public static IHealthChecksBuilder AddMailHealthCheck(
        this IHealthChecksBuilder builder,
        string name,
        string apiKey,
        HealthStatus? failureStatus = default,
        IEnumerable<string>? tags = default)
    {
        return builder.Add(new HealthCheckRegistration(
            name,
            sp => new MailProviderHealthCheck(apiKey, 
                sp.GetRequiredService<IHttpClientFactory>(), 
                sp.GetRequiredService<ILoggerFactory>()),
            failureStatus,
            tags));
    }
}