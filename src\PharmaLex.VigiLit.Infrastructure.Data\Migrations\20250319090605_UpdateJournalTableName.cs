﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class UpdateJournalTableName : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Journal_Countries_CountryId",
                table: "Journals");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Journal",
                table: "Journals");

            migrationBuilder.RenameIndex(
                name: "IX_Journal_CountryId",
                table: "Journals",
                newName: "IX_Journals_CountryId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Journals",
                table: "Journals",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Journals_Countries_CountryId",
                table: "Journals",
                column: "CountryId",
                principalTable: "Countries",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Journals_Countries_CountryId",
                table: "Journals");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Journals",
                table: "Journals");

            migrationBuilder.RenameIndex(
                name: "IX_Journals_CountryId",
                table: "Journals",
                newName: "IX_Journal_CountryId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Journal",
                table: "Journals",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Journal_Countries_CountryId",
                table: "Journals",
                column: "CountryId",
                principalTable: "Countries",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
