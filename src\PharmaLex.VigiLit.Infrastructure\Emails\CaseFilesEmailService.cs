﻿using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NewRelic.Api.Agent;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.Domain.Interfaces.Services;
using PharmaLex.VigiLit.Domain.Interfaces.Services.Document.Case;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Domain.Models.Document.Case;
using PharmaLex.VigiLit.Infrastructure.Emails.Interfaces;
using PharmaLex.VigiLit.Infrastructure.Utilities;
using PharmaLex.VigiLit.Ui.ViewModels.CaseManagement;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Infrastructure.Emails;

public class CaseFilesEmailService : ICaseFilesEmailService
{
    private readonly ICompanyService _companyService;
    private readonly ICaseService _caseService;
    private readonly ICaseDocumentService _caseDocumentService;
    private readonly IEmailSender _emailSender;
    private readonly IEmailLogService _emailLogService;
    private readonly IWebsiteUriProvider _websiteUriProvider;
    private readonly ILogger<CaseFilesEmailService> _logger;
    private readonly EmailOptions _emailOptions;

#pragma warning disable S107 // Methods should not have too many parameters
    public CaseFilesEmailService(
        ICompanyService companyService,
        ICaseService caseService,
        ICaseDocumentService caseDocumentService,
        IEmailSender emailSender,
        IEmailLogService emailLogService,
        IWebsiteUriProvider websiteUriProvider,
        ILogger<CaseFilesEmailService> logger,
        IOptions<EmailOptions> options)
#pragma warning restore S107 // Methods should not have too many parameters
    {
        _companyService = companyService;
        _caseService = caseService;
        _caseDocumentService = caseDocumentService;
        _emailSender = emailSender;
        _emailLogService = emailLogService;
        _websiteUriProvider = websiteUriProvider;
        _logger = logger;
        _emailOptions = options.Value;
    }

    [Trace]
    public async Task Send(EmailTriggerType emailTriggerType)
    {
        var email = await _emailLogService.CreateAndSaveEmailLog(EmailType.DailyCaseFilesEmail, emailTriggerType);

        var sentEmailsCount = 0;
        var failedEmailsCount = 0;

        try
        {
            var companies = (await _companyService.GetActiveCompaniesWithUsers()).ToList();
            _logger.LogDebug("Found {Count} active companies.", companies.Count);

            foreach (var company in companies)
            {
                var emailRecipients = GetEmailRecipients(company);
                if (emailRecipients.Count == 0)
                {
                    _logger.LogDebug("Company '{CompanyName}' does not have any recipients subscribed to the daily case files email.", company.Name);
                    continue;
                }

                var searchRequest = new CaseSearchRequest
                {
                    CaseStatus = CaseStatus.Pending,
                    CompanyId = company.Id
                };
                var cases = (await _caseService.SearchAsync(searchRequest)).ToList();

                var emailAttachments = await GetCaseEmailAttachments(cases);

                var companyWithContracts = await _companyService.GetCompanyWithContracts(company.Id);

                var emailSubstances = companyWithContracts.Contracts
                    .Where(c => c.IsActive)
                    .Select(contract => contract.SubstanceName)
                    .Distinct()
                    .Select(substanceName =>
                        new CaseEmailSubstanceModel(
                            substanceName,
                            cases
                                .Where(c => c.SubstanceName == substanceName)
                                .Select(c => new CaseEmailCaseModel(c.Id, c.PlxId, emailAttachments.Count(emailAttachment => emailAttachment.CaseId == c.Id)))
                                .ToList()
                            ))
                    .OrderBy(substance => substance.Name)
                    .ToList();

                _logger.LogDebug("Company '{CompanyName}' has {CountCases} pending cases and {CountUsers} recipients subscribed to the daily case files email.",
                    company.Name, cases.Count, emailRecipients.Count);

                var streamHelper = new StreamHelper();

                var zippedAttachments = streamHelper.GetZipOfBytesFromFileStreams(emailAttachments.ToDictionary(x => x.FileName, x => x.Stream));

                foreach (var emailRecipient in emailRecipients)
                {
                    _logger.LogInformation("Sending email for company '{CompanyName}' to recipient '{RecipientName}' at '{RecipientEmail}'.",
                        company.Name, emailRecipient.Name, emailRecipient.Email);

                    var caseEmail = GetCaseEmail(emailRecipient, cases, emailSubstances);

                    var response = await _emailSender.SendCaseEmail(caseEmail, "files.zip", zippedAttachments);

                    // count success or failure
                    _ = response.IsSuccessStatusCode ? sentEmailsCount++ : failedEmailsCount++;

                    // log email message
                    email.EmailMessages.Add(new EmailMessage()
                    {
                        CompanyId = company.Id,
                        UserId = emailRecipient.Id,
                        EmailAddress = caseEmail.RecipientEmail,
                        Subject = caseEmail.Subject,
                        EmailMessageStatusType = response.IsSuccessStatusCode ? EmailMessageStatusType.Sent : EmailMessageStatusType.Failed,
                        Timestamp = DateTime.UtcNow
                    });
                }
            }

            await _caseService.PublishPendingCasesAsync();

            await _emailLogService.LogOutcome(email, failedEmailsCount == 0 ? EmailStatusType.Completed : EmailStatusType.CompletedWithFailedEmails, sentEmailsCount, failedEmailsCount);
        }
        catch
        {
            await _emailLogService.LogOutcome(email, EmailStatusType.Failed, sentEmailsCount, failedEmailsCount);
            throw;
        }
    }

    private static IReadOnlyCollection<EmailRecipientModel> GetEmailRecipients(CompanyEmailModel company)
    {
        return company.CompanyUsers
            .Where(companyUser => companyUser.Active && companyUser.EmailPreferenceIds.Contains((int)EmailType.DailyCaseFilesEmail))
            .Select(companyUser => new EmailRecipientModel(companyUser.Id, companyUser.DisplayFullName, companyUser.Email))
            .ToList();
    }

    private CaseEmailModel GetCaseEmail(EmailRecipientModel emailRecipient, IReadOnlyCollection<CaseModel> cases, IReadOnlyCollection<CaseEmailSubstanceModel> substances)
    {
        var subject = $"{cases.Count} New Case{(cases.Count != 1 ? "s" : string.Empty)} ({DateTime.UtcNow:d MMM yyyy})";
        var viewCaseUri = new Uri(_websiteUriProvider.Provide(), "/Cases");

        var caseEmail = new CaseEmailModel(emailRecipient.Name, emailRecipient.Email, subject, substances, viewCaseUri);

        return caseEmail;
    }

    private async Task<IReadOnlyCollection<CaseEmailAttachmentModel>> GetCaseEmailAttachments(IEnumerable<CaseModel> cases)
    {
        var caseEmailAttachments = new List<CaseEmailAttachmentModel>();
        var totalBytes = 0L;

        foreach (var caseModel in cases.OrderBy(c => c.Id))
        {
            var tasks = caseModel.CaseFiles
                .Select(GetCaseEmailAttachment)
                .ToList();

            var attachments = await Task.WhenAll(tasks);

            totalBytes += attachments.Sum(attachment => attachment?.Stream.Length ?? 0);

            if (!_emailOptions.CaseEmailAttachmentMaxBytes.HasValue || totalBytes <= _emailOptions.CaseEmailAttachmentMaxBytes.Value)
            {
                caseEmailAttachments.AddRange(attachments.Where(attachment => attachment != null)!);
            }
            else
            {
                break;
            }
        }

        return caseEmailAttachments;
    }

    private async Task<CaseEmailAttachmentModel?> GetCaseEmailAttachment(CaseFileModel caseFile)
    {
        var caseDocumentDescriptor = new CaseDocumentDescriptor(caseFile.CaseId, caseFile.FileName);

        var exists = await _caseDocumentService.Exists(caseDocumentDescriptor);

        if (!exists)
        {
            _logger.LogWarning("Case File '{FileName}' does not exist for case {CaseId}.", caseFile.FileName, caseFile.CaseId);
            return null;
        }

        var caseFileStream = await _caseDocumentService.OpenRead(caseDocumentDescriptor);
        var filename = $"{caseFile.CaseId}\\{caseFile.FileName}";
        var caseEmailAttachment = new CaseEmailAttachmentModel(caseFile.CaseId, filename, caseFileStream);

        return caseEmailAttachment;
    }
}