﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.Domain.Interfaces.Repositories;

public interface IReferenceUpdateRepository : ITrackingRepository<ReferenceUpdate>
{
    Task<ReferenceUpdate?> GetById(int id);
    Task<IEnumerable<ReferenceUpdate>> GetUpdatesByReferenceId(int referenceId);
    Task<ReferenceUpdateModel?> GetForClassification(int referenceId, int substanceId);
    void ClearChangeTracker();
}