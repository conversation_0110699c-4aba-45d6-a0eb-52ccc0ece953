﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddScreeningTypeToContractsTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ScreeningType",
                table: "Contracts",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_FeatureFlags_Name",
                table: "FeatureFlags",
                column: "Name",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_FeatureFlags_Name",
                table: "FeatureFlags");

            migrationBuilder.DropColumn(
                name: "ScreeningType",
                table: "Contracts");
        }
    }
}
