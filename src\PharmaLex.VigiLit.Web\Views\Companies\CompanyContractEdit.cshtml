@using Microsoft.AspNetCore.Mvc.TagHelpers
@using PharmaLex.Core.Web.Helpers
@using PharmaLex.VigiLit.ContractManagement.Enums
@using PharmaLex.VigiLit.Domain.Enums
@using PharmaLex.VigiLit.Domain.Interfaces.Services
@using Microsoft.FeatureManagement
@inject IProjectService ProjectService;
@inject IFeatureManager FeatureManager;

@model PharmaLex.VigiLit.Ui.ViewModels.ContractManagement.ContractModel

@{
    ViewData["Title"] = "Company Contracts";
    var Substances = ViewBag.Substances;

    const string enableQueryStringValidation = "EnableContractQueryStringValidation";
    var isQueryStringValidationFlagEnabled = await FeatureManager.IsEnabledAsync(enableQueryStringValidation);
}

<div id="edit-contract" v-cloak>
    <form method="post">
        @Html.AntiForgeryToken()
        <div class="sub-header">
            @if (Model.Id == default)
            {
                <h2>Create Contract</h2>
            }
            else
            {
                <h2>Edit Contract | <a href="@($"/Contracts/History/{Model.Id}")">View History</a></h2>
            }

            <div class="controls">

                @{
                    if (Model.ReadOnly)
                    {
                              <button class="button btn-default" v-on:click.stop.prevent="back()">Back</button>
                    }
                    else
                    {
                            @if (Model.Id == default)
                        {
                                <button :disabled="!saveEnabled" v-on:click.stop.prevent="saveContract('create')" class="button icon-button-save btn-default">Save</button>
                        }
                        else
                        {
                                <button :disabled="!saveEnabled || !hasChanged" v-on:click.stop.prevent="saveContract('edit')" class="button icon-button-save btn-default">Save</button>
                        }
                            <a class="button secondary icon-button-cancel btn-default" href=@($"/Companies/{Model.CompanyId}/Contracts")>Cancel</a>
                    }
                }

            </div>
        </div>

        <section class="flex flex-wrap flex-gap card-container">
            <div class="section-card expand">
                <h2>Details</h2>
                @Html.HiddenFor(m => m.CompanyId)
                @Html.HiddenFor(m => m.Id)
                <div class="form-group">
                    <label for="ProjectId">Project</label>
                    <div class="custom-select">
                        <select name="ProjectId" v-model="contract.projectId" id="ProjectId" disabled="@(Model.Id != 0)" required>
                            <option v-for="project in projects" v-bind:value="project.value">
                                {{project.text}}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="SubstanceId">Substance</label>
                    @if (Model.Id == 0)
                    {
                        <div class="custom-select">
                            <select name="SubstanceId" v-model="contract.substanceId" id="SubstanceId" required>
                                <option v-for="substance in substances" v-bind:value="substance.value">
                                    {{substance.text}}
                                </option>
                            </select>
                        </div>
                    }
                    else
                    {
                        <div class="custom-select">
                            <select asp-for="SubstanceId" asp-items="Substances" disabled>
                            </select>
                        </div>
                        @Html.HiddenFor(m => m.SubstanceId)
                    }
                </div>
                <div class="form-group">
                    <label for="ScreeningType">Screening Type</label>

                    @if (Model.ReadOnly)
                    {
                        <div class="custom-select">
                            <select asp-for="ContractVersionPending.ScreeningType" v-model.number="contractVersionPending.screeningType" disabled>
                                <option value="@((Int16)@ScreeningType.Global)">Global</option>
                                <option value="@((Int16)@ScreeningType.Local)">Local</option>
                            </select>
                        </div>
                    }
                    else
                    {
                        <div class="custom-select">
                            <select asp-for="ContractVersionPending.ScreeningType" v-model.number="contractVersionPending.screeningType" required v-on:change="handleScreeningTypeChange($event.target.value)">
                                <option value="@((Int16)@ScreeningType.Global)">Global</option>
                                <option value="@((Int16)@ScreeningType.Local)">Local</option>
                            </select>
                        </div>
                    }

                </div>
                <div v-show="isGlobal" class="form-group">
                    <label for="ContractType">Contract Type</label>

                    @if (Model.ReadOnly)
                    {
                        <select asp-for="ContractVersionPending.ContractType" v-model.number="contractVersionPending.contractType" disabled>
                            <option value="@((int)ContractType.Scheduled)">Scheduled</option>
                            <option value="@((int)ContractType.AdHocOnly)">Ad-Hoc Only</option>
                        </select>
                    }
                    else
                    {
                        <div class="custom-select">
                            <select asp-for="ContractVersionPending.ContractType" v-model.number="contractVersionPending.contractType" required v-on:change="handleContractTypeChange($event.target.value)">
                                <option value="@((int)ContractType.Scheduled)">Scheduled</option>
                                <option value="@((int)ContractType.AdHocOnly)">Ad-Hoc Only</option>
                            </select>
                        </div>
                    }
                </div>
                <div v-show="isGlobal" class="form-group" v-if="displayContractWeekday">
                    <label for="ContractWeekday">Contract Weekday</label>

                    @if (Model.ReadOnly)
                    {
                        <div class="custom-select">
                            <select asp-for="ContractVersionPending.ContractWeekday" v-model.number="contractVersionPending.contractWeekday" disabled>
                                <option value="1">Monday</option>
                                <option value="2">Tuesday</option>
                                <option value="3">Wednesday</option>
                                <option value="4">Thursday</option>
                                <option value="5">Friday</option>
                            </select>
                        </div>
                    }
                    else
                    {
                        <div class="custom-select">
                            <select asp-for="ContractVersionPending.ContractWeekday" v-model.number="contractVersionPending.contractWeekday" required>
                                <option value="1">Monday</option>
                                <option value="2">Tuesday</option>
                                <option value="3">Wednesday</option>
                                <option value="4">Thursday</option>
                                <option value="5">Friday</option>
                            </select>
                        </div>
                    }
                </div>
                <div v-show="isGlobal" class="form-group">
                    <label for="SearchPeriodDays">Search Period</label>

                    @if (Model.ReadOnly)
                    {
                        <div class="custom-select">
                            <select asp-for="ContractVersionPending.SearchPeriodDays" v-model.number="contractVersionPending.searchPeriodDays" disabled>
                                <option value="7">7 days</option>
                                <option value="56">56 days</option>
                                <option value="365">365 days</option>
                                <option value="0">Unlimited</option>
                            </select>
                        </div>
                    }
                    else
                    {
                        <div class="custom-select">
                            <select asp-for="ContractVersionPending.SearchPeriodDays" v-model.number="contractVersionPending.searchPeriodDays">
                                <option value="7">7 days</option>
                                <option value="56">56 days</option>
                                <option value="365">365 days</option>
                                <option value="0">Unlimited</option>
                            </select>
                        </div>
                    }
                </div>

                <div v-show="isLocal" class="form-group">
                    <label for="Country">Country</label>
                    @if (Model.ReadOnly)
                    {
                        <select v-model.number="contractVersionPending.country" v-on:change="handleCountryChange($event.target.value)">
                            <option v-for='country in countries' :value='country.id' disabled="">{{ country.name }}</option>
                        </select>
                    }
                    else
                    {
                        <select v-model.number="contractVersionPending.country" v-on:change="handleCountryChange($event.target.value)">
                            <option v-for='country in countries' :value='country.id'>{{ country.name }}</option>
                        </select>
                    }
                </div>

                <div v-show="isLocal" class="form-group">
                    <label for="Journals">Journal</label>
                        <multi-select :item-list="journals" :placeholder="'Select Journals'" style="width: 100%; margin-bottom: 50px;"
                                      :selections="contractVersionPending.journals"
                                      :enable-filter="true"
                                      :disabled="contract.readOnly">
                        </multi-select>
                </div>

                <div class="form-group">
                    <hr class="full-width-rule"/>
                </div>
                <div class="flex columns gapped">
                    <div class="flex flex-align-center justify-space-between">
                        @if (Model.ContractVersionCurrent != null && Model.ContractVersionCurrent.Version != default)
                        {
                            <span class="version-label">{{"V" + @Html.DisplayFor(m => m.ContractVersionCurrent.Version)}}</span>
                        }
                        <div class="flex flex-align-center">
                            <label class="switch-container switch-active-inactive">
                                @if (Model.ReadOnly)
                                {
                                    <input asp-for="ContractVersionPending.IsActive" v-model="contractVersionPending.isActive" type="checkbox" class="switch" disabled />
                                    <label for="ContractVersionPending_IsActive" class="switch" disabled></label>
                                }
                                else
                                {
                                    <input asp-for="ContractVersionPending.IsActive" v-model="contractVersionPending.isActive" type="checkbox" class="switch" aria-label="Active" />
                                    <label for="ContractVersionPending_IsActive" class="switch"></label>
                                }
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="section-card flex-grow-1">
                
                @* Read only text areas *@
                @if (Model.Id == default || Model.ContractVersionCurrent == null)
                {
                    // No previous search string, so no text area
                    <h2>Criteria</h2>
                }
                else if (Model.ContractVersionCurrent.ContractVersionStatus == 3)
                {
                    // Current readonly search string
                    @Html.HiddenFor(m => m.ContractVersionCurrent.Version)
                    <div class="form-group">
                        <label for="ContractStateCurrent_SearchString">Active Search String</label>
                        <textarea asp-for="ContractVersionCurrent.SearchString" rows="10" wrap="soft" readonly maxlength="4000"></textarea>
                    </div>
                    <input asp-for="ContractVersionCurrent.Id" type="hidden" />
                }

                @* Editable text areas *@
                @if (Model.ContractVersionPending != null)
                {
                    // Editable search string awaiting approval
                    <h2>There is a search string waiting approval!!!</h2>
                    <div class="form-group">
                        <label asp-for="ContractVersionPending.ContractVersionStatus">Current Status</label>
                        <input asp-for="ContractVersionPending.ContractVersionStatus" type="number" />
                        <label>Search String</label>
                        <textarea asp-for="ContractVersionPending.SearchString" rows="10" wrap="soft" readonly="@(Model.ContractVersionPending.ContractVersionStatus != 0)" required maxlength="4000"></textarea>
                    </div>
                    <input asp-for="ContractVersionPending.Id" type="hidden" />
                }
                else
                {
                    // Editable new search string
                    @if (!Model.ReadOnly)
                    {
                        <div class="form-group">
                            <label asp-for="ContractVersionPending.SearchString">New Search String</label>
                            <textarea v-if="!showSearchStringErrors" asp-for="ContractVersionPending.SearchString" v-model.trim="searchString" rows="10" wrap="soft" placeholder="Input your new search string here." maxlength="4000"></textarea>

                            <div v-if="showSearchStringErrors" v-html="highlightErrors" class="search-error-box" @@click="showSearchStringErrors = false"></div>
                        </div>

                        <ul class="error-message">
                            <li v-for="error in searchStringErrors">
                                {{error.errorMessage}}
                            </li>
                        </ul>
                    }
                }
            </div>
            <modal-dialog v-if="showSignatureDialog"
                          :title="confirmTitle"
                          :btntxt="confirmSignTxt"
                          :disabled="!hasReasonForChange"
                          width="400px"
                          height="150px"
                          v-on:close="clearDialog"
                          v-on:confirm="signInAndSaveContract">
                <p>{{reasonForChangeMsgTxt}}</p>
                <label for="reasonForChange">Reason</label>
                <textarea id="reasonForChange" type="text" v-model.trim="contractVersionPending.reasonForChange" maxlength="256"></textarea>
                <p class="mt-5">{{signConfirmMsgTxt}}</p>
            </modal-dialog>
        </section>
    </form>
</div>

@section Scripts {
    <script type="text/javascript">
        let model = @Html.Raw(AntiXss.ToJson(Model));
        const token = document.getElementsByName("__RequestVerificationToken")[0].value;
        var pageConfig = {
            appElement: '#edit-contract',
            data: function () {
                return {
                    contract: model,
                    contractVersionPending: {
                        journals: { selectedIds: [] }
                    },
                    searchString: '',
                    searchStringErrors: [],
                    showSearchStringErrors: false,
                    showSignatureDialog: false,
                    confirmTitle: '',
                    confirmSignTxt: 'Sign',
                    reasonOfChangeMsgTxt: '',
                    signConfirmMsgTxt: '',
                    mode: '',
                    readOnly: '',
                    redirectUrl: model.refererUrl,
                    projects: @Html.Raw(AntiXss.ToJson(@ViewBag.Projects)),
                    substances: @Html.Raw(AntiXss.ToJson(@ViewBag.Substances)),
                    countries: [],
                    journals: []
                };
            },
            methods: {
                handleContractTypeChange(value) {
                    if (value == 20) {
                        this.contractVersionPending.contractWeekday = 0;
                    }
                },
                handleScreeningTypeChange(value) {
                    if (this.isLocal && this.countries.length == 0) {
                        this.getCountryList()
                    }
                },
                saveContract(mode) {
                    // Reauthenticate/Sign and save changes
                    // Since Updating only fields other then search string creates empty search strings hence need to copy it

                    if ((this.searchString == "" || this.searchString == undefined)
                        && mode == "edit") {
                        this.contractVersionPending.searchString = this.contract.contractVersionCurrent.searchString;
                    } else {
                        this.contractVersionPending.searchString = this.searchString;
                    }

                    let thisGlobal = this;

                    @if (isQueryStringValidationFlagEnabled)
                    {
                        <text>
                                let globalThis = this;

                                this.searchStringErrors.length = 0;

                                let myPromise = new Promise(function (resolve, reject){

                                        fetch(`/Companies/ValidateSearchString`, {
                                            method: "POST",
                                            credentials: 'same-origin',
                                            body: JSON.stringify(thisGlobal.contractVersionPending.searchString),
                                            headers: {
                                            "Content-Type": "application/json",
                                            "RequestVerificationToken": token
                                        },
                                    })
                                    .then(response => {
                                        if (!response.ok) {
                                            throw response;
                                        }
                                                
                                        return response.json();
                                    })
                                    .then(data => {
                                        message = data;

                                        if (data.length> 0){
                                            globalThis.showSearchStringErrors = true;
                                        }

                                        if (data == "") {
                                            resolve("");
                                        }
                                        else{
                                            reject(data);
                                        }

                                        return data;
                                    });
                                })
                                .then(

                                    function (value) {
                                        thisGlobal.InitiateSignatureProcessing(thisGlobal, mode);
                                    },

                                    function (error) {
                                        thisGlobal.searchStringErrors = error;
                                    }
                                );
                        </text>
                    }
                    else
                    {
                        @:this.InitiateSignatureProcessing(thisGlobal, mode);
                    }
                },
                InitiateSignatureProcessing(thisGlobal, mode) {
                    thisGlobal.contract.contractVersionPending = thisGlobal.contractVersionPending;
                    thisGlobal.setTextByMode(mode);
                    thisGlobal.showSignatureDialog = true;
                    thisGlobal.contract.saveMode = mode;
                },
                signInAndSaveContract() {

                    if (this.isLocal) {
                        this.setLocalLitDefaults();
                    }

                    fetch(`/Companies/SaveContractInSession`, {
                        method: "POST",
                        credentials: 'same-origin',
                        body: JSON.stringify(this.contract),
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        },
                    }).then(result => {
                        if (result.ok) {
                            location.href = "/Companies/ReAuthenticate";
                        }
                        else {
                            plx.toast.show('There was a problem saving contract. Please try again.', 2, 'failed', null, 5000)
                            console.log(error);
                        }

                    })
                },
                back() {
                    location.href = this.redirectUrl;
                },
                clearDialog() {
                    this.contractVersionPending.reasonForChange = "";
                    this.showSignatureDialog = false;
                },
                setTextByMode(mode) {
                    if (mode === 'create') {
                        this.confirmTitle = 'Confirm your add';
                        this.reasonForChangeMsgTxt = 'You are adding search strings to this Contract. Please give the reason below.';
                        this.signConfirmMsgTxt = 'You are about to add search string. When you are ready, select the Sign button and you will be redirected to a new page where you will be asked to re-enter your password to confirm your identity.';
                    }
                    else if (mode === 'edit') {
                        this.confirmTitle = 'Confirm your update';
                        this.reasonForChangeMsgTxt = 'You are making update to this Contract. Please give the reason below.';
                        this.signConfirmMsgTxt = 'You are about to change search string. When you are ready, select the Sign button and you will be redirected to a new page where you will be asked to re-enter your password to confirm your identity.';
                    }
                },
                getCountryList() {
                    fetch(`/Journals/GetSubscribedCountries`, {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((data) => {
                        this.countries = data;
                    }).catch(error => {
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
                },
                handleCountryChange(value) {
                    fetch(`/Journals/JournalsForCountry/${value}`, {
                        method: "GET",
                        credentials: 'same-origin',
                        headers: {
                            "Content-Type": "application/json",
                            "RequestVerificationToken": token
                        }
                    }).then(res => {
                        if (!res.ok) {
                            throw res;
                        }
                        return res.json();
                    }).then((data) => {
                        this.journals = data;
                        this.updateSelectionsFromLoading();
                    }).catch(error => {
                        plx.toast.show('Something went wrong, please try again', 2, 'failed', null, 2500);
                    });
                },
                updateSelectionsFromLoading() {
                    if (this.journalPreviouslyLoaded()) {
                        this.contractVersionPending.journals.selectedIds = [...this.contract.contractVersionCurrent.journals.selectedIds];
                    }
                    else{
                        this.contractVersionPending.journals.selectedIds = [];
                    }
                },
                journalPreviouslyLoaded() {
                    let journalIds = this.journals.map(x => x.id);
                    return this.contract.contractVersionCurrent.journals && this.contract.contractVersionCurrent.journals.selectedIds.every(r => journalIds.includes(r));
                },
                setLocalLitDefaults(){
                    this.contractVersionPending.contractType = 10;
                },
                insertAt(str, index, value) {
                    return str.substr(0, index) + value + str.substr(index);
                }
            },
            computed: {
                saveEnabled() {
                    var isEnabled = this.contract.projectId > 0
                        && this.contract.substanceId > 0
                        && (this.isValidGlobalSave || this.isValidLocalSave);

                    // Is edit
                    if (this.contract.id != 0) {
                        return isEnabled
                    }
                    // Is create then also check if has valid search string
                    else {
                        return isEnabled && (this.contractVersionPending != undefined && this.searchString != "" && this.searchString != undefined);
                    }
                },
                isValidGlobalSave(){
                    return this.isGlobal && (this.contractVersionPending.contractType == 10 && this.contractVersionPending.contractWeekday > 0)
                        || (this.contractVersionPending.contractType == 20 && this.contractVersionPending.contractWeekday == 0)
                },
                isValidLocalSave() {
                    return this.isLocal && this.contractVersionPending.country > 0 && this.contractVersionPending.journals.selectedIds.length > 0;
                },
                hasReasonForChange() {
                    if (this.contractVersionPending.reasonForChange != "" && this.contractVersionPending.reasonForChange != undefined) {
                        return true;
                    } else {
                        return false;
                    }
                },
                displayContractWeekday() {
                    return this.contractVersionPending.contractType == 10;
                },
                highlightErrors() {
                    if (this.searchStringErrors){
                            let output = this.searchString;
                            let currLength = output.length;
                            let added = 0;
                            let openHighlight = '<span style="background-color:yellow">';
                            let closeHighlight = '</span>';

                            this.searchStringErrors.forEach(err => {
                                currLength = output.length;
                                output = this.insertAt(output, err.start + added, openHighlight);
                                output = this.insertAt(output, err.end + openHighlight.length + added, closeHighlight);
                                added = added + output.length - currLength;
                            });

                            return output;
                    }
                },
                // Additional Check to enable save button only if any inputs are edited
                hasChanged() {
                    return this.searchString != "" || Object.keys(this.contractVersionPending).some(field => this.contractVersionPending[field] !== this.contract.contractVersionCurrent[field]);
                },
                isLocal() {
                    // local contracts are for local literature
                    return this.contractVersionPending.screeningType == @((Int16)ScreeningType.Local);
                },
                isGlobal() {
                    // global contracts are PubMed
                    return this.contractVersionPending.screeningType == @((Int16)@ScreeningType.Global);
                }
            },
            created() {

                if (model.id !== 0) {
                    this.contractVersionPending.contractType = this.contract.contractVersionCurrent.contractType;
                    this.contractVersionPending.contractWeekday = this.contract.contractVersionCurrent.contractWeekday;
                    this.contractVersionPending.searchPeriodDays = this.contract.contractVersionCurrent.searchPeriodDays;
                    this.contractVersionPending.isActive = this.contract.contractVersionCurrent.isActive;
                    this.contractVersionPending.screeningType = this.contract.contractVersionCurrent.screeningType;
                    this.contractVersionPending.country = this.contract.contractVersionCurrent.country;
                    this.journals = [];

                    if (!this.isLocal && !this.isGlobal) {
                        this.contractVersionPending.screeningType = @((Int16)@ScreeningType.Global);
                    }

                    if (this.isLocal) {
                        this.getCountryList();

                        if (this.contractVersionPending.country != 0) {
                            this.handleCountryChange(this.contractVersionPending.country);
                        }

                        if (!this.contractVersionPending.journals.selectedIds) {
                            this.contractVersionPending.journals.selectedIds = [];
                        }
                        else {
                            this.contractVersionPending.journals.selectedIds = [...this.contract.contractVersionCurrent.journals.selectedIds];
                        }
                    }
                }
            }
        }
    </script>
}
@section VueComponentScripts{
    <partial name="Components/ModalDialog" />
    <partial name="Components/MultiSelect" />
}
