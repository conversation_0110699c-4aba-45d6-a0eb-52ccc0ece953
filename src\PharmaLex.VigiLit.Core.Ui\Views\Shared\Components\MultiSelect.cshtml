<script type="text/x-template" id="multi-select-template">
    <div>
        <div :id="uid" class="multi-select multi-select-container" :class="{ active: isOpen }">

            <div class="preview-box" @@click.stop="" >

                <input ref="filter" v-show="enableFilter && filterShow" @@click.stop="" :id="uniqueId('')" type="search" class="white-background filter-multiselect" placeholder="Enter filter" aria-label="Enter filter" v-model='filterText' />
                <label v-if="!this.filterShow" class="selected-label">{{ selectedItemsText }}</label>
                <div class="selected-count">{{ this.selections.selectedIds.length }}</div>
            </div>
            <div class="item-container" :class="[isOpen ? '' : 'hidden']">
                <div class="multi-select-item" v-for="item in filteredItems(filterText)" :key="item" :class="{ 'disabled-item': item.enabled === false }">
                    <label class="formatLabel">
                        <input type="checkbox"
                               :id="uniqueId(item.id)"
                               :value="item.id"
                               v-model="selections.selectedIds"
                               :disabled="disabled || item.enabled === false">
                    </label>
                    <label class="multiSelectItemLabel" :for="uniqueId(item.id)">{{ item["name"] }}</label>
                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
    vueApp.component('multi-select', {
        template: '#multi-select-template',
        props: {
            itemList: {
                type: Array
            },
            placeholder: {
                type: String,
                default: "Select items"
            },
            selections: {
                type: Object,
                default: { selectedIds:[] }
            },
            enableFilter: {
                type: Boolean,
                default: false
            },
            disabled: {
                type: Boolean,
                default: false
            }
        },
        data: function() {
            return {
                uid: null,
                isOpen: false,
                filterText: '',
                filterShow: false
            }
        },
        methods: {
            // This component generates a random uid on load. This is combined with the intended id to ensure no cross-component contamination.
            uniqueId: function(id) {
                return `${this.uid}-${id}`;
            },
            // Returns JQuery only within this component, so you can have multiple multi-selects on a page.
            query: function (query) {
                return $(this.$el).find(query).first();
            },
            toggleOpen: function(shouldOpen) {
                this.isOpen = (shouldOpen === undefined) ? !this.isOpen : shouldOpen;
                this.toggleFilter(this.isOpen);
            },
            toggleFilter: function (show) {

                if (this.enableFilter) {
                    this.filterShow = show;

                    if (this.$refs.filter && show) {
                        this.$nextTick(() => { this.$refs.filter.focus(); })
                    }
                }
            },
            filteredItems: function(filter) {
                return this.itemList.filter(function (data) { return data.name.toLowerCase().includes(filter.toLowerCase()) });
            }
        },
        computed: {
            selectedItemsText() {
                let selectedItems = this.itemList.filter(item => { return this.selections.selectedIds.includes(item.id) });

                if (this.selections.selectedIds.length == 0) {
                    return this.placeholder
                } else {
                    var x = selectedItems.map(function (i) {
                        return i["name"];
                    });
                    return x.join(', ');
                }
            }
        },
        mounted() {
            this.uid = Math.random().toString(16).slice(2);

            // need the following click events to reference this object
            var vm = this;

            // toggle when clicked
            this.query('.preview-box').click(function () {
                vm.toggleOpen();
            });

            // close when clicked off
            $(document).click(function (event) {
                var target = vm.query(event.target);
                if (!target.closest('.multi-select-container').length) {
                    vm.toggleOpen(false);
                }
            });
        }
    });
</script>
