image:
  repository: phlexglobal.azurecr.io/vigilit-ai
  pullPolicy: Always

replicas: 2
minAvailability: 1

keyVaultName: "vgt-prod-kv-eun"

azureWorkload:
  clientId: 072f61f9-ae0f-45e4-b4df-885a722a6404 #aksshared-sharedprod-aks-eun-sp-vigilit-ai-prod

serviceBus: "vgt-prod-servicebus-eun"
newRelicAppName: "vgt-prod-ai-eun"

AiEndpointSettingsUri: "https://vigilit-ai.smartphlex.com/latest/process"
