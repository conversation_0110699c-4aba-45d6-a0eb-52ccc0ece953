using AutoMapper;
using System.Linq;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.ContractManagement;
using PharmaLex.VigiLit.Ui.ViewModels.UserManagement;

namespace PharmaLex.VigiLit.Application.AutoMapper;

public class CompanyMappingProfile : Profile
{
    public CompanyMappingProfile()
    {
        CreateMap<Company, CompanyModel>();

        CreateMap<Company, CompanyEmailModel>()
        .ForMember(d => d.CompanyUsers, s => s.MapFrom(c => c.CompanyUsers.Where(cu => cu.Active)));

        CreateMap<Company, CompanyWithContractsModel>()
            .ForMember(d => d.Contracts, s => s.MapFrom(c => c.Projects.SelectMany(p => p.ContractsInternal).OrderBy(c => c.Project.Name).ThenBy(c => c.Substance.Name)));

        CreateMap<Company, CompanyWithProjectsModel>()
            .ForMember(d => d.Projects, s => s.MapFrom(c => c.Projects.OrderBy(p => p.Name)));

        CreateMap<Company, CompanyWithUsersModel>()
            .ForMember(d => d.CompanyUsers, s => s.MapFrom(c => c.CompanyUsers.Select(u => u.User).OrderBy(u => u.FamilyName).ThenBy(u => u.GivenName)));

        CreateMap<Company, CompanyItemModel>();

        CreateMap<Company, CompanyUserListModel>();
    }
}
