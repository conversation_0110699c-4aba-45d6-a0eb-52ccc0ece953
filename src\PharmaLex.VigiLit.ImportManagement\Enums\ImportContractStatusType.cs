﻿using System.ComponentModel;

namespace PharmaLex.VigiLit.ImportManagement.Enums;

public enum ImportContractStatusType
{
    [Description("")]
    None = 0,

    [Description("Queued")]
    Queued = 10,

    [Description("Started")]
    Started = 15,

    [Description("Completed")]
    Completed = 20,

    [Description("Completed (Capped)")]
    CompletedCapped = 21,

    [Description("Failed")]
    Failed = 30,

    [Description("Search Failed")]
    SearchFailed = 31,

    [Description("Timeout")]
    Timeout = 40,

    [Description("Invalid Search String")]
    InvalidSearchString = 50,
}
