﻿using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using SendGrid;
using SendGrid.Helpers.Mail;
using System.Net;

namespace PharmaLex.Core.HealthCheck;

public class MailProviderHealthCheck : IHealthCheck
{
    private const string _subject = "This is the test subject.";
    private const string _plainTextContent = "This is the test message.";
    private const string _address = "<EMAIL>";
    private const string _recipient = "First Middle Last";

    private readonly string _apiKey;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ILogger<MailProviderHealthCheck> _logger;

    public MailProviderHealthCheck(string apiKey, IHttpClientFactory httpClientFactory, ILoggerFactory loggerFactory)
    {
        _apiKey = apiKey;
        _httpClientFactory = httpClientFactory;
        _logger = loggerFactory.CreateLogger<MailProviderHealthCheck>();
    }

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
    {
        bool isHealthy;

        try
        {
            isHealthy = await IsReachableAsync(cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Exception occurred while validating mail provider health.");
            isHealthy = false;
        }

        return await Task.FromResult(isHealthy ? HealthCheckResult.Healthy() : HealthCheckResult.Unhealthy());
    }

    private async Task<bool> IsReachableAsync(CancellationToken cancellationToken = default)
    {
        var httpClient = _httpClientFactory.CreateClient();
        var client = new SendGridClient(httpClient, _apiKey);
        var from = new EmailAddress(_address, _recipient);
        var to = new EmailAddress(_address, _recipient);
        var msg = MailHelper.CreateSingleEmail(from, to, _subject, _plainTextContent, null);
        msg.SetSandBoxMode(true);

        var response = await client.SendEmailAsync(msg, cancellationToken).ConfigureAwait(false);

        return response.StatusCode == HttpStatusCode.OK;
    }
}