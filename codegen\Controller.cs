using System.Collections.Generic;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.AspNetCore.Mvc;
{{#if Dependencies}}{{#each Dependencies}}using {{this}};
{{/each}}{{/if}}
namespace {{Namespace}}
{
    [Route("[controller]")]
    public class {{EntityNamePlural}}Controller: Controller
    {
        private readonly I{{EntityName}}Service _{{EntityNameCamelCase}}Service;
        private readonly IMapper _mapper;

        public {{EntityNamePlural}}Controller(I{{EntityName}}Service {{EntityNameCamelCase}}Service, IMapper mapper)
        {
            _{{EntityNameCamelCase}}Service = {{EntityNameCamelCase}}Service;
            _mapper = mapper;
        }
        
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var items = await _{{EntityNameCamelCase}}Service.GetAllAsync();
            return View(items);
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> History(int id)
        {
            var items = await _{{EntityNameCamelCase}}Service.GetHistory(id);
            return View(items);
        }

        [HttpGet("[action]")]
        public IActionResult Create()
        {
            return View("Edit", new {{EntityName}}Model());
        }

        [HttpPost("[action]")]
        public async Task<IActionResult> Create({{EntityName}}Model model)
        {
            if (ModelState.IsValid)
            {
                await _{{EntityNameCamelCase}}Service.AddAsync(model);

                return RedirectToAction("Index");
            }

            return View("Edit", model);
        }

        [HttpGet("[action]/{id}")]
        public async Task<IActionResult> Edit(int id)
        {
            var {{EntityNameCamelCase}} = await _{{EntityNameCamelCase}}Service.GetByIdAsync(id);
            
            return View({{EntityNameCamelCase}});
        }

        [HttpPost("[action]/{id}")]
        public async Task<IActionResult> Edit({{EntityName}}Model model)
        {
            if (ModelState.IsValid)
            {
                await _{{EntityNameCamelCase}}Service.UpdateAsync(model);

                return RedirectToAction("Index");
            }

            return View(model);
        }

        [HttpDelete("/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _{{EntityNameCamelCase}}Service.DeleteAsync(id);
            return Ok();
        }
    }
}
