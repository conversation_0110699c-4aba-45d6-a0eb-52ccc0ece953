﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.DataExtraction.Entities;

namespace PharmaLex.VigiLit.DataExtraction.Service.Interfaces;

public interface IMdeQueueItemRepository : ITrackingRepository<MdeQueueItem>
{
    /// <summary>
    /// Gets the queue item by the correlation identifier.
    /// </summary>
    /// <param name="correlationId">The correlation identifier.</param>
    /// <returns></returns>
    Task<MdeQueueItem?> GetByCorrelationId(Guid correlationId);

    Task<MdeQueueItem?> GetNextInQueue();
}