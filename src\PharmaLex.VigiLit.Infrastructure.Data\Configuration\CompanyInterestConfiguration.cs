﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Models;

namespace PharmaLex.VigiLit.Infrastructure.Data.Configuration;

public class CompanyInterestConfiguration : EntityBaseMap<CompanyInterest>
{
    public override void Configure(EntityTypeBuilder<CompanyInterest> builder)
    {
        base.Configure(builder);

        builder.ToTable("CompanyInterests");

        builder.<PERSON><PERSON><PERSON>(e => e.Id);

        builder.HasIndex(s => new { s.CompanyId, s.ReferenceClassificationId }).IsUnique();
        builder.HasIndex(s => new { s.CompanyId, s.ReferenceId });

        // daily email
        builder.HasIndex(s => new { s.CompanyId, s.Id })
            .IncludeProperties(p => new
            {
                p.ReferenceId,
                p.ReferenceClassificationId,
                p.CreatedDate,
                p.CreatedBy,
                p.LastUpdatedDate,
                p.LastUpdatedBy
            });

        builder.HasOne(ic => ic.Company)
            .WithMany()
            .HasForeignKey(ic => ic.CompanyId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(ic => ic.Reference)
            .WithMany()
            .HasForeignKey(ic => ic.ReferenceId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();

        builder.HasOne(ic => ic.ReferenceClassification)
            .WithMany()
            .HasForeignKey(ic => ic.ReferenceClassificationId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired();
    }
}