﻿using PharmaLex.DataAccess;
using PharmaLex.VigiLit.ImportManagement.Ui.Services;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Generics;

public interface IGenericCardService<TCardEntity, TViewModel> : ICardRetriever
    where TCardEntity : EntityBase
    where TViewModel : IViewModel
{
    Task Add(TViewModel model);
    Task<TViewModel> Get(int id);
    Task Update(TViewModel model);
    Task Abandon(int id);
    Task Enqueue(int id);
    Task<IEnumerable<TCardEntity>> GetAll();
}