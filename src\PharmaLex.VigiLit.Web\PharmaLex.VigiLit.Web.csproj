﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<IncludeOpenAPIAnalyzers>true</IncludeOpenAPIAnalyzers>
		<GenerateAssemblyFileVersionAttribute>false</GenerateAssemblyFileVersionAttribute>
		<GenerateAssemblyInfo>true</GenerateAssemblyInfo>
	</PropertyGroup>

	<PropertyGroup>
		<ErrorOnDuplicatePublishOutputFiles>false</ErrorOnDuplicatePublishOutputFiles>
	</PropertyGroup>

	<ItemGroup>
	  <None Remove="250463.pdf" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="250463.pdf">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Include="public\double-ring.gif" />
		<EmbeddedResource Include="wwwroot\images\loaders\dashes.gif" />
		<EmbeddedResource Include="wwwroot\images\loaders\double-ring.gif" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AutoMapper" Version="14.0.0" />
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.4.0" />
		<PackageReference Include="HtmlSanitizer" Version="9.0.886" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.DataProtection.Abstractions" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.10" />
		<PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="8.0.10" />
		<PackageReference Include="Microsoft.Build.Tasks.Core" Version="17.14.8" />
		<PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.CSharp.Features" Version="4.14.0" />
		<PackageReference Include="Microsoft.CodeAnalysis.Workspaces.MSBuild" Version="4.14.0" />
		<PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.7">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Azure" Version="1.12.0" />
		<PackageReference Include="Microsoft.Extensions.FileProviders.Embedded" Version="8.0.10" />
		<PackageReference Include="Microsoft.Extensions.FileProviders.Physical" Version="9.0.7" />
		<PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" Version="2.23.0" />
		<PackageReference Include="Microsoft.Graph" Version="5.87.0" />
		<PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
		<PackageReference Include="NewRelic.Agent" Version="10.43.0" />
		<PackageReference Include="PharmaLex.Authentication.B2C" Version="8.0.0.212" />
		<PackageReference Include="PharmaLex.FeatureManagement" Version="8.0.0.110" />
		<PackageReference Include="PharmaLex.FeatureManagement.Entities" Version="8.0.0.110" />
		<PackageReference Include="PharmaLex.Helpers" Version="8.0.0.129" />
		<PackageReference Include="PharmaLex.BlobStorage" Version="8.0.0.126" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\PharmaLex.Core.Configuration\PharmaLex.Core.Configuration.csproj" />
		<ProjectReference Include="..\PharmaLex.Core.HealthCheck\PharmaLex.Core.HealthCheck.csproj" />
		<ProjectReference Include="..\PharmaLex.Core.Web\PharmaLex.Core.Web.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Application.Services\PharmaLex.VigiLit.Application.Services.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Application\PharmaLex.VigiLit.Application.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Case\PharmaLex.VigiLit.Case.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.ContractManagement\PharmaLex.VigiLit.ContractManagement.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Core.Ui\PharmaLex.VigiLit.Core.Ui.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Data.Repositories\PharmaLex.VigiLit.Data.Repositories.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.DataExtraction.Service\PharmaLex.VigiLit.DataExtraction.Service.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Domain\PharmaLex.VigiLit.Domain.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.ImportManagement.Service\PharmaLex.VigiLit.ImportManagement.Service.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Infrastructure.Data\PharmaLex.VigiLit.Infrastructure.Data.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Infrastructure.Import.PubMed\PharmaLex.VigiLit.Infrastructure.Import.PubMed.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Infrastructure\PharmaLex.VigiLit.Infrastructure.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Contracts\PharmaLex.VigiLit.Reporting.Contracts.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Reporting.Ui\PharmaLex.VigiLit.Reporting.Ui.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Reporting\PharmaLex.VigiLit.Reporting.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Reports.Accovion.Domain\PharmaLex.VigiLit.Reports.Accovion.Domain.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Reports.Apogepha.Domain\PharmaLex.VigiLit.Reports.Apogepha.Domain.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Reports.Merz.Domain\PharmaLex.VigiLit.Reports.Merz.Domain.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Reports.TrackingSheets.Domain\PharmaLex.VigiLit.Reports.TrackingSheets.Domain.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Scraping.Client\PharmaLex.VigiLit.Scraping.Client.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Scraping.Service\PharmaLex.VigiLit.Scraping.Service.csproj" />
		<ProjectReference Include="..\PharmaLex.VigiLit.Search.Ui\PharmaLex.VigiLit.Search.Ui.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="Views\Home\Anonymous.cshtml">
			<Pack>false</Pack>
		</Content>
		<Content Update="Views\Home\Error.cshtml">
		  <Pack>false</Pack>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Views\Help\Files\VigiLit User Manual V1.pdf">
		  <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="newrelic\" />
		<Folder Include="src\images\" />
		<Folder Include="Views\Help\Files\" />
		<Folder Include="Views\Shared\" />
		<Folder Include="wwwroot\lib\jsdiff\" />
	</ItemGroup>

	<ItemGroup>
	  <None Include="..\PharmaLex.VigiLit.Core.Ui\Views\Shared\_Layout.cshtml" Link="Views\Shared\_Layout.cshtml" />
	</ItemGroup>



	<PropertyGroup>
		<AddRazorSupportForMvc>true</AddRazorSupportForMvc>
		<OutputType>Exe</OutputType>
	</PropertyGroup>

	<PropertyGroup>
		<GenerateEmbeddedFilesManifest>true</GenerateEmbeddedFilesManifest>
		<UserSecretsId>PharmaLex.VigiLit</UserSecretsId>
	</PropertyGroup>

</Project>
