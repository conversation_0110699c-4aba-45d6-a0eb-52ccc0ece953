﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using PharmaLex.DataAccess;
using PharmaLex.VigiLit.Domain.Interfaces.Repositories;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Ui.ViewModels.EmailService;

namespace PharmaLex.VigiLit.Data.Repositories;

public class EmailSuppressionRepository : TrackingGenericRepository<EmailSuppression>, IEmailSuppressionRepository
{
    private readonly IMapper _mapper;

    public EmailSuppressionRepository(PlxDbContext context, IMapper mapper, IUserContext userContext) : base(context, userContext.User)
    {
        _mapper = mapper;
    }

    public async Task AddRangeAsync(List<EmailSuppression> emailSuppressions)
    {
        await context.Set<EmailSuppression>().AddRangeAsync(emailSuppressions);
        await SaveChangesAsync();
    }

    public async Task DeleteAll()
    {
        await context.Database.ExecuteSqlRawAsync("DELETE FROM EmailSuppression");
    }

    public async Task<EmailSuppressionModel?> GetUserSuppression(int userId)
    {
        var query = context.Set<EmailSuppression>()
            .Where(x => x.UserId == userId)
            .AsNoTracking();

        return await _mapper.ProjectTo<EmailSuppressionModel>(query).FirstOrDefaultAsync();
    }
}
