﻿namespace PharmaLex.Core.UserManagement.Users;

/// <summary>
///     Provides methods for finding users in Microsoft Graph.
/// </summary>
public interface IAzureAdGraphFindHelper
{
    /// <summary>
    ///     Finds users in Microsoft Graph based on the specified search term.
    /// </summary>
    /// <param name="term">The search term.</param>
    /// <returns>
    ///     A task that represents the asynchronous operation. The task result contains a list of user models matching the
    ///     search term.
    /// </returns>
    Task<List<UserFindResult>> Find(string term);
}