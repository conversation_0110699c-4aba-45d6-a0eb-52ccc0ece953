﻿using Microsoft.EntityFrameworkCore.Migrations;
using PharmaLex.VigiLit.Infrastructure.Data.Extensions;

#nullable disable

namespace PharmaLex.VigiLit.ImportApp.Data.Migrations
{
    public partial class AddSpGetAiSuggestedClassification : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.SqlFileExec("spGetAiSuggestedClassification_1.sql");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // VigiLit does not go back in versions
        }
    }
}
