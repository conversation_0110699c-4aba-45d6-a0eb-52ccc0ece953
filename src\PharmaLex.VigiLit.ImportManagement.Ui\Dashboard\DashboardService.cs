﻿using Microsoft.Extensions.Logging;
using PharmaLex.VigiLit.Domain;
using PharmaLex.VigiLit.Domain.Enums;
using PharmaLex.VigiLit.ImportManagement.Enums;
using PharmaLex.VigiLit.ImportManagement.Ui.Repositories;
using PharmaLex.VigiLit.Ui.ViewModels.ReferenceModule;

namespace PharmaLex.VigiLit.ImportManagement.Ui.Dashboard;

internal class DashboardService : IDashboardService
{
    private readonly ILogger<DashboardService> _logger;
    private readonly IImportRepository _importRepository;
    private readonly IImportContractReferenceClassificationRepository _importContractReferenceClassificationRepository;
    private readonly IImportSelectionRepository _importSelectionRepository;
    private readonly IReferenceClassificationLockRepository _referenceClassificationLockRepository;
    private readonly ICompanyRepository _companyRepository;
    private readonly IVigiLitUserContext _userContext;

    public DashboardService(
        ILoggerFactory loggerFactory,
        IImportRepository importRepository,
        IImportContractReferenceClassificationRepository importContractReferenceClassificationRepository,
        IImportSelectionRepository importSelectionRepository,
        IReferenceClassificationLockRepository referenceClassificationLockRepository,
        ICompanyRepository companyRepository,
        IVigiLitUserContext userContext)
    {
        _logger = loggerFactory.CreateLogger<DashboardService>();

        _importRepository = importRepository;
        _importContractReferenceClassificationRepository = importContractReferenceClassificationRepository;
        _importSelectionRepository = importSelectionRepository;
        _referenceClassificationLockRepository = referenceClassificationLockRepository;
        _companyRepository = companyRepository;

        _userContext = userContext;
    }

    public async Task<IEnumerable<ImportDashboardModel>> GetImportsForDashboard()
    {
        return await _importRepository.GetImportsForDashboard();
    }

    public async Task<DashboardDetailsModel> GetImportDashboardDetails(int importId, int page, int pageSize)
    {
        ValidatePageParameters(page, pageSize);

        var result = await _importContractReferenceClassificationRepository.GetImportDashboardDetailsRows(importId, page, pageSize);

        return new DashboardDetailsModel()
        {
#pragma warning disable CS8601
            Import = await _importRepository.GetImportForDashboardDetails(importId),
#pragma warning disable CS8601
            Rows = result.Rows,
            TotalRowCount = result.TotalRowCount,
            Page = page,
            PageSize = pageSize,
        };
    }

    private static void ValidatePageParameters(int page, int pageSize)
    {
        if (page < 1)
        {
            throw new ArgumentOutOfRangeException(nameof(page), "Page must be 1 or greater.");
        }

        if (pageSize < 25 || pageSize > 100)
        {
            throw new ArgumentOutOfRangeException(nameof(pageSize), "Page size must be between 25 and 100.");
        }
    }

    public async Task<IEnumerable<DashboardDetailsRowModel>> GetImportDashboardDetailsExport(int importId)
    {
        return await _importContractReferenceClassificationRepository.GetImportDashboardDetailsExport(importId);
    }

    public async Task Archive(int importId)
    {
        var import = await _importRepository.GetById(importId);

        if (import == null)
        {
            throw new ArgumentException(string.Format("Invalid Import Id. importId={0}", importId));
        }

        if (import.ImportDashboardStatusType != ImportDashboardStatusType.Signed)
        {
            throw new InvalidOperationException(string.Format("An import can only be archived if it is signed. importId={0}", importId));
        }

        await _importRepository.Archive(importId);
        await _importSelectionRepository.ClearSelections(importId);
    }

    public async Task Select(int importId)
    {
        var import = await _importRepository.GetById(importId);

        if (import == null)
        {
            throw new ArgumentException(string.Format("Invalid Import Id. importId={0}", importId));
        }

        if (!DashboardStatusTypeGroups.Active.Contains(import.ImportDashboardStatusType))
        {
            throw new InvalidOperationException(string.Format("An import can only be selected if it is active. importId={0}", importId));
        }

        var unlockingResult = await _referenceClassificationLockRepository.Unlock(_userContext.UserId);

        await _importSelectionRepository.Select(_userContext.UserId, importId);

        var unlockedClassificationIds = unlockingResult.ClassificationIds.Count != 0 ? string.Join(",", unlockingResult.ClassificationIds) : "none";

        _logger.LogInformation("Select: User {UserId} selected import {ImportId}. Locks cleared unlockedClassificationIds={UnlockedClassificationIds}",
            _userContext.UserId, importId, unlockedClassificationIds);
    }

    public async Task Deselect(int importId)
    {
        var import = await _importRepository.GetById(importId);

        if (import == null)
        {
            throw new ArgumentException(string.Format("Invalid Import Id. importId={0}", importId));
        }

        if (!DashboardStatusTypeGroups.Active.Contains(import.ImportDashboardStatusType))
        {
            throw new InvalidOperationException(string.Format("An import can only be deselected if it is active. importId={0}", importId));
        }

        var unlockingResult = await _referenceClassificationLockRepository.Unlock(_userContext.UserId);

        await _importSelectionRepository.Deselect(_userContext.UserId, importId);

        var unlockedClassificationIds = unlockingResult.ClassificationIds.Count != 0 ? string.Join(",", unlockingResult.ClassificationIds) : "none";

        _logger.LogInformation("Deselect: User {UserId} deselecting import {ImportId}. Locks cleared unlockedClassificationIds={UnlockedClassificationIds}",
            _userContext.UserId, importId, unlockedClassificationIds);
    }

    public async Task<ImportSelectionModel?> GetSelectedImport()
    {
        return await _importSelectionRepository.GetSelectedImport(_userContext.UserId);
    }

    public async Task<DashboardModel> GetCompanyDashboardStats()
    {
        var companySummary = new List<DashboardCompanySummary>();

        var companySuppressions = await _companyRepository.GetAllCompaniesWithEmailSuppressions();

        foreach (var company in companySuppressions)
        {
            companySummary.Add(
                new DashboardCompanySummary
                {
                    Id = company.Id,
                    CompanyName = company.Name,
                    SuppressionSummary = new CompanyEmailStats
                    {
                        BounceCount = company.CompanyUsers.Where(cu => cu.User.EmailSuppression != null).Count(cu => cu.User.EmailSuppression.EmailSuppressionType == EmailSuppressionType.Bounce),
                        BlockCount = company.CompanyUsers.Where(cu => cu.User.EmailSuppression != null).Count(cu => cu.User.EmailSuppression.EmailSuppressionType == EmailSuppressionType.Block),
                        SpamCount = company.CompanyUsers.Where(cu => cu.User.EmailSuppression != null).Count(cu => cu.User.EmailSuppression.EmailSuppressionType == EmailSuppressionType.Spam)
                    }
                });
        }

        return new DashboardModel
        {
            CompanySummary = companySummary,
            Total = new CompanyEmailStats
            {
                BounceCount = companySummary.Sum(s => s.SuppressionSummary.BounceCount),
                BlockCount = companySummary.Sum(s => s.SuppressionSummary.BlockCount),
                SpamCount = companySummary.Sum(s => s.SuppressionSummary.SpamCount),
            }
        };
    }
}